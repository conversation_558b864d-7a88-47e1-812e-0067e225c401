
.comfy-menu-hamburger[data-v-4c2fbe7d] {
    position: fixed;
    z-index: 9999;
    display: flex;
    flex-direction: row
}

[data-v-1b9dea63] .p-splitter-gutter {
  pointer-events: auto;
}
[data-v-1b9dea63] .p-splitter-gutter:hover,[data-v-1b9dea63] .p-splitter-gutter[data-p-gutter-resizing='true'] {
  transition: background-color 0.2s ease 300ms;
  background-color: var(--p-primary-color);
}
.side-bar-panel[data-v-1b9dea63] {
  background-color: var(--bg-color);
  pointer-events: auto;
}
.bottom-panel[data-v-1b9dea63] {
  background-color: var(--bg-color);
  pointer-events: auto;
}
.splitter-overlay[data-v-1b9dea63] {
  pointer-events: none;
  border-style: none;
  background-color: transparent;
}
.splitter-overlay-root[data-v-1b9dea63] {
  position: absolute;
  top: 0px;
  left: 0px;
  height: 100%;
  width: 100%;

  /* Set it the same as the ComfyUI menu */
  /* Note: Lite-graph DOM widgets have the same z-index as the node id, so
  999 should be sufficient to make sure splitter overlays on node's DOM
  widgets */
  z-index: 999;
}

.dom-widget[data-v-b10bc68b] > * {
    height: 100%;
    width: 100%
}

.p-buttongroup-vertical[data-v-27a9500c] {
  display: flex;
  flex-direction: column;
  border-radius: var(--p-button-border-radius);
  overflow: hidden;
  border: 1px solid var(--p-panel-border-color);
}
.p-buttongroup-vertical .p-button[data-v-27a9500c] {
  margin: 0;
  border-radius: 0;
}

.node-tooltip[data-v-98099942] {
  background: var(--comfy-input-bg);
  border-radius: 5px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.4);
  color: var(--input-text);
  font-family: sans-serif;
  left: 0;
  max-width: 30vw;
  padding: 4px 8px;
  position: absolute;
  top: 0;
  transform: translate(5px, calc(-100% - 5px));
  white-space: pre-wrap;
  z-index: 99999;
}

.selection-overlay-container[data-v-1dfc1857] > * {
  pointer-events: auto;
}
.show-border[data-v-1dfc1857] {
  border-radius: 0.375rem;
  border-width: 2px;
  border-style: dashed;
  border-color: var(--border-color);
}

.color-picker-container[data-v-82fd78df] {
  transform: translateX(-50%);
}
[data-v-82fd78df] .p-togglebutton {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}

.selection-toolbox[data-v-32cb49b5] {
  transform: translateX(-50%) translateY(-120%);
}

.group-title-editor.node-title-editor[data-v-83a5c9ab] {
  z-index: 9999;
  padding: 0.25rem;
}
[data-v-83a5c9ab] .editable-text {
  width: 100%;
  height: 100%;
}
[data-v-83a5c9ab] .editable-text input {
  width: 100%;
  height: 100%;
  /* Override the default font size */
  font-size: inherit;
}

[data-v-186bfdc4] .highlight {
  background-color: var(--p-primary-color);
  color: var(--p-primary-contrast-color);
  font-weight: bold;
  border-radius: 0.25rem;
  padding: 0 0.125rem;
  margin: -0.125rem 0.125rem;
}

.invisible-dialog-root {
  width: 60%;
  min-width: 24rem;
  max-width: 48rem;
  border: 0 !important;
  background-color: transparent !important;
  margin-top: 25vh;
  margin-left: 400px;
}
@media all and (max-width: 768px) {
.invisible-dialog-root {
    margin-left: 0;
}
}
.node-search-box-dialog-mask {
  align-items: flex-start !important;
}

.side-bar-button-icon {
  font-size: var(--sidebar-icon-size) !important;
}
.side-bar-button-selected .side-bar-button-icon {
  font-size: var(--sidebar-icon-size) !important;
  font-weight: bold;
}

.side-bar-button[data-v-6ab4daa6] {
  width: var(--sidebar-width);
  height: var(--sidebar-width);
  border-radius: 0;
}
.comfyui-body-left .side-bar-button.side-bar-button-selected[data-v-6ab4daa6],
.comfyui-body-left .side-bar-button.side-bar-button-selected[data-v-6ab4daa6]:hover {
  border-left: 4px solid var(--p-button-text-primary-color);
}
.comfyui-body-right .side-bar-button.side-bar-button-selected[data-v-6ab4daa6],
.comfyui-body-right .side-bar-button.side-bar-button-selected[data-v-6ab4daa6]:hover {
  border-right: 4px solid var(--p-button-text-primary-color);
}

.side-tool-bar-container[data-v-04875455] {
  display: flex;
  flex-direction: column;
  align-items: center;

  width: var(--sidebar-width);
  height: 100%;

  background-color: var(--comfy-menu-secondary-bg);
  color: var(--fg-color);
  box-shadow: var(--bar-shadow);

  --sidebar-width: 4rem;
  --sidebar-icon-size: 1.5rem;
}
.side-tool-bar-container.small-sidebar[data-v-04875455] {
  --sidebar-width: 2.5rem;
  --sidebar-icon-size: 1rem;
}
.side-tool-bar-end[data-v-04875455] {
  align-self: flex-end;
  margin-top: auto;
}

.status-indicator[data-v-fd6ae3af] {
  position: absolute;
  font-weight: 700;
  font-size: 1.5rem;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%)
}

[data-v-cdece80f] .p-togglebutton {
  position: relative;
  flex-shrink: 0;
  border-radius: 0px;
  border-width: 0px;
  border-right-width: 1px;
  border-style: solid;
  background-color: transparent;
  padding: 0px;
  border-right-color: var(--border-color)
}
[data-v-cdece80f] .p-togglebutton::before {
  display: none
}
[data-v-cdece80f] .p-togglebutton:first-child {
  border-left-width: 1px;
  border-style: solid;
  border-left-color: var(--border-color)
}
[data-v-cdece80f] .p-togglebutton:not(:first-child) {
  border-left-width: 0px
}
[data-v-cdece80f] .p-togglebutton.p-togglebutton-checked {
  height: 100%;
  border-bottom-width: 1px;
  border-style: solid;
  border-bottom-color: var(--p-button-text-primary-color)
}
[data-v-cdece80f] .p-togglebutton:not(.p-togglebutton-checked) {
  opacity: 0.75
}
[data-v-cdece80f] .p-togglebutton-checked .close-button,[data-v-cdece80f] .p-togglebutton:hover .close-button {
  visibility: visible
}
[data-v-cdece80f] .p-togglebutton:hover .status-indicator {
  display: none
}
[data-v-cdece80f] .p-togglebutton .close-button {
  visibility: hidden
}
[data-v-cdece80f] .p-scrollpanel-content {
  height: 100%
}

/* Scrollbar half opacity to avoid blocking the active tab bottom border */
[data-v-cdece80f] .p-scrollpanel:hover .p-scrollpanel-bar,[data-v-cdece80f] .p-scrollpanel:active .p-scrollpanel-bar {
  opacity: 0.5
}
[data-v-cdece80f] .p-selectbutton {
  height: 100%;
  border-radius: 0px
}

[data-v-6ab68035] .workflow-tabs {
  background-color: var(--comfy-menu-bg);
}

[data-v-2b6c0e60] .p-inputtext {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.comfyui-queue-button[data-v-b26020de] .p-splitbutton-dropdown {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.actionbar[data-v-ebd56d51] {
  pointer-events: all;
  position: fixed;
  z-index: 1000;
}
.actionbar.is-docked[data-v-ebd56d51] {
  position: static;
  border-style: none;
  background-color: transparent;
  padding: 0px;
}
.actionbar.is-dragging[data-v-ebd56d51] {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
[data-v-ebd56d51] .p-panel-content {
  padding: 0.25rem;
}
.is-docked[data-v-ebd56d51] .p-panel-content {
  padding: 0px;
}
[data-v-ebd56d51] .p-panel-header {
  display: none;
}
.drag-handle[data-v-ebd56d51] {
  height: -moz-max-content;
  height: max-content;
  width: 0.75rem;
}

[data-v-6a9cf415] .p-menubar-submenu.dropdown-direction-up {
  top: auto;
  bottom: 100%;
  flex-direction: column-reverse;
}
.keybinding-tag[data-v-6a9cf415] {
  background: var(--p-content-hover-background);
  border-color: var(--p-content-border-color);
  border-style: solid;
}

.comfyui-menu[data-v-6d26f480] {
  width: 100vw;
  height: var(--comfy-topbar-height);
  background: var(--comfy-menu-bg);
  color: var(--fg-color);
  box-shadow: var(--bar-shadow);
  font-family: Arial, Helvetica, sans-serif;
  font-size: 0.8em;
  box-sizing: border-box;
  z-index: 1000;
  order: 0;
  grid-column: 1/-1;
}
.comfyui-menu.dropzone[data-v-6d26f480] {
  background: var(--p-highlight-background);
}
.comfyui-menu.dropzone-active[data-v-6d26f480] {
  background: var(--p-highlight-background-focus);
}
[data-v-6d26f480] .p-menubar-item-label {
  line-height: revert;
}
.comfyui-logo[data-v-6d26f480] {
  font-size: 1.2em;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  cursor: default;
}

.comfyui-body[data-v-3813d0e2] {
  grid-template-columns: auto 1fr auto;
  grid-template-rows: auto 1fr auto;
}

/**
  +------------------+------------------+------------------+
  |                                                        |
  |  .comfyui-body-                                        |
  |       top                                              |
  | (spans all cols)                                       |
  |                                                        |
  +------------------+------------------+------------------+
  |                  |                  |                  |
  | .comfyui-body-   |   #graph-canvas  | .comfyui-body-   |
  |      left        |                  |      right       |
  |                  |                  |                  |
  |                  |                  |                  |
  +------------------+------------------+------------------+
  |                                                        |
  |  .comfyui-body-                                        |
  |      bottom                                            |
  | (spans all cols)                                       |
  |                                                        |
  +------------------+------------------+------------------+
*/
.comfyui-body-top[data-v-3813d0e2] {
  order: -5;
  /* Span across all columns */
  grid-column: 1/-1;
  /* Position at the first row */
  grid-row: 1;
  /* Top menu bar dropdown needs to be above of graph canvas splitter overlay which is z-index: 999 */
  /* Top menu bar z-index needs to be higher than bottom menu bar z-index as by default
  pysssss's image feed is located at body-bottom, and it can overlap with the queue button, which
  is located in body-top. */
  z-index: 1001;
  display: flex;
  flex-direction: column;
}
.comfyui-body-left[data-v-3813d0e2] {
  order: -4;
  /* Position in the first column */
  grid-column: 1;
  /* Position below the top element */
  grid-row: 2;
  z-index: 10;
  display: flex;
}
.graph-canvas-container[data-v-3813d0e2] {
  width: 100%;
  height: 100%;
  order: -3;
  grid-column: 2;
  grid-row: 2;
  position: relative;
  overflow: hidden;
}
.comfyui-body-right[data-v-3813d0e2] {
  order: -2;
  z-index: 10;
  grid-column: 3;
  grid-row: 2;
}
.comfyui-body-bottom[data-v-3813d0e2] {
  order: 4;
  /* Span across all columns */
  grid-column: 1/-1;
  grid-row: 3;
  /* Bottom menu bar dropdown needs to be above of graph canvas splitter overlay which is z-index: 999 */
  z-index: 1000;
  display: flex;
  flex-direction: column;
}
