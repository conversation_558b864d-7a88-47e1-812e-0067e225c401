[rank0]: Traceback (most recent call last):
[rank0]:   File "/root/diffusion-pipe/train.py", line 366, in <module>
[rank0]:     pipeline_model = deepspeed.pipe.PipelineModule(
[rank0]:   File "/root/miniconda3/envs/pipe/lib/python3.10/site-packages/deepspeed/runtime/pipe/module.py", line 213, in __init__
[rank0]:     self.to(get_accelerator().device_name(self.local_rank))
[rank0]:   File "/root/miniconda3/envs/pipe/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1174, in to
[rank0]:     return self._apply(convert)
[rank0]:   File "/root/miniconda3/envs/pipe/lib/python3.10/site-packages/torch/nn/modules/module.py", line 780, in _apply
[rank0]:     module._apply(fn)
[rank0]:   File "/root/miniconda3/envs/pipe/lib/python3.10/site-packages/torch/nn/modules/module.py", line 780, in _apply
[rank0]:     module._apply(fn)
[rank0]:   File "/root/miniconda3/envs/pipe/lib/python3.10/site-packages/torch/nn/modules/module.py", line 780, in _apply
[rank0]:     module._apply(fn)
[rank0]:   [Previous line repeated 2 more times]
[rank0]:   File "/root/miniconda3/envs/pipe/lib/python3.10/site-packages/torch/nn/modules/module.py", line 805, in _apply
[rank0]:     param_applied = fn(param)
[rank0]:   File "/root/miniconda3/envs/pipe/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1160, in convert
[rank0]:     return t.to(
[rank0]: torch.OutOfMemoryError: CUDA out of memory. Tried to allocate 136.00 MiB. GPU 0 has a total capacity of 23.54 GiB of which 26.69 MiB is free. Including non-PyTorch memory, this process has 23.51 GiB memory in use. Of the allocated memory 21.65 GiB is allocated by PyTorch, and 1.31 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
[rank0]:[W525 12:38:58.096152215 ProcessGroupNCCL.cpp:1168] Warning: WARNING: process group has NOT been destroyed before we destruct ProcessGroupNCCL. On normal program exit, the application should call destroy_process_group to ensure that any pending NCCL operations have finished in this process. In rare cases this process can exit before this point and block the progress of another member of the process group. This constraint has always been present,  but this warning has only been added since PyTorch 2.4 (function operator())
[2025-05-25 12:39:00,680] [INFO] [launch.py:319:sigkill_handler] Killing subprocess 3394
[2025-05-25 12:39:00,680] [ERROR] [launch.py:325:sigkill_handler] ['/root/miniconda3/envs/pipe/bin/python', '-u', 'train.py', '--local_rank=0', '--regenerate_cache', '--deepspeed', '--config', 'examples/wan2_1_lora.toml'] exits with return code = 1
命令执行失败：
None