Stack trace:
Frame         Function      Args
0007FFFFB0E0  00021006118E (00021028DEE8, 000210272B3E, 0007FFFFB0E0, 0007FFFF9FE0) msys-2.0.dll+0x2118E
0007FFFFB0E0  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFB3B8) msys-2.0.dll+0x69BA
0007FFFFB0E0  0002100469F2 (00021028DF99, 0007FFFFAF98, 0007FFFFB0E0, 000000000000) msys-2.0.dll+0x69F2
0007FFFFB0E0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFB0E0  00021006A545 (0007FFFFB0F0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFB3C0  00021006B9A5 (0007FFFFB0F0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF9BAD00000 ntdll.dll
7FF9B8D60000 KERNEL32.DLL
7FF9B72C0000 KERNELBASE.dll
7FF9B8A80000 USER32.dll
7FF9B7090000 win32u.dll
7FF9B88D0000 GDI32.dll
7FF9B7D60000 gdi32full.dll
7FF9B70B0000 msvcp_win.dll
7FF9B7170000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF9B8CB0000 advapi32.dll
7FF9BA680000 msvcrt.dll
7FF9BAC30000 sechost.dll
7FF9BA550000 RPCRT4.dll
7FF9B6690000 CRYPTBASE.DLL
7FF9B6E30000 bcryptPrimitives.dll
7FF9B8C20000 IMM32.DLL
