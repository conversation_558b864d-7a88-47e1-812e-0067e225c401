{"version": 3, "file": "GraphView-DIFwgSix.js", "sources": ["../../src/components/BrowserTabTitle.vue", "../../src/components/MenuHamburger.vue", "../../src/components/dialog/UnloadWindowConfirmDialog.vue", "../../src/components/LiteGraphCanvasSplitterOverlay.vue", "../../src/components/common/ExtensionSlot.vue", "../../src/components/bottomPanel/BottomPanel.vue", "../../src/composables/element/useAbsolutePosition.ts", "../../src/composables/element/useDomClipping.ts", "../../src/components/graph/widgets/DomWidget.vue", "../../src/components/graph/DomWidgets.vue", "../../src/components/graph/GraphCanvasMenu.vue", "../../src/components/graph/NodeBadge.vue", "../../src/components/graph/NodeTooltip.vue", "../../src/components/graph/SelectionOverlay.vue", "../../src/components/graph/selectionToolbox/ColorPickerButton.vue", "../../src/composables/useRefreshableSelection.ts", "../../src/components/graph/SelectionToolbox.vue", "../../src/components/graph/TitleEditor.vue", "../../src/stores/workspace/searchBoxStore.ts", "../../src/types/litegraphTypes.ts", "../../src/types/searchBoxTypes.ts", "../../src/components/primevueOverride/AutoCompletePlus.vue", "../../src/components/searchbox/NodeSearchItem.vue", "../../src/components/searchbox/NodeSearchBox.vue", "../../src/components/searchbox/NodeSearchBoxPopover.vue", "../../src/components/sidebar/SidebarIcon.vue", "../../src/components/sidebar/SidebarLogoutIcon.vue", "../../src/components/sidebar/SidebarSettingsToggleIcon.vue", "../../src/components/sidebar/SidebarThemeToggleIcon.vue", "../../src/components/sidebar/SideToolbar.vue", "../../src/components/topbar/WorkflowTab.vue", "../../src/components/topbar/WorkflowTabs.vue", "../../src/composables/useCanvasDrop.ts", "../../src/composables/useContextMenuTranslation.ts", "../../src/composables/useCopy.ts", "../../src/composables/useGlobalLitegraph.ts", "../../src/composables/useLitegraphSettings.ts", "../../src/composables/usePaste.ts", "../../src/composables/useWorkflowPersistence.ts", "../../src/constants/coreSettings.ts", "../../src/components/graph/GraphCanvas.vue", "../../src/components/toast/GlobalToast.vue", "../../src/components/actionbar/BatchCountEdit.vue", "../../src/components/actionbar/ComfyQueueButton.vue", "../../src/components/actionbar/ComfyActionbar.vue", "../../src/components/topbar/BottomPanelToggleButton.vue", "../../src/components/topbar/CommandMenubar.vue", "../../src/components/topbar/TopMenubar.vue", "../../src/composables/useCoreCommands.ts", "../../src/types/serverArgs.ts", "../../src/constants/serverConfig.ts", "../../src/services/autoQueueService.ts", "../../src/views/GraphView.vue"], "sourcesContent": ["<template>\n  <div>\n    <!-- This component does not render anything visible. -->\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { useTitle } from '@vueuse/core'\nimport { computed } from 'vue'\n\nimport { useExecutionStore } from '@/stores/executionStore'\nimport { useSettingStore } from '@/stores/settingStore'\nimport { useWorkflowStore } from '@/stores/workflowStore'\n\nconst DEFAULT_TITLE = 'ComfyUI'\nconst TITLE_SUFFIX = ' - ComfyUI'\n\nconst executionStore = useExecutionStore()\nconst executionText = computed(() =>\n  executionStore.isIdle ? '' : `[${executionStore.executionProgress}%]`\n)\n\nconst settingStore = useSettingStore()\nconst betaMenuEnabled = computed(\n  () => settingStore.get('Comfy.UseNewMenu') !== 'Disabled'\n)\n\nconst workflowStore = useWorkflowStore()\nconst isUnsavedText = computed(() =>\n  workflowStore.activeWorkflow?.isModified ||\n  !workflowStore.activeWorkflow?.isPersisted\n    ? ' *'\n    : ''\n)\nconst workflowNameText = computed(() => {\n  const workflowName = workflowStore.activeWorkflow?.filename\n  return workflowName\n    ? isUnsavedText.value + workflowName + TITLE_SUFFIX\n    : DEFAULT_TITLE\n})\n\nconst nodeExecutionTitle = computed(() =>\n  executionStore.executingNode && executionStore.executingNodeProgress\n    ? `${executionText.value}[${executionStore.executingNodeProgress}%] ${executionStore.executingNode.type}`\n    : ''\n)\n\nconst workflowTitle = computed(\n  () =>\n    executionText.value +\n    (betaMenuEnabled.value ? workflowNameText.value : DEFAULT_TITLE)\n)\n\nconst title = computed(() => nodeExecutionTitle.value || workflowTitle.value)\nuseTitle(title)\n</script>\n", "<template>\n  <div\n    v-show=\"workspaceState.focusMode\"\n    class=\"comfy-menu-hamburger no-drag\"\n    :style=\"positionCSS\"\n  >\n    <Button\n      icon=\"pi pi-bars\"\n      severity=\"secondary\"\n      text\n      size=\"large\"\n      v-tooltip=\"{ value: $t('menu.showMenu'), showDelay: 300 }\"\n      :aria-label=\"$t('menu.showMenu')\"\n      aria-live=\"assertive\"\n      @click=\"exitFocusMode\"\n      @contextmenu=\"showNativeSystemMenu\"\n    />\n    <div v-show=\"menuSetting !== 'Bottom'\" class=\"window-actions-spacer\" />\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport Button from 'primevue/button'\nimport { CSSProperties, computed, watchEffect } from 'vue'\n\nimport { app } from '@/scripts/app'\nimport { useSettingStore } from '@/stores/settingStore'\nimport { useWorkspaceStore } from '@/stores/workspaceStore'\nimport { showNativeSystemMenu } from '@/utils/envUtil'\n\nconst workspaceState = useWorkspaceStore()\nconst settingStore = useSettingStore()\nconst exitFocusMode = () => {\n  workspaceState.focusMode = false\n}\n\nwatchEffect(() => {\n  if (settingStore.get('Comfy.UseNewMenu') !== 'Disabled') {\n    return\n  }\n  if (workspaceState.focusMode) {\n    app.ui.menuContainer.style.display = 'none'\n  } else {\n    app.ui.menuContainer.style.display = 'block'\n  }\n})\n\nconst menuSetting = computed(() => settingStore.get('Comfy.UseNewMenu'))\nconst positionCSS = computed<CSSProperties>(() =>\n  // 'Bottom' menuSetting shows the hamburger button in the bottom right corner\n  // 'Disabled', 'Top' menuSetting shows the hamburger button in the top right corner\n  menuSetting.value === 'Bottom'\n    ? { bottom: '0px', right: '0px' }\n    : { top: '0px', right: '0px' }\n)\n</script>\n\n<style scoped>\n.comfy-menu-hamburger {\n  @apply fixed z-[9999] flex flex-row;\n}\n</style>\n", "<template>\n  <div>\n    <!--\n    UnloadWindowConfirmDialog: This component does not render\n    anything visible. It is used to confirm the user wants to\n    close the window, and if they do, it will call the\n    beforeunload event.\n    -->\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { onBeforeUnmount, onMounted } from 'vue'\n\nimport { useSettingStore } from '@/stores/settingStore'\nimport { useWorkflowStore } from '@/stores/workflowStore'\n\nconst settingStore = useSettingStore()\nconst workflowStore = useWorkflowStore()\n\nconst handleBeforeUnload = (event: BeforeUnloadEvent) => {\n  if (\n    settingStore.get('Comfy.Window.UnloadConfirmation') &&\n    workflowStore.modifiedWorkflows.length > 0\n  ) {\n    event.preventDefault()\n    return true\n  }\n  return undefined\n}\n\nonMounted(() => {\n  window.addEventListener('beforeunload', handleBeforeUnload)\n})\n\nonBeforeUnmount(() => {\n  window.removeEventListener('beforeunload', handleBeforeUnload)\n})\n</script>\n", "<template>\n  <Splitter\n    class=\"splitter-overlay-root splitter-overlay\"\n    :pt:gutter=\"sidebarPanelVisible ? '' : 'hidden'\"\n    :key=\"activeSidebarTabId ?? undefined\"\n    :stateKey=\"activeSidebarTabId ?? undefined\"\n    stateStorage=\"local\"\n  >\n    <SplitterPanel\n      class=\"side-bar-panel\"\n      :minSize=\"10\"\n      :size=\"20\"\n      v-show=\"sidebarPanelVisible\"\n      v-if=\"sidebarLocation === 'left'\"\n    >\n      <slot name=\"side-bar-panel\"></slot>\n    </SplitterPanel>\n\n    <SplitterPanel :size=\"100\">\n      <Splitter\n        class=\"splitter-overlay max-w-full\"\n        layout=\"vertical\"\n        :pt:gutter=\"bottomPanelVisible ? '' : 'hidden'\"\n        stateKey=\"bottom-panel-splitter\"\n        stateStorage=\"local\"\n      >\n        <SplitterPanel class=\"graph-canvas-panel relative\">\n          <slot name=\"graph-canvas-panel\"></slot>\n        </SplitterPanel>\n        <SplitterPanel class=\"bottom-panel\" v-show=\"bottomPanelVisible\">\n          <slot name=\"bottom-panel\"></slot>\n        </SplitterPanel>\n      </Splitter>\n    </SplitterPanel>\n\n    <SplitterPanel\n      class=\"side-bar-panel\"\n      :minSize=\"10\"\n      :size=\"20\"\n      v-show=\"sidebarPanelVisible\"\n      v-if=\"sidebarLocation === 'right'\"\n    >\n      <slot name=\"side-bar-panel\"></slot>\n    </SplitterPanel>\n  </Splitter>\n</template>\n\n<script setup lang=\"ts\">\nimport Splitter from 'primevue/splitter'\nimport SplitterPanel from 'primevue/splitterpanel'\nimport { computed } from 'vue'\n\nimport { useSettingStore } from '@/stores/settingStore'\nimport { useBottomPanelStore } from '@/stores/workspace/bottomPanelStore'\nimport { useSidebarTabStore } from '@/stores/workspace/sidebarTabStore'\n\nconst settingStore = useSettingStore()\nconst sidebarLocation = computed<'left' | 'right'>(() =>\n  settingStore.get('Comfy.Sidebar.Location')\n)\n\nconst sidebarPanelVisible = computed(\n  () => useSidebarTabStore().activeSidebarTab !== null\n)\nconst bottomPanelVisible = computed(\n  () => useBottomPanelStore().bottomPanelVisible\n)\nconst activeSidebarTabId = computed(\n  () => useSidebarTabStore().activeSidebarTabId\n)\n</script>\n\n<style scoped>\n:deep(.p-splitter-gutter) {\n  pointer-events: auto;\n}\n\n:deep(.p-splitter-gutter:hover),\n:deep(.p-splitter-gutter[data-p-gutter-resizing='true']) {\n  transition: background-color 0.2s ease 300ms;\n  background-color: var(--p-primary-color);\n}\n\n.side-bar-panel {\n  background-color: var(--bg-color);\n  pointer-events: auto;\n}\n\n.bottom-panel {\n  background-color: var(--bg-color);\n  pointer-events: auto;\n}\n\n.splitter-overlay {\n  @apply bg-transparent pointer-events-none border-none;\n}\n\n.splitter-overlay-root {\n  @apply w-full h-full absolute top-0 left-0;\n\n  /* Set it the same as the ComfyUI menu */\n  /* Note: Lite-graph DOM widgets have the same z-index as the node id, so\n  999 should be sufficient to make sure splitter overlays on node's DOM\n  widgets */\n  z-index: 999;\n}\n</style>\n", "<template>\n  <component v-if=\"extension.type === 'vue'\" :is=\"extension.component\" />\n  <div\n    v-else\n    :ref=\"\n      (el) => {\n        if (el)\n          mountCustomExtension(\n            props.extension as CustomExtension,\n            el as HTMLElement\n          )\n      }\n    \"\n  ></div>\n</template>\n\n<script setup lang=\"ts\">\nimport { onBeforeUnmount } from 'vue'\n\nimport { CustomExtension, VueExtension } from '@/types/extensionTypes'\n\nconst props = defineProps<{\n  extension: VueExtension | CustomExtension\n}>()\n\nconst mountCustomExtension = (extension: CustomExtension, el: HTMLElement) => {\n  extension.render(el)\n}\n\nonBeforeUnmount(() => {\n  if (props.extension.type === 'custom' && props.extension.destroy) {\n    props.extension.destroy()\n  }\n})\n</script>\n", "<template>\n  <div class=\"flex flex-col h-full\">\n    <Tabs v-model:value=\"bottomPanelStore.activeBottomPanelTabId\">\n      <TabList pt:tabList=\"border-none\">\n        <div class=\"w-full flex justify-between\">\n          <div class=\"tabs-container\">\n            <Tab\n              v-for=\"tab in bottomPanelStore.bottomPanelTabs\"\n              :key=\"tab.id\"\n              :value=\"tab.id\"\n              class=\"p-3 border-none\"\n            >\n              <span class=\"font-bold\">\n                {{ tab.title.toUpperCase() }}\n              </span>\n            </Tab>\n          </div>\n          <Button\n            class=\"justify-self-end\"\n            icon=\"pi pi-times\"\n            severity=\"secondary\"\n            size=\"small\"\n            text\n            @click=\"bottomPanelStore.bottomPanelVisible = false\"\n          />\n        </div>\n      </TabList>\n    </Tabs>\n    <!-- h-0 to force the div to flex-grow -->\n    <div class=\"flex-grow h-0\">\n      <ExtensionSlot\n        v-if=\"\n          bottomPanelStore.bottomPanelVisible &&\n          bottomPanelStore.activeBottomPanelTab\n        \"\n        :extension=\"bottomPanelStore.activeBottomPanelTab\"\n      />\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport Button from 'primevue/button'\nimport Tab from 'primevue/tab'\nimport TabList from 'primevue/tablist'\nimport Tabs from 'primevue/tabs'\n\nimport ExtensionSlot from '@/components/common/ExtensionSlot.vue'\nimport { useBottomPanelStore } from '@/stores/workspace/bottomPanelStore'\n\nconst bottomPanelStore = useBottomPanelStore()\n</script>\n", "import type { Size, Vector2 } from '@comfyorg/litegraph'\nimport { CSSProperties, ref } from 'vue'\n\nimport { app } from '@/scripts/app'\nimport { useCanvasStore } from '@/stores/graphStore'\n\nexport interface PositionConfig {\n  /* The position of the element on litegraph canvas */\n  pos: Vector2\n  /* The size of the element on litegraph canvas */\n  size: Size\n  /* The scale factor of the canvas */\n  scale?: number\n}\n\nexport function useAbsolutePosition() {\n  const canvasStore = useCanvasStore()\n  const style = ref<CSSProperties>({\n    position: 'fixed',\n    left: '0px',\n    top: '0px',\n    width: '0px',\n    height: '0px'\n  })\n\n  /**\n   * Update the position of the element on the litegraph canvas.\n   *\n   * @param config\n   * @param extraStyle\n   */\n  const updatePosition = (\n    config: PositionConfig,\n    extraStyle?: CSSProperties\n  ) => {\n    const { pos, size, scale = canvasStore.canvas?.ds?.scale ?? 1 } = config\n    const [left, top] = app.canvasPosToClientPos(pos)\n    const [width, height] = size\n\n    style.value = {\n      ...style.value,\n      left: `${left}px`,\n      top: `${top}px`,\n      width: `${width * scale}px`,\n      height: `${height * scale}px`,\n      ...extraStyle\n    }\n  }\n\n  /**\n   * Update the position and size of the element on the litegraph canvas,\n   * with CSS transform scaling applied.\n   *\n   * @param config\n   * @param extraStyle\n   */\n  const updatePositionWithTransform = (\n    config: PositionConfig,\n    extraStyle?: CSSProperties\n  ) => {\n    const { pos, size, scale = canvasStore.canvas?.ds?.scale ?? 1 } = config\n    const [left, top] = app.canvasPosToClientPos(pos)\n    const [width, height] = size\n\n    style.value = {\n      ...style.value,\n      transformOrigin: '0 0',\n      transform: `scale(${scale})`,\n      left: `${left}px`,\n      top: `${top}px`,\n      width: `${width}px`,\n      height: `${height}px`,\n      ...extraStyle\n    }\n  }\n\n  return {\n    style,\n    updatePosition,\n    updatePositionWithTransform\n  }\n}\n", "import { CSSProperties, ref } from 'vue'\n\ninterface Rect {\n  x: number\n  y: number\n  width: number\n  height: number\n}\n\n/**\n * Finds the intersection between two rectangles\n */\nfunction intersect(a: Rect, b: Rect): [number, number, number, number] | null {\n  const x1 = Math.max(a.x, b.x)\n  const y1 = Math.max(a.y, b.y)\n  const x2 = Math.min(a.x + a.width, b.x + b.width)\n  const y2 = Math.min(a.y + a.height, b.y + b.height)\n\n  if (x1 >= x2 || y1 >= y2) {\n    return null\n  }\n\n  return [x1, y1, x2 - x1, y2 - y1]\n}\n\nexport interface ClippingOptions {\n  margin?: number\n}\n\nexport const useDomClipping = (options: ClippingOptions = {}) => {\n  const style = ref<CSSProperties>({})\n  const { margin = 4 } = options\n\n  /**\n   * Calculates a clip path for an element based on its intersection with a selected area\n   */\n  const calculateClipPath = (\n    elementRect: DOMRect,\n    canvasRect: DOMRect,\n    isSelected: boolean,\n    selectedArea?: {\n      x: number\n      y: number\n      width: number\n      height: number\n      scale: number\n      offset: [number, number]\n    }\n  ): string => {\n    if (!isSelected && selectedArea) {\n      const { scale, offset } = selectedArea\n\n      // Get intersection in browser space\n      const intersection = intersect(\n        {\n          x: elementRect.left - canvasRect.left,\n          y: elementRect.top - canvasRect.top,\n          width: elementRect.width,\n          height: elementRect.height\n        },\n        {\n          x: (selectedArea.x + offset[0] - margin) * scale,\n          y: (selectedArea.y + offset[1] - margin) * scale,\n          width: (selectedArea.width + 2 * margin) * scale,\n          height: (selectedArea.height + 2 * margin) * scale\n        }\n      )\n\n      if (!intersection) {\n        return ''\n      }\n\n      // Convert intersection to canvas scale (element has scale transform)\n      const clipX =\n        (intersection[0] - elementRect.left + canvasRect.left) / scale + 'px'\n      const clipY =\n        (intersection[1] - elementRect.top + canvasRect.top) / scale + 'px'\n      const clipWidth = intersection[2] / scale + 'px'\n      const clipHeight = intersection[3] / scale + 'px'\n\n      return `polygon(0% 0%, 0% 100%, ${clipX} 100%, ${clipX} ${clipY}, calc(${clipX} + ${clipWidth}) ${clipY}, calc(${clipX} + ${clipWidth}) calc(${clipY} + ${clipHeight}), ${clipX} calc(${clipY} + ${clipHeight}), ${clipX} 100%, 100% 100%, 100% 0%)`\n    }\n\n    return ''\n  }\n\n  /**\n   * Updates the clip-path style based on element and selection information\n   */\n  const updateClipPath = (\n    element: HTMLElement,\n    canvasElement: HTMLCanvasElement,\n    isSelected: boolean,\n    selectedArea?: {\n      x: number\n      y: number\n      width: number\n      height: number\n      scale: number\n      offset: [number, number]\n    }\n  ) => {\n    const elementRect = element.getBoundingClientRect()\n    const canvasRect = canvasElement.getBoundingClientRect()\n\n    const clipPath = calculateClipPath(\n      elementRect,\n      canvasRect,\n      isSelected,\n      selectedArea\n    )\n\n    style.value = {\n      clipPath: clipPath || 'none',\n      willChange: 'clip-path'\n    }\n  }\n\n  return {\n    style,\n    updateClipPath\n  }\n}\n", "<template>\n  <div\n    class=\"dom-widget\"\n    :title=\"tooltip\"\n    ref=\"widgetElement\"\n    :style=\"style\"\n    v-show=\"widgetState.visible\"\n  >\n    <component\n      v-if=\"isComponentWidget(widget)\"\n      :is=\"widget.component\"\n      :modelValue=\"widget.value\"\n      @update:modelValue=\"emit('update:widgetValue', $event)\"\n      :widget=\"widget\"\n    />\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport type { LGraphNode } from '@comfyorg/litegraph'\nimport { useEventListener } from '@vueuse/core'\nimport { CSSProperties, computed, onMounted, ref, watch } from 'vue'\n\nimport { useAbsolutePosition } from '@/composables/element/useAbsolutePosition'\nimport { useDomClipping } from '@/composables/element/useDomClipping'\nimport {\n  type BaseDOMWidget,\n  isComponentWidget,\n  isDOMWidget\n} from '@/scripts/domWidget'\nimport { DomWidgetState } from '@/stores/domWidgetStore'\nimport { useCanvasStore } from '@/stores/graphStore'\nimport { useSettingStore } from '@/stores/settingStore'\n\nconst { widget, widgetState } = defineProps<{\n  widget: BaseDOMWidget<string | object>\n  widgetState: DomWidgetState\n}>()\n\nconst emit = defineEmits<{\n  (e: 'update:widgetValue', value: string | object): void\n}>()\n\nconst widgetElement = ref<HTMLElement | undefined>()\n\nconst { style: positionStyle, updatePositionWithTransform } =\n  useAbsolutePosition()\nconst { style: clippingStyle, updateClipPath } = useDomClipping()\nconst style = computed<CSSProperties>(() => ({\n  ...positionStyle.value,\n  ...(enableDomClipping.value ? clippingStyle.value : {}),\n  zIndex: widgetState.zIndex,\n  pointerEvents: widgetState.readonly ? 'none' : 'auto'\n}))\n\nconst canvasStore = useCanvasStore()\nconst settingStore = useSettingStore()\nconst enableDomClipping = computed(() =>\n  settingStore.get('Comfy.DOMClippingEnabled')\n)\n\nconst updateDomClipping = () => {\n  const lgCanvas = canvasStore.canvas\n  if (!lgCanvas || !widgetElement.value) return\n\n  const selectedNode = Object.values(\n    lgCanvas.selected_nodes ?? {}\n  )[0] as LGraphNode\n  const node = widget.node\n  const isSelected = selectedNode === node\n  const renderArea = selectedNode?.renderArea\n  const offset = lgCanvas.ds.offset\n  const scale = lgCanvas.ds.scale\n  const selectedAreaConfig = renderArea\n    ? {\n        x: renderArea[0],\n        y: renderArea[1],\n        width: renderArea[2],\n        height: renderArea[3],\n        scale,\n        offset: [offset[0], offset[1]] as [number, number]\n      }\n    : undefined\n\n  updateClipPath(\n    widgetElement.value,\n    lgCanvas.canvas,\n    isSelected,\n    selectedAreaConfig\n  )\n}\n\nwatch(\n  () => widgetState,\n  (newState) => {\n    updatePositionWithTransform(newState)\n    if (enableDomClipping.value) {\n      updateDomClipping()\n    }\n  },\n  { deep: true }\n)\n\nwatch(\n  () => widgetState.visible,\n  (newVisible, oldVisible) => {\n    if (!newVisible && oldVisible) {\n      widget.options.onHide?.(widget)\n    }\n  }\n)\n\nif (isDOMWidget(widget)) {\n  if (widget.element.blur) {\n    useEventListener(document, 'mousedown', (event) => {\n      if (!widget.element.contains(event.target as HTMLElement)) {\n        widget.element.blur()\n      }\n    })\n  }\n\n  for (const evt of widget.options.selectOn ?? ['focus', 'click']) {\n    useEventListener(widget.element, evt, () => {\n      const lgCanvas = canvasStore.canvas\n      lgCanvas?.selectNode(widget.node)\n      lgCanvas?.bringToFront(widget.node)\n    })\n  }\n}\n\nconst inputSpec = widget.node.constructor.nodeData\nconst tooltip = inputSpec?.inputs?.[widget.name]?.tooltip\n\nonMounted(() => {\n  if (isDOMWidget(widget) && widgetElement.value) {\n    widgetElement.value.appendChild(widget.element)\n  }\n})\n</script>\n\n<style scoped>\n.dom-widget > * {\n  @apply h-full w-full;\n}\n</style>\n", "<template>\n  <!-- Create a new stacking context for widgets to avoid z-index issues -->\n  <div class=\"isolate\">\n    <DomWidget\n      v-for=\"widget in widgets\"\n      :key=\"widget.id\"\n      :widget=\"widget\"\n      :widget-state=\"domWidgetStore.widgetStates.get(widget.id)!\"\n      @update:widget-value=\"widget.value = $event\"\n    />\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport type { LGraphNode } from '@comfyorg/litegraph'\nimport { computed, watch } from 'vue'\n\nimport DomWidget from '@/components/graph/widgets/DomWidget.vue'\nimport { useChainCallback } from '@/composables/functional/useChainCallback'\nimport { BaseDOMWidget } from '@/scripts/domWidget'\nimport { useDomWidgetStore } from '@/stores/domWidgetStore'\nimport { useCanvasStore } from '@/stores/graphStore'\n\nconst domWidgetStore = useDomWidgetStore()\nconst widgets = computed(() =>\n  Array.from(\n    domWidgetStore.widgetInstances.values() as Iterable<\n      BaseDOMWidget<string | object>\n    >\n  )\n)\n\nconst DEFAULT_MARGIN = 10\nconst updateWidgets = () => {\n  const lgCanvas = canvasStore.canvas\n  if (!lgCanvas) return\n\n  const lowQuality = lgCanvas.low_quality\n  for (const widget of domWidgetStore.widgetInstances.values()) {\n    const node = widget.node as LGraphNode\n    const widgetState = domWidgetStore.widgetStates.get(widget.id)\n\n    if (!widgetState) continue\n\n    const visible =\n      lgCanvas.isNodeVisible(node) &&\n      !(widget.options.hideOnZoom && lowQuality) &&\n      widget.isVisible()\n\n    widgetState.visible = visible\n    if (visible) {\n      const margin = widget.options.margin ?? DEFAULT_MARGIN\n      widgetState.pos = [node.pos[0] + margin, node.pos[1] + margin + widget.y]\n      widgetState.size = [\n        (widget.width ?? node.width) - margin * 2,\n        (widget.computedHeight ?? 50) - margin * 2\n      ]\n      // TODO: optimize this logic as it's O(n), where n is the number of nodes\n      widgetState.zIndex = lgCanvas.graph?.nodes.indexOf(node) ?? -1\n      widgetState.readonly = lgCanvas.read_only\n    }\n  }\n}\n\nconst canvasStore = useCanvasStore()\nwatch(\n  () => canvasStore.canvas,\n  (lgCanvas) => {\n    if (!lgCanvas) return\n\n    lgCanvas.onDrawForeground = useChainCallback(\n      lgCanvas.onDrawForeground,\n      () => {\n        updateWidgets()\n      }\n    )\n  }\n)\n</script>\n", "<template>\n  <ButtonGroup\n    class=\"p-buttongroup-vertical absolute bottom-[10px] right-[10px] z-[1000]\"\n  >\n    <Button\n      severity=\"secondary\"\n      icon=\"pi pi-plus\"\n      v-tooltip.left=\"t('graphCanvasMenu.zoomIn')\"\n      :aria-label=\"$t('graphCanvasMenu.zoomIn')\"\n      @mousedown=\"repeat('Comfy.Canvas.ZoomIn')\"\n      @mouseup=\"stopRepeat\"\n    />\n    <Button\n      severity=\"secondary\"\n      icon=\"pi pi-minus\"\n      v-tooltip.left=\"t('graphCanvasMenu.zoomOut')\"\n      :aria-label=\"$t('graphCanvasMenu.zoomOut')\"\n      @mousedown=\"repeat('Comfy.Canvas.ZoomOut')\"\n      @mouseup=\"stopRepeat\"\n    />\n    <Button\n      severity=\"secondary\"\n      icon=\"pi pi-expand\"\n      v-tooltip.left=\"t('graphCanvasMenu.fitView')\"\n      :aria-label=\"$t('graphCanvasMenu.fitView')\"\n      @click=\"() => commandStore.execute('Comfy.Canvas.FitView')\"\n    />\n    <Button\n      severity=\"secondary\"\n      v-tooltip.left=\"\n        t(\n          'graphCanvasMenu.' +\n            (canvasStore.canvas?.read_only ? 'panMode' : 'selectMode')\n        ) + ' (Space)'\n      \"\n      :aria-label=\"\n        t(\n          'graphCanvasMenu.' +\n            (canvasStore.canvas?.read_only ? 'panMode' : 'selectMode')\n        )\n      \"\n      @click=\"() => commandStore.execute('Comfy.Canvas.ToggleLock')\"\n    >\n      <template #icon>\n        <i-material-symbols:pan-tool-outline\n          v-if=\"canvasStore.canvas?.read_only\"\n        />\n        <i-simple-line-icons:cursor v-else />\n      </template>\n    </Button>\n    <Button\n      severity=\"secondary\"\n      :icon=\"linkHidden ? 'pi pi-eye-slash' : 'pi pi-eye'\"\n      v-tooltip.left=\"t('graphCanvasMenu.toggleLinkVisibility')\"\n      :aria-label=\"$t('graphCanvasMenu.toggleLinkVisibility')\"\n      @click=\"() => commandStore.execute('Comfy.Canvas.ToggleLinkVisibility')\"\n      data-testid=\"toggle-link-visibility-button\"\n    />\n  </ButtonGroup>\n</template>\n\n<script setup lang=\"ts\">\nimport { LiteGraph } from '@comfyorg/litegraph'\nimport Button from 'primevue/button'\nimport ButtonGroup from 'primevue/buttongroup'\nimport { computed } from 'vue'\nimport { useI18n } from 'vue-i18n'\n\nimport { useCommandStore } from '@/stores/commandStore'\nimport { useCanvasStore } from '@/stores/graphStore'\nimport { useSettingStore } from '@/stores/settingStore'\n\nconst { t } = useI18n()\nconst commandStore = useCommandStore()\nconst canvasStore = useCanvasStore()\nconst settingStore = useSettingStore()\n\nconst linkHidden = computed(\n  () => settingStore.get('Comfy.LinkRenderMode') === LiteGraph.HIDDEN_LINK\n)\n\nlet interval: number | null = null\nconst repeat = (command: string) => {\n  if (interval) return\n  const cmd = () => commandStore.execute(command)\n  cmd()\n  interval = window.setInterval(cmd, 100)\n}\nconst stopRepeat = () => {\n  if (interval) {\n    clearInterval(interval)\n    interval = null\n  }\n}\n</script>\n\n<style scoped>\n.p-buttongroup-vertical {\n  display: flex;\n  flex-direction: column;\n  border-radius: var(--p-button-border-radius);\n  overflow: hidden;\n  border: 1px solid var(--p-panel-border-color);\n}\n\n.p-buttongroup-vertical .p-button {\n  margin: 0;\n  border-radius: 0;\n}\n</style>\n", "<template>\n  <div>\n    <!-- This component does not render anything visible. -->\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport type { LGraphNode } from '@comfyorg/litegraph'\nimport { BadgePosition } from '@comfyorg/litegraph'\nimport { LGraphBadge } from '@comfyorg/litegraph'\nimport _ from 'lodash'\nimport { computed, onMounted, watch } from 'vue'\n\nimport { app } from '@/scripts/app'\nimport { ComfyNodeDefImpl, useNodeDefStore } from '@/stores/nodeDefStore'\nimport { useSettingStore } from '@/stores/settingStore'\nimport { useColorPaletteStore } from '@/stores/workspace/colorPaletteStore'\nimport { NodeBadgeMode } from '@/types/nodeSource'\n\nconst settingStore = useSettingStore()\nconst colorPaletteStore = useColorPaletteStore()\n\nconst nodeSourceBadgeMode = computed(\n  () => settingStore.get('Comfy.NodeBadge.NodeSourceBadgeMode') as NodeBadgeMode\n)\nconst nodeIdBadgeMode = computed(\n  () => settingStore.get('Comfy.NodeBadge.NodeIdBadgeMode') as NodeBadgeMode\n)\nconst nodeLifeCycleBadgeMode = computed(\n  () =>\n    settingStore.get('Comfy.NodeBadge.NodeLifeCycleBadgeMode') as NodeBadgeMode\n)\n\nwatch([nodeSourceBadgeMode, nodeIdBadgeMode, nodeLifeCycleBadgeMode], () => {\n  app.graph?.setDirtyCanvas(true, true)\n})\n\nconst nodeDefStore = useNodeDefStore()\nfunction badgeTextVisible(\n  nodeDef: ComfyNodeDefImpl | null,\n  badgeMode: NodeBadgeMode\n): boolean {\n  return !(\n    badgeMode === NodeBadgeMode.None ||\n    (nodeDef?.isCoreNode && badgeMode === NodeBadgeMode.HideBuiltIn)\n  )\n}\n\nonMounted(() => {\n  app.registerExtension({\n    name: 'Comfy.NodeBadge',\n    nodeCreated(node: LGraphNode) {\n      node.badgePosition = BadgePosition.TopRight\n\n      const badge = computed(() => {\n        const nodeDef = nodeDefStore.fromLGraphNode(node)\n        return new LGraphBadge({\n          text: _.truncate(\n            [\n              badgeTextVisible(nodeDef, nodeIdBadgeMode.value)\n                ? `#${node.id}`\n                : '',\n              badgeTextVisible(nodeDef, nodeLifeCycleBadgeMode.value)\n                ? nodeDef?.nodeLifeCycleBadgeText ?? ''\n                : '',\n              badgeTextVisible(nodeDef, nodeSourceBadgeMode.value)\n                ? nodeDef?.nodeSource?.badgeText ?? ''\n                : ''\n            ]\n              .filter((s) => s.length > 0)\n              .join(' '),\n            {\n              length: 31\n            }\n          ),\n          fgColor:\n            colorPaletteStore.completedActivePalette.colors.litegraph_base\n              .BADGE_FG_COLOR,\n          bgColor:\n            colorPaletteStore.completedActivePalette.colors.litegraph_base\n              .BADGE_BG_COLOR\n        })\n      })\n\n      node.badges.push(() => badge.value)\n    }\n  })\n})\n</script>\n", "<template>\n  <div\n    v-if=\"tooltipText\"\n    ref=\"tooltipRef\"\n    class=\"node-tooltip\"\n    :style=\"{ left, top }\"\n  >\n    {{ tooltipText }}\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport {\n  LiteGraph,\n  isOverNodeInput,\n  isOverNodeOutput\n} from '@comfyorg/litegraph'\nimport { useEventListener } from '@vueuse/core'\nimport { nextTick, ref } from 'vue'\n\nimport { st } from '@/i18n'\nimport { app as comfyApp } from '@/scripts/app'\nimport { isDOMWidget } from '@/scripts/domWidget'\nimport { useNodeDefStore } from '@/stores/nodeDefStore'\nimport { useSettingStore } from '@/stores/settingStore'\nimport { normalizeI18nKey } from '@/utils/formatUtil'\n\nlet idleTimeout: number\nconst nodeDefStore = useNodeDefStore()\nconst settingStore = useSettingStore()\nconst tooltipRef = ref<HTMLDivElement | undefined>()\nconst tooltipText = ref('')\nconst left = ref<string>()\nconst top = ref<string>()\n\nconst hideTooltip = () => (tooltipText.value = '')\n\nconst showTooltip = async (tooltip: string | null | undefined) => {\n  if (!tooltip) return\n\n  left.value = comfyApp.canvas.mouse[0] + 'px'\n  top.value = comfyApp.canvas.mouse[1] + 'px'\n  tooltipText.value = tooltip\n\n  await nextTick()\n\n  const rect = tooltipRef.value?.getBoundingClientRect()\n  if (!rect) return\n\n  if (rect.right > window.innerWidth) {\n    left.value = comfyApp.canvas.mouse[0] - rect.width + 'px'\n  }\n\n  if (rect.top < 0) {\n    top.value = comfyApp.canvas.mouse[1] + rect.height + 'px'\n  }\n}\n\nconst onIdle = () => {\n  const { canvas } = comfyApp\n  const node = canvas.node_over\n  if (!node) return\n\n  const ctor = node.constructor as { title_mode?: 0 | 1 | 2 | 3 }\n  const nodeDef = nodeDefStore.nodeDefsByName[node.type ?? '']\n\n  if (\n    ctor.title_mode !== LiteGraph.NO_TITLE &&\n    canvas.graph_mouse[1] < node.pos[1] // If we are over a node, but not within the node then we are on its title\n  ) {\n    return showTooltip(nodeDef.description)\n  }\n\n  if (node.flags?.collapsed) return\n\n  const inputSlot = isOverNodeInput(\n    node,\n    canvas.graph_mouse[0],\n    canvas.graph_mouse[1],\n    [0, 0]\n  )\n  if (inputSlot !== -1) {\n    const inputName = node.inputs[inputSlot].name\n    const translatedTooltip = st(\n      `nodeDefs.${normalizeI18nKey(node.type ?? '')}.inputs.${normalizeI18nKey(inputName)}.tooltip`,\n      nodeDef.inputs[inputName]?.tooltip ?? ''\n    )\n    return showTooltip(translatedTooltip)\n  }\n\n  const outputSlot = isOverNodeOutput(\n    node,\n    canvas.graph_mouse[0],\n    canvas.graph_mouse[1],\n    [0, 0]\n  )\n  if (outputSlot !== -1) {\n    const translatedTooltip = st(\n      `nodeDefs.${normalizeI18nKey(node.type ?? '')}.outputs.${outputSlot}.tooltip`,\n      nodeDef.outputs[outputSlot]?.tooltip ?? ''\n    )\n    return showTooltip(translatedTooltip)\n  }\n\n  const widget = comfyApp.canvas.getWidgetAtCursor()\n  // Dont show for DOM widgets, these use native browser tooltips as we dont get proper mouse events on these\n  if (widget && !isDOMWidget(widget)) {\n    const translatedTooltip = st(\n      `nodeDefs.${normalizeI18nKey(node.type ?? '')}.inputs.${normalizeI18nKey(widget.name)}.tooltip`,\n      nodeDef.inputs[widget.name]?.tooltip ?? ''\n    )\n    // Widget tooltip can be set dynamically, current translation collection does not support this.\n    return showTooltip(widget.tooltip ?? translatedTooltip)\n  }\n}\n\nconst onMouseMove = (e: MouseEvent) => {\n  hideTooltip()\n  clearTimeout(idleTimeout)\n\n  if ((e.target as Node).nodeName !== 'CANVAS') return\n  idleTimeout = window.setTimeout(\n    onIdle,\n    settingStore.get('LiteGraph.Node.TooltipDelay')\n  )\n}\n\nuseEventListener(window, 'mousemove', onMouseMove)\nuseEventListener(window, 'click', hideTooltip)\n</script>\n\n<style lang=\"css\" scoped>\n.node-tooltip {\n  background: var(--comfy-input-bg);\n  border-radius: 5px;\n  box-shadow: 0 0 5px rgba(0, 0, 0, 0.4);\n  color: var(--input-text);\n  font-family: sans-serif;\n  left: 0;\n  max-width: 30vw;\n  padding: 4px 8px;\n  position: absolute;\n  top: 0;\n  transform: translate(5px, calc(-100% - 5px));\n  white-space: pre-wrap;\n  z-index: 99999;\n}\n</style>\n", "<!-- This component is used to bound the selected items on the canvas. -->\n<template>\n  <div\n    class=\"selection-overlay-container pointer-events-none z-40\"\n    :class=\"{\n      'show-border': showBorder\n    }\"\n    :style=\"style\"\n    v-show=\"visible\"\n  >\n    <slot></slot>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { createBounds } from '@comfyorg/litegraph'\nimport type { LGraphCanvas } from '@comfyorg/litegraph'\nimport { ref, watch } from 'vue'\n\nimport { useAbsolutePosition } from '@/composables/element/useAbsolutePosition'\nimport { useChainCallback } from '@/composables/functional/useChainCallback'\nimport { useCanvasStore } from '@/stores/graphStore'\n\nconst canvasStore = useCanvasStore()\nconst { style, updatePosition } = useAbsolutePosition()\n\nconst visible = ref(false)\nconst showBorder = ref(false)\n\nconst positionSelectionOverlay = (canvas: LGraphCanvas) => {\n  const selectedItems = canvas.selectedItems\n  showBorder.value = selectedItems.size > 1\n\n  if (!selectedItems.size) {\n    visible.value = false\n    return\n  }\n\n  visible.value = true\n  const bounds = createBounds(selectedItems)\n  if (bounds) {\n    updatePosition({\n      pos: [bounds[0], bounds[1]],\n      size: [bounds[2], bounds[3]]\n    })\n  }\n}\n\n// Register listener on canvas creation.\nwatch(\n  () => canvasStore.canvas as LGraphCanvas | null,\n  (canvas: LGraphCanvas | null) => {\n    if (!canvas) return\n\n    canvas.onSelectionChange = useChainCallback(canvas.onSelectionChange, () =>\n      positionSelectionOverlay(canvas)\n    )\n  },\n  { immediate: true }\n)\n\nwatch(\n  () => {\n    const canvas = canvasStore.canvas\n    if (!canvas) return null\n    return {\n      scale: canvas.ds.state.scale,\n      offset: [canvas.ds.state.offset[0], canvas.ds.state.offset[1]]\n    }\n  },\n  (state) => {\n    if (!state) return\n\n    positionSelectionOverlay(canvasStore.canvas as LGraphCanvas)\n  }\n)\n\nwatch(\n  () => canvasStore.canvas?.state?.draggingItems,\n  (draggingItems) => {\n    // Litegraph draggingItems state can end early before the bounding boxes of\n    // the selected items are updated. Delay to make sure we put the overlay in\n    // the correct position.\n    // https://github.com/Comfy-Org/ComfyUI_frontend/issues/2656\n    if (draggingItems === false) {\n      setTimeout(() => {\n        visible.value = true\n        positionSelectionOverlay(canvasStore.canvas as LGraphCanvas)\n      }, 100)\n    } else {\n      visible.value = false\n    }\n  }\n)\n</script>\n\n<style scoped>\n.selection-overlay-container > * {\n  pointer-events: auto;\n}\n\n.show-border {\n  @apply border-dashed rounded-md border-2 border-[var(--border-color)];\n}\n</style>\n", "<template>\n  <div class=\"relative\">\n    <Button\n      severity=\"secondary\"\n      text\n      @click=\"() => (showColorPicker = !showColorPicker)\"\n    >\n      <template #icon>\n        <div class=\"flex items-center gap-1\">\n          <i class=\"pi pi-circle-fill\" :style=\"{ color: currentColor ?? '' }\" />\n          <i class=\"pi pi-chevron-down\" :style=\"{ fontSize: '0.5rem' }\" />\n        </div>\n      </template>\n    </Button>\n    <div\n      v-if=\"showColorPicker\"\n      class=\"color-picker-container absolute -top-10 left-1/2\"\n    >\n      <SelectButton\n        :modelValue=\"selectedColorOption\"\n        @update:modelValue=\"applyColor\"\n        :options=\"colorOptions\"\n        optionLabel=\"name\"\n        dataKey=\"value\"\n      >\n        <template #option=\"{ option }\">\n          <i\n            class=\"pi pi-circle-fill\"\n            :style=\"{\n              color: isLightTheme ? option.value.light : option.value.dark\n            }\"\n            v-tooltip.top=\"option.localizedName\"\n            :data-testid=\"option.name\"\n          />\n        </template>\n      </SelectButton>\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport type { ColorOption as CanvasColorOption } from '@comfyorg/litegraph'\nimport { LGraphCanvas, LiteGraph, isColorable } from '@comfyorg/litegraph'\nimport Button from 'primevue/button'\nimport SelectButton from 'primevue/selectbutton'\nimport { computed, ref, watch } from 'vue'\nimport { useI18n } from 'vue-i18n'\n\nimport { useCanvasStore } from '@/stores/graphStore'\nimport { useColorPaletteStore } from '@/stores/workspace/colorPaletteStore'\nimport { adjustColor } from '@/utils/colorUtil'\nimport { getItemsColorOption } from '@/utils/litegraphUtil'\n\nconst { t } = useI18n()\nconst canvasStore = useCanvasStore()\nconst colorPaletteStore = useColorPaletteStore()\nconst isLightTheme = computed(\n  () => colorPaletteStore.completedActivePalette.light_theme\n)\nconst toLightThemeColor = (color: string) =>\n  adjustColor(color, { lightness: 0.5 })\n\nconst showColorPicker = ref(false)\n\ntype ColorOption = {\n  name: string\n  localizedName: string\n  value: {\n    dark: string\n    light: string\n  }\n}\n\nconst NO_COLOR_OPTION: ColorOption = {\n  name: 'noColor',\n  localizedName: t('color.noColor'),\n  value: {\n    dark: LiteGraph.NODE_DEFAULT_BGCOLOR,\n    light: toLightThemeColor(LiteGraph.NODE_DEFAULT_BGCOLOR)\n  }\n}\nconst colorOptions: ColorOption[] = [\n  NO_COLOR_OPTION,\n  ...Object.entries(LGraphCanvas.node_colors).map(([name, color]) => ({\n    name,\n    localizedName: t(`color.${name}`),\n    value: {\n      dark: color.bgcolor,\n      light: toLightThemeColor(color.bgcolor)\n    }\n  }))\n]\n\nconst selectedColorOption = ref<ColorOption | null>(null)\nconst applyColor = (colorOption: ColorOption | null) => {\n  const colorName = colorOption?.name ?? NO_COLOR_OPTION.name\n  const canvasColorOption =\n    colorName === NO_COLOR_OPTION.name\n      ? null\n      : LGraphCanvas.node_colors[colorName]\n\n  for (const item of canvasStore.selectedItems) {\n    if (isColorable(item)) {\n      item.setColorOption(canvasColorOption)\n    }\n  }\n\n  canvasStore.canvas?.setDirty(true, true)\n  currentColorOption.value = canvasColorOption\n  showColorPicker.value = false\n}\n\nconst currentColorOption = ref<CanvasColorOption | null>(null)\nconst currentColor = computed(() =>\n  currentColorOption.value\n    ? isLightTheme.value\n      ? toLightThemeColor(currentColorOption.value?.bgcolor)\n      : currentColorOption.value?.bgcolor\n    : null\n)\n\nwatch(\n  () => canvasStore.selectedItems,\n  (newSelectedItems) => {\n    showColorPicker.value = false\n    selectedColorOption.value = null\n    currentColorOption.value = getItemsColorOption(newSelectedItems)\n  }\n)\n</script>\n\n<style scoped>\n.color-picker-container {\n  transform: translateX(-50%);\n}\n\n:deep(.p-togglebutton) {\n  @apply py-2 px-1;\n}\n</style>\n", "import type { LGraphNode } from '@comfyorg/litegraph'\nimport type { IWidget } from '@comfyorg/litegraph'\nimport { computed, ref, watchEffect } from 'vue'\n\nimport { useCommandStore } from '@/stores/commandStore'\nimport { useCanvasStore } from '@/stores/graphStore'\nimport { isLGraphNode } from '@/utils/litegraphUtil'\n\ninterface RefreshableItem {\n  refresh: () => Promise<void> | void\n}\n\ntype RefreshableWidget = IWidget & RefreshableItem\n\nconst isRefreshableWidget = (widget: IWidget): widget is RefreshableWidget =>\n  'refresh' in widget && typeof widget.refresh === 'function'\n\n/**\n * Tracks selected nodes and their refreshable widgets\n */\nexport const useRefreshableSelection = () => {\n  const graphStore = useCanvasStore()\n  const commandStore = useCommandStore()\n  const selectedNodes = ref<LGraphNode[]>([])\n  const isAllNodesSelected = ref(false)\n\n  watchEffect(() => {\n    selectedNodes.value = graphStore.selectedItems.filter(isLGraphNode)\n    isAllNodesSelected.value =\n      graphStore.canvas?.graph?.nodes?.every((node) => !!node.selected) ?? false\n  })\n\n  const refreshableWidgets = computed(() =>\n    selectedNodes.value.flatMap(\n      (node) => node.widgets?.filter(isRefreshableWidget) ?? []\n    )\n  )\n\n  const isRefreshable = computed(\n    () => refreshableWidgets.value.length > 0 || isAllNodesSelected.value\n  )\n\n  async function refreshSelected() {\n    if (!isRefreshable.value) return\n\n    if (isAllNodesSelected.value) {\n      commandStore.execute('Comfy.RefreshNodeDefinitions')\n    } else {\n      await Promise.all(refreshableWidgets.value.map((item) => item.refresh()))\n    }\n  }\n\n  return {\n    isRefreshable,\n    refreshSelected\n  }\n}\n", "<template>\n  <Panel\n    class=\"selection-toolbox absolute left-1/2 rounded-lg\"\n    :pt=\"{\n      header: 'hidden',\n      content: 'p-0 flex flex-row'\n    }\"\n  >\n    <ColorPickerButton v-show=\"nodeSelected || groupSelected\" />\n    <Button\n      v-show=\"nodeSelected\"\n      severity=\"secondary\"\n      text\n      @click=\"\n        () => commandStore.execute('Comfy.Canvas.ToggleSelectedNodes.Bypass')\n      \"\n      data-testid=\"bypass-button\"\n      v-tooltip.top=\"{\n        value: t('commands.Comfy_Canvas_ToggleSelectedNodes_Bypass.label'),\n        showDelay: 1000\n      }\"\n    >\n      <template #icon>\n        <i-game-icons:detour />\n      </template>\n    </Button>\n    <Button\n      v-show=\"nodeSelected || groupSelected\"\n      severity=\"secondary\"\n      text\n      icon=\"pi pi-thumbtack\"\n      @click=\"() => commandStore.execute('Comfy.Canvas.ToggleSelected.Pin')\"\n      v-tooltip.top=\"{\n        value: t('commands.Comfy_Canvas_ToggleSelectedNodes_Pin.label'),\n        showDelay: 1000\n      }\"\n    />\n    <Button\n      severity=\"danger\"\n      text\n      icon=\"pi pi-trash\"\n      @click=\"() => commandStore.execute('Comfy.Canvas.DeleteSelectedItems')\"\n      v-tooltip.top=\"{\n        value: t('commands.Comfy_Canvas_DeleteSelectedItems.label'),\n        showDelay: 1000\n      }\"\n    />\n    <Button\n      v-show=\"isRefreshable\"\n      severity=\"info\"\n      text\n      icon=\"pi pi-refresh\"\n      @click=\"refreshSelected\"\n    />\n    <Button\n      v-for=\"command in extensionToolboxCommands\"\n      :key=\"command.id\"\n      severity=\"secondary\"\n      text\n      :icon=\"typeof command.icon === 'function' ? command.icon() : command.icon\"\n      @click=\"() => commandStore.execute(command.id)\"\n      v-tooltip.top=\"{\n        value:\n          st(`commands.${normalizeI18nKey(command.id)}.label`, '') || undefined,\n        showDelay: 1000\n      }\"\n    />\n  </Panel>\n</template>\n\n<script setup lang=\"ts\">\nimport Button from 'primevue/button'\nimport Panel from 'primevue/panel'\nimport { computed } from 'vue'\n\nimport ColorPickerButton from '@/components/graph/selectionToolbox/ColorPickerButton.vue'\nimport { useRefreshableSelection } from '@/composables/useRefreshableSelection'\nimport { st, t } from '@/i18n'\nimport { useExtensionService } from '@/services/extensionService'\nimport { ComfyCommand, useCommandStore } from '@/stores/commandStore'\nimport { useCanvasStore } from '@/stores/graphStore'\nimport { normalizeI18nKey } from '@/utils/formatUtil'\nimport { isLGraphGroup, isLGraphNode } from '@/utils/litegraphUtil'\n\nconst commandStore = useCommandStore()\nconst canvasStore = useCanvasStore()\nconst extensionService = useExtensionService()\nconst { isRefreshable, refreshSelected } = useRefreshableSelection()\nconst nodeSelected = computed(() =>\n  canvasStore.selectedItems.some(isLGraphNode)\n)\nconst groupSelected = computed(() =>\n  canvasStore.selectedItems.some(isLGraphGroup)\n)\n\nconst extensionToolboxCommands = computed<ComfyCommand[]>(() => {\n  const commandIds = new Set<string>(\n    canvasStore.selectedItems\n      .map(\n        (item) =>\n          extensionService\n            .invokeExtensions('getSelectionToolboxCommands', item)\n            .flat() as string[]\n      )\n      .flat()\n  )\n  return Array.from(commandIds)\n    .map((commandId) => commandStore.getCommand(commandId))\n    .filter((command) => command !== undefined)\n})\n</script>\n\n<style scoped>\n.selection-toolbox {\n  transform: translateX(-50%) translateY(-120%);\n}\n</style>\n", "<template>\n  <div\n    v-if=\"showInput\"\n    class=\"group-title-editor node-title-editor\"\n    :style=\"inputStyle\"\n  >\n    <EditableText\n      :isEditing=\"showInput\"\n      :modelValue=\"editedTitle\"\n      @edit=\"onEdit\"\n    />\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { LGraphGroup, LGraphNode, LiteGraph } from '@comfyorg/litegraph'\nimport type { LiteGraphCanvasEvent } from '@comfyorg/litegraph'\nimport { useEventListener } from '@vueuse/core'\nimport { ref, watch } from 'vue'\n\nimport EditableText from '@/components/common/EditableText.vue'\nimport { useAbsolutePosition } from '@/composables/element/useAbsolutePosition'\nimport { app } from '@/scripts/app'\nimport { useCanvasStore, useTitleEditorStore } from '@/stores/graphStore'\nimport { useSettingStore } from '@/stores/settingStore'\n\nconst settingStore = useSettingStore()\n\nconst showInput = ref(false)\nconst editedTitle = ref('')\nconst { style: inputStyle, updatePosition } = useAbsolutePosition()\n\nconst titleEditorStore = useTitleEditorStore()\nconst canvasStore = useCanvasStore()\nconst previousCanvasDraggable = ref(true)\n\nconst onEdit = (newValue: string) => {\n  if (titleEditorStore.titleEditorTarget && newValue.trim() !== '') {\n    titleEditorStore.titleEditorTarget.title = newValue.trim()\n    app.graph.setDirtyCanvas(true, true)\n  }\n  showInput.value = false\n  titleEditorStore.titleEditorTarget = null\n  canvasStore.canvas!.allow_dragcanvas = previousCanvasDraggable.value\n}\n\nwatch(\n  () => titleEditorStore.titleEditorTarget,\n  (target) => {\n    if (target === null) {\n      return\n    }\n    editedTitle.value = target.title\n    showInput.value = true\n    const canvas = canvasStore.canvas!\n    previousCanvasDraggable.value = canvas.allow_dragcanvas\n    canvas.allow_dragcanvas = false\n    const scale = canvas.ds.scale\n\n    if (target instanceof LGraphGroup) {\n      const group = target\n      updatePosition(\n        {\n          pos: group.pos,\n          size: [group.size[0], group.titleHeight]\n        },\n        { fontSize: `${group.font_size * scale}px` }\n      )\n    } else if (target instanceof LGraphNode) {\n      const node = target\n      const [x, y] = node.getBounding()\n      updatePosition(\n        {\n          pos: [x, y],\n          size: [node.width, LiteGraph.NODE_TITLE_HEIGHT]\n        },\n        { fontSize: `${12 * scale}px` }\n      )\n    }\n  }\n)\n\nconst canvasEventHandler = (event: LiteGraphCanvasEvent) => {\n  if (event.detail.subType === 'group-double-click') {\n    if (!settingStore.get('Comfy.Group.DoubleClickTitleToEdit')) {\n      return\n    }\n\n    const group: LGraphGroup = event.detail.group\n    const [_, y] = group.pos\n\n    const e = event.detail.originalEvent\n    const relativeY = e.canvasY - y\n    // Only allow editing if the click is on the title bar\n    if (relativeY <= group.titleHeight) {\n      titleEditorStore.titleEditorTarget = group\n    }\n  } else if (event.detail.subType === 'node-double-click') {\n    if (!settingStore.get('Comfy.Node.DoubleClickTitleToEdit')) {\n      return\n    }\n\n    const node: LGraphNode = event.detail.node\n    const [_, y] = node.pos\n\n    const e = event.detail.originalEvent\n    const relativeY = e.canvasY - y\n    // Only allow editing if the click is on the title bar\n    if (relativeY <= 0) {\n      titleEditorStore.titleEditorTarget = node\n    }\n  }\n}\n\nuseEventListener(document, 'litegraph:canvas', canvasEventHandler)\n</script>\n\n<style scoped>\n.group-title-editor.node-title-editor {\n  z-index: 9999;\n  padding: 0.25rem;\n}\n\n:deep(.editable-text) {\n  width: 100%;\n  height: 100%;\n}\n\n:deep(.editable-text input) {\n  width: 100%;\n  height: 100%;\n  /* Override the default font size */\n  font-size: inherit;\n}\n</style>\n", "import { defineStore } from 'pinia'\nimport { ref } from 'vue'\n\nexport const useSearchBoxStore = defineStore('searchBox', () => {\n  const visible = ref(false)\n  function toggleVisible() {\n    visible.value = !visible.value\n  }\n\n  return {\n    visible,\n    toggleVisible\n  }\n})\n", "import type {\n  ConnectingLink,\n  INodeInputSlot,\n  INodeOutputSlot,\n  INodeSlot,\n  ISlotType,\n  LGraphNode,\n  Vector2\n} from '@comfyorg/litegraph'\nimport { LiteGraph } from '@comfyorg/litegraph'\nimport { RerouteId } from '@comfyorg/litegraph/dist/Reroute'\n\nexport class ConnectingLinkImpl implements ConnectingLink {\n  constructor(\n    public node: LGraphNode,\n    public slot: number,\n    public input: INodeInputSlot | null | undefined | any,\n    public output: INodeOutputSlot | null | undefined | any,\n    public pos: Vector2,\n    public afterRerouteId?: RerouteId\n  ) {}\n\n  static createFromPlainObject(obj: ConnectingLink) {\n    return new ConnectingLinkImpl(\n      obj.node,\n      obj.slot,\n      obj.input,\n      obj.output,\n      obj.pos,\n      obj.afterRerouteId\n    )\n  }\n\n  get type(): ISlotType | null {\n    const result = this.input ? this.input.type : this.output?.type ?? null\n    return result === -1 ? null : result\n  }\n\n  /**\n   * Which slot type is release and need to be reconnected.\n   * - 'output' means we need a new node's outputs slot to connect with this link\n   */\n  get releaseSlotType(): 'input' | 'output' {\n    return this.output ? 'input' : 'output'\n  }\n\n  connectTo(newNode: LGraphNode) {\n    const newNodeSlots =\n      this.releaseSlotType === 'output' ? newNode.outputs : newNode.inputs\n    if (!newNodeSlots) return\n\n    const newNodeSlot = newNodeSlots.findIndex(\n      (slot: INodeSlot) =>\n        this.type && LiteGraph.isValidConnection(slot.type, this.type)\n    )\n\n    if (newNodeSlot === -1) {\n      console.warn(\n        `Could not find slot with type ${this.type} on node ${newNode.title}. This should never happen`\n      )\n      return\n    }\n\n    if (this.releaseSlotType === 'input') {\n      this.node.connect(this.slot, newNode, newNodeSlot, this.afterRerouteId)\n    } else {\n      newNode.connect(newNodeSlot, this.node, this.slot, this.afterRerouteId)\n    }\n  }\n}\n\nexport type CanvasDragAndDropData<T = any> = {\n  type: 'add-node'\n  data: T\n}\n", "export enum LinkReleaseTriggerMode {\n  ALWAYS = 'always',\n  HOLD_SHIFT = 'hold shift',\n  NOT_HOLD_SHIFT = 'NOT hold shift'\n}\n\nexport enum LinkReleaseTriggerAction {\n  CONTEXT_MENU = 'context menu',\n  SEARCH_BOX = 'search box',\n  NO_ACTION = 'no action'\n}\n", "<!-- Auto complete with extra event \"focused-option-changed\" -->\n<script>\nimport AutoComplete from 'primevue/autocomplete'\n\nexport default {\n  name: 'AutoCompletePlus',\n  extends: AutoComplete,\n  emits: ['focused-option-changed'],\n  data() {\n    return {\n      // Flag to determine if IME is active\n      isComposing: false\n    }\n  },\n  mounted() {\n    if (typeof AutoComplete.mounted === 'function') {\n      AutoComplete.mounted.call(this)\n    }\n\n    // Retrieve the actual <input> element and attach IME events\n    const inputEl = this.$el.querySelector('input')\n    if (inputEl) {\n      inputEl.addEventListener('compositionstart', () => {\n        this.isComposing = true\n      })\n      inputEl.addEventListener('compositionend', () => {\n        this.isComposing = false\n      })\n    }\n    // Add a watcher on the focusedOptionIndex property\n    this.$watch(\n      () => this.focusedOptionIndex,\n      (newVal, oldVal) => {\n        // Emit a custom event when focusedOptionIndex changes\n        this.$emit('focused-option-changed', newVal)\n      }\n    )\n  },\n  methods: {\n    // Override onKeyDown to block <PERSON><PERSON> when IME is active\n    onKeyDown(event) {\n      if (event.key === 'Enter' && this.isComposing) {\n        event.preventDefault()\n        event.stopPropagation()\n        return\n      }\n\n      AutoComplete.methods.onKeyDown.call(this, event)\n    }\n  }\n}\n</script>\n", "<template>\n  <div\n    class=\"option-container flex justify-between items-center px-2 py-0 cursor-pointer overflow-hidden w-full\"\n  >\n    <div class=\"option-display-name font-semibold flex flex-col\">\n      <div>\n        <span v-if=\"isBookmarked\">\n          <i class=\"pi pi-bookmark-fill text-sm mr-1\"></i>\n        </span>\n        <span\n          v-html=\"highlightQuery(nodeDef.display_name, currentQuery)\"\n        ></span>\n        <span>&nbsp;</span>\n        <Tag v-if=\"showIdName\" severity=\"secondary\">\n          <span v-html=\"highlightQuery(nodeDef.name, currentQuery)\"></span>\n        </Tag>\n      </div>\n      <div\n        v-if=\"showCategory\"\n        class=\"option-category font-light text-sm text-muted overflow-hidden text-ellipsis whitespace-nowrap\"\n      >\n        {{ nodeDef.category.replaceAll('/', ' > ') }}\n      </div>\n    </div>\n    <div class=\"option-badges\">\n      <Tag\n        v-if=\"nodeDef.experimental\"\n        :value=\"$t('g.experimental')\"\n        severity=\"primary\"\n      />\n      <Tag\n        v-if=\"nodeDef.deprecated\"\n        :value=\"$t('g.deprecated')\"\n        severity=\"danger\"\n      />\n      <Tag\n        v-if=\"showNodeFrequency && nodeFrequency > 0\"\n        :value=\"formatNumberWithSuffix(nodeFrequency, { roundToInt: true })\"\n        severity=\"secondary\"\n      />\n      <Chip\n        v-if=\"nodeDef.nodeSource.type !== NodeSourceType.Unknown\"\n        class=\"text-sm font-light\"\n      >\n        {{ nodeDef.nodeSource.displayText }}\n      </Chip>\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport Chip from 'primevue/chip'\nimport Tag from 'primevue/tag'\nimport { computed } from 'vue'\n\nimport { useNodeBookmarkStore } from '@/stores/nodeBookmarkStore'\nimport { ComfyNodeDefImpl, useNodeFrequencyStore } from '@/stores/nodeDefStore'\nimport { useSettingStore } from '@/stores/settingStore'\nimport { NodeSourceType } from '@/types/nodeSource'\nimport { highlightQuery } from '@/utils/formatUtil'\nimport { formatNumberWithSuffix } from '@/utils/formatUtil'\n\nconst settingStore = useSettingStore()\nconst showCategory = computed(() =>\n  settingStore.get('Comfy.NodeSearchBoxImpl.ShowCategory')\n)\nconst showIdName = computed(() =>\n  settingStore.get('Comfy.NodeSearchBoxImpl.ShowIdName')\n)\nconst showNodeFrequency = computed(() =>\n  settingStore.get('Comfy.NodeSearchBoxImpl.ShowNodeFrequency')\n)\nconst nodeFrequencyStore = useNodeFrequencyStore()\nconst nodeFrequency = computed(() =>\n  nodeFrequencyStore.getNodeFrequency(props.nodeDef)\n)\n\nconst nodeBookmarkStore = useNodeBookmarkStore()\nconst isBookmarked = computed(() =>\n  nodeBookmarkStore.isBookmarked(props.nodeDef)\n)\n\nconst props = defineProps<{\n  nodeDef: ComfyNodeDefImpl\n  currentQuery: string\n}>()\n</script>\n\n<style scoped>\n:deep(.highlight) {\n  background-color: var(--p-primary-color);\n  color: var(--p-primary-contrast-color);\n  font-weight: bold;\n  border-radius: 0.25rem;\n  padding: 0 0.125rem;\n  margin: -0.125rem 0.125rem;\n}\n</style>\n", "<template>\n  <div\n    class=\"comfy-vue-node-search-container flex justify-center items-center w-full min-w-96\"\n  >\n    <div\n      class=\"comfy-vue-node-preview-container absolute left-[-350px] top-[50px]\"\n      v-if=\"enableNodePreview\"\n    >\n      <NodePreview\n        :nodeDef=\"hoveredSuggestion\"\n        :key=\"hoveredSuggestion?.name || ''\"\n        v-if=\"hoveredSuggestion\"\n      />\n    </div>\n\n    <Button\n      icon=\"pi pi-filter\"\n      severity=\"secondary\"\n      class=\"filter-button z-10\"\n      @click=\"nodeSearchFilterVisible = true\"\n    />\n    <Dialog\n      v-model:visible=\"nodeSearchFilterVisible\"\n      class=\"min-w-96\"\n      dismissable-mask\n      modal\n      @hide=\"reFocusInput\"\n    >\n      <template #header>\n        <h3>Add node filter condition</h3>\n      </template>\n      <div class=\"_dialog-body\">\n        <NodeSearchFilter @addFilter=\"onAddFilter\"></NodeSearchFilter>\n      </div>\n    </Dialog>\n\n    <AutoCompletePlus\n      :model-value=\"filters\"\n      class=\"comfy-vue-node-search-box z-10 flex-grow\"\n      scrollHeight=\"40vh\"\n      :placeholder=\"placeholder\"\n      :input-id=\"inputId\"\n      append-to=\"self\"\n      :suggestions=\"suggestions\"\n      :min-length=\"0\"\n      :delay=\"100\"\n      :loading=\"!nodeFrequencyStore.isLoaded\"\n      @complete=\"search($event.query)\"\n      @option-select=\"emit('addNode', $event.value)\"\n      @focused-option-changed=\"setHoverSuggestion($event)\"\n      complete-on-focus\n      auto-option-focus\n      force-selection\n      multiple\n      :optionLabel=\"'display_name'\"\n    >\n      <template v-slot:option=\"{ option }\">\n        <NodeSearchItem :nodeDef=\"option\" :currentQuery=\"currentQuery\" />\n      </template>\n      <!-- FilterAndValue -->\n      <template v-slot:chip=\"{ value }\">\n        <SearchFilterChip\n          v-if=\"Array.isArray(value) && value.length === 2\"\n          :key=\"`${value[0].id}-${value[1]}`\"\n          @remove=\"onRemoveFilter($event, value as FilterAndValue)\"\n          :text=\"value[1]\"\n          :badge=\"value[0].invokeSequence.toUpperCase()\"\n          :badge-class=\"value[0].invokeSequence + '-badge'\"\n        />\n      </template>\n    </AutoCompletePlus>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport Button from 'primevue/button'\nimport Dialog from 'primevue/dialog'\nimport { computed, nextTick, onMounted, ref } from 'vue'\nimport { useI18n } from 'vue-i18n'\n\nimport NodePreview from '@/components/node/NodePreview.vue'\nimport AutoCompletePlus from '@/components/primevueOverride/AutoCompletePlus.vue'\nimport NodeSearchFilter from '@/components/searchbox/NodeSearchFilter.vue'\nimport NodeSearchItem from '@/components/searchbox/NodeSearchItem.vue'\nimport { type FilterAndValue } from '@/services/nodeSearchService'\nimport {\n  ComfyNodeDefImpl,\n  useNodeDefStore,\n  useNodeFrequencyStore\n} from '@/stores/nodeDefStore'\nimport { useSettingStore } from '@/stores/settingStore'\n\nimport SearchFilterChip from '../common/SearchFilterChip.vue'\n\nconst settingStore = useSettingStore()\nconst { t } = useI18n()\n\nconst enableNodePreview = computed(() =>\n  settingStore.get('Comfy.NodeSearchBoxImpl.NodePreview')\n)\n\nconst { filters, searchLimit = 64 } = defineProps<{\n  filters: FilterAndValue[]\n  searchLimit?: number\n}>()\n\nconst nodeSearchFilterVisible = ref(false)\nconst inputId = `comfy-vue-node-search-box-input-${Math.random()}`\nconst suggestions = ref<ComfyNodeDefImpl[]>([])\nconst hoveredSuggestion = ref<ComfyNodeDefImpl | null>(null)\nconst currentQuery = ref('')\nconst placeholder = computed(() => {\n  return filters.length === 0 ? t('g.searchNodes') + '...' : ''\n})\n\nconst nodeDefStore = useNodeDefStore()\nconst nodeFrequencyStore = useNodeFrequencyStore()\nconst search = (query: string) => {\n  const queryIsEmpty = query === '' && filters.length === 0\n  currentQuery.value = query\n  suggestions.value = queryIsEmpty\n    ? nodeFrequencyStore.topNodeDefs\n    : [\n        ...nodeDefStore.nodeSearchService.searchNode(query, filters, {\n          limit: searchLimit\n        })\n      ]\n}\n\nconst emit = defineEmits(['addFilter', 'removeFilter', 'addNode'])\n\nlet inputElement: HTMLInputElement | null = null\nconst reFocusInput = () => {\n  inputElement ??= document.getElementById(inputId) as HTMLInputElement\n  if (inputElement) {\n    inputElement.blur()\n    nextTick(() => inputElement?.focus())\n  }\n}\n\nonMounted(reFocusInput)\nconst onAddFilter = (filterAndValue: FilterAndValue) => {\n  nodeSearchFilterVisible.value = false\n  emit('addFilter', filterAndValue)\n}\nconst onRemoveFilter = (event: Event, filterAndValue: FilterAndValue) => {\n  event.stopPropagation()\n  event.preventDefault()\n  emit('removeFilter', filterAndValue)\n  reFocusInput()\n}\nconst setHoverSuggestion = (index: number) => {\n  if (index === -1) {\n    hoveredSuggestion.value = null\n    return\n  }\n  const value = suggestions.value[index]\n  hoveredSuggestion.value = value\n}\n</script>\n", "<template>\n  <div>\n    <Dialog\n      v-model:visible=\"visible\"\n      modal\n      :dismissable-mask=\"dismissable\"\n      @hide=\"clearFilters\"\n      :pt=\"{\n        root: {\n          class: 'invisible-dialog-root',\n          role: 'search'\n        },\n        mask: { class: 'node-search-box-dialog-mask' },\n        transition: {\n          enterFromClass: 'opacity-0 scale-75',\n          // 100ms is the duration of the transition in the dialog component\n          enterActiveClass: 'transition-all duration-100 ease-out',\n          leaveActiveClass: 'transition-all duration-100 ease-in',\n          leaveToClass: 'opacity-0 scale-75'\n        }\n      }\"\n    >\n      <template #container>\n        <NodeSearchBox\n          :filters=\"nodeFilters\"\n          @add-filter=\"addFilter\"\n          @remove-filter=\"removeFilter\"\n          @add-node=\"addNode\"\n        />\n      </template>\n    </Dialog>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { LiteGraph } from '@comfyorg/litegraph'\nimport type {\n  ConnectingLink,\n  LiteGraphCanvasEvent,\n  Vector2\n} from '@comfyorg/litegraph'\nimport type { OriginalEvent } from '@comfyorg/litegraph/dist/types/events'\nimport { useEventListener } from '@vueuse/core'\nimport { storeToRefs } from 'pinia'\nimport Dialog from 'primevue/dialog'\nimport { computed, ref, toRaw, watchEffect } from 'vue'\n\nimport { useLitegraphService } from '@/services/litegraphService'\nimport { FilterAndValue } from '@/services/nodeSearchService'\nimport { useCanvasStore } from '@/stores/graphStore'\nimport { ComfyNodeDefImpl, useNodeDefStore } from '@/stores/nodeDefStore'\nimport { useSettingStore } from '@/stores/settingStore'\nimport { useSearchBoxStore } from '@/stores/workspace/searchBoxStore'\nimport { ConnectingLinkImpl } from '@/types/litegraphTypes'\nimport { LinkReleaseTriggerAction } from '@/types/searchBoxTypes'\n\nimport NodeSearchBox from './NodeSearchBox.vue'\n\nconst settingStore = useSettingStore()\nconst litegraphService = useLitegraphService()\n\nconst { visible } = storeToRefs(useSearchBoxStore())\nconst dismissable = ref(true)\nconst triggerEvent = ref<LiteGraphCanvasEvent | null>(null)\nconst getNewNodeLocation = (): Vector2 => {\n  if (!triggerEvent.value) {\n    return litegraphService.getCanvasCenter()\n  }\n\n  const originalEvent = (triggerEvent.value.detail as OriginalEvent)\n    .originalEvent\n  return [originalEvent.canvasX, originalEvent.canvasY]\n}\nconst nodeFilters = ref<FilterAndValue[]>([])\nconst addFilter = (filter: FilterAndValue) => {\n  nodeFilters.value.push(filter)\n}\nconst removeFilter = (filter: FilterAndValue) => {\n  nodeFilters.value = nodeFilters.value.filter(\n    (f) => toRaw(f) !== toRaw(filter)\n  )\n}\nconst clearFilters = () => {\n  nodeFilters.value = []\n}\nconst closeDialog = () => {\n  visible.value = false\n}\n\nconst addNode = (nodeDef: ComfyNodeDefImpl) => {\n  const node = litegraphService.addNodeOnGraph(nodeDef, {\n    pos: getNewNodeLocation()\n  })\n\n  const eventDetail = triggerEvent.value?.detail\n  if (eventDetail && eventDetail.subType === 'empty-release') {\n    // @ts-expect-error fixme ts strict error\n    eventDetail.linkReleaseContext.links.forEach((link: ConnectingLink) => {\n      ConnectingLinkImpl.createFromPlainObject(link).connectTo(node)\n    })\n  }\n\n  // TODO: This is not robust timing-wise.\n  // PrimeVue complains about the dialog being closed before the event selecting\n  // item is fully processed.\n  window.setTimeout(() => {\n    closeDialog()\n  }, 100)\n}\n\nconst newSearchBoxEnabled = computed(\n  () => settingStore.get('Comfy.NodeSearchBoxImpl') === 'default'\n)\nconst showSearchBox = (e: LiteGraphCanvasEvent) => {\n  const detail = e.detail as OriginalEvent\n  if (newSearchBoxEnabled.value) {\n    if (detail.originalEvent?.pointerType === 'touch') {\n      setTimeout(() => {\n        showNewSearchBox(e)\n      }, 128)\n    } else {\n      showNewSearchBox(e)\n    }\n  } else {\n    // @ts-expect-error fixme ts strict error\n    canvasStore.canvas.showSearchBox(detail.originalEvent)\n  }\n}\n\nconst nodeDefStore = useNodeDefStore()\nconst showNewSearchBox = (e: LiteGraphCanvasEvent) => {\n  if (e.detail.subType === 'empty-release') {\n    const links = e.detail.linkReleaseContext.links\n    if (links.length === 0) {\n      console.warn('Empty release with no links! This should never happen')\n      return\n    }\n    const firstLink = ConnectingLinkImpl.createFromPlainObject(links[0])\n    const filter = nodeDefStore.nodeSearchService.getFilterById(\n      firstLink.releaseSlotType\n    )\n    // @ts-expect-error fixme ts strict error\n    const dataType = firstLink.type.toString()\n    // @ts-expect-error fixme ts strict error\n    addFilter([filter, dataType])\n  }\n\n  visible.value = true\n  triggerEvent.value = e\n\n  // Prevent the dialog from being dismissed immediately\n  dismissable.value = false\n  setTimeout(() => {\n    dismissable.value = true\n  }, 300)\n}\n\nconst showContextMenu = (e: LiteGraphCanvasEvent) => {\n  if (e.detail.subType !== 'empty-release') {\n    return\n  }\n\n  const links = e.detail.linkReleaseContext.links\n  if (links.length === 0) {\n    console.warn('Empty release with no links! This should never happen')\n    return\n  }\n\n  const firstLink = ConnectingLinkImpl.createFromPlainObject(links[0])\n  const mouseEvent = e.detail.originalEvent\n  const commonOptions = {\n    e: mouseEvent,\n    allow_searchbox: true,\n    showSearchBox: () => showSearchBox(e)\n  }\n  const connectionOptions = firstLink.output\n    ? {\n        nodeFrom: firstLink.node,\n        slotFrom: firstLink.output,\n        afterRerouteId: firstLink.afterRerouteId\n      }\n    : {\n        nodeTo: firstLink.node,\n        slotTo: firstLink.input,\n        afterRerouteId: firstLink.afterRerouteId\n      }\n  // @ts-expect-error fixme ts strict error\n  canvasStore.canvas.showConnectionMenu({\n    ...connectionOptions,\n    ...commonOptions\n  })\n}\n\n// Disable litegraph's default behavior of release link and search box.\nconst canvasStore = useCanvasStore()\nwatchEffect(() => {\n  if (canvasStore.canvas) {\n    LiteGraph.release_link_on_empty_shows_menu = false\n    canvasStore.canvas.allow_searchbox = false\n  }\n})\n\nconst canvasEventHandler = (e: LiteGraphCanvasEvent) => {\n  if (e.detail.subType === 'empty-double-click') {\n    showSearchBox(e)\n  } else if (e.detail.subType === 'empty-release') {\n    handleCanvasEmptyRelease(e)\n  } else if (e.detail.subType === 'group-double-click') {\n    const group = e.detail.group\n    const [_, y] = group.pos\n    const relativeY = e.detail.originalEvent.canvasY - y\n    // Show search box if the click is NOT on the title bar\n    if (relativeY > group.titleHeight) {\n      showSearchBox(e)\n    }\n  }\n}\n\nconst linkReleaseAction = computed(() => {\n  return settingStore.get('Comfy.LinkRelease.Action')\n})\n\nconst linkReleaseActionShift = computed(() => {\n  return settingStore.get('Comfy.LinkRelease.ActionShift')\n})\n\nconst handleCanvasEmptyRelease = (e: LiteGraphCanvasEvent) => {\n  const detail = e.detail as OriginalEvent\n  const shiftPressed = detail.originalEvent.shiftKey\n\n  const action = shiftPressed\n    ? linkReleaseActionShift.value\n    : linkReleaseAction.value\n  switch (action) {\n    case LinkReleaseTriggerAction.SEARCH_BOX:\n      showSearchBox(e)\n      break\n    case LinkReleaseTriggerAction.CONTEXT_MENU:\n      showContextMenu(e)\n      break\n    case LinkReleaseTriggerAction.NO_ACTION:\n    default:\n      break\n  }\n}\n\nuseEventListener(document, 'litegraph:canvas', canvasEventHandler)\n</script>\n\n<style>\n.invisible-dialog-root {\n  width: 60%;\n  min-width: 24rem;\n  max-width: 48rem;\n  border: 0 !important;\n  background-color: transparent !important;\n  margin-top: 25vh;\n  margin-left: 400px;\n}\n@media all and (max-width: 768px) {\n  .invisible-dialog-root {\n    margin-left: 0;\n  }\n}\n\n.node-search-box-dialog-mask {\n  align-items: flex-start !important;\n}\n</style>\n", "<template>\n  <Button\n    :class=\"props.class\"\n    text\n    :pt=\"{\n      root: {\n        class: `side-bar-button ${\n          props.selected\n            ? 'p-button-primary side-bar-button-selected'\n            : 'p-button-secondary'\n        }`,\n        'aria-label': props.tooltip\n      }\n    }\"\n    @click=\"emit('click', $event)\"\n    v-tooltip=\"{ value: props.tooltip, showDelay: 300, hideDelay: 300 }\"\n  >\n    <template #icon>\n      <OverlayBadge v-if=\"shouldShowBadge\" :value=\"overlayValue\">\n        <i :class=\"props.icon + ' side-bar-button-icon'\" />\n      </OverlayBadge>\n      <i v-else :class=\"props.icon + ' side-bar-button-icon'\" />\n    </template>\n  </Button>\n</template>\n\n<script setup lang=\"ts\">\nimport Button from 'primevue/button'\nimport OverlayBadge from 'primevue/overlaybadge'\nimport { PropType, computed } from 'vue'\n\n// Add this line to import PropsType\n\nconst props = defineProps({\n  icon: String,\n  selected: Boolean,\n  tooltip: {\n    type: String,\n    default: ''\n  },\n  class: {\n    type: String,\n    default: ''\n  },\n  iconBadge: {\n    type: [String, Function] as PropType<string | (() => string | null)>,\n    default: ''\n  }\n})\n\nconst emit = defineEmits(['click'])\nconst overlayValue = computed(() =>\n  typeof props.iconBadge === 'function'\n    ? props.iconBadge() || ''\n    : props.iconBadge\n)\nconst shouldShowBadge = computed(() => !!overlayValue.value)\n</script>\n\n<style>\n.side-bar-button-icon {\n  font-size: var(--sidebar-icon-size) !important;\n}\n\n.side-bar-button-selected .side-bar-button-icon {\n  font-size: var(--sidebar-icon-size) !important;\n  font-weight: bold;\n}\n</style>\n\n<style scoped>\n.side-bar-button {\n  width: var(--sidebar-width);\n  height: var(--sidebar-width);\n  border-radius: 0;\n}\n\n.comfyui-body-left .side-bar-button.side-bar-button-selected,\n.comfyui-body-left .side-bar-button.side-bar-button-selected:hover {\n  border-left: 4px solid var(--p-button-text-primary-color);\n}\n\n.comfyui-body-right .side-bar-button.side-bar-button-selected,\n.comfyui-body-right .side-bar-button.side-bar-button-selected:hover {\n  border-right: 4px solid var(--p-button-text-primary-color);\n}\n</style>\n", "<template>\n  <SidebarIcon icon=\"pi pi-sign-out\" :tooltip=\"tooltip\" @click=\"logout\" />\n</template>\n\n<script setup lang=\"ts\">\nimport { computed } from 'vue'\nimport { useI18n } from 'vue-i18n'\n\nimport { useUserStore } from '@/stores/userStore'\n\nimport SidebarIcon from './SidebarIcon.vue'\n\nconst { t } = useI18n()\nconst userStore = useUserStore()\n\nconst tooltip = computed(\n  () => `${t('sideToolbar.logout')} (${userStore.currentUser?.username})`\n)\nconst logout = () => {\n  userStore.logout()\n  window.location.reload()\n}\n</script>\n", "<template>\n  <SidebarIcon\n    icon=\"pi pi-cog\"\n    class=\"comfy-settings-btn\"\n    @click=\"showSetting\"\n    :tooltip=\"$t('g.settings')\"\n  />\n</template>\n\n<script setup lang=\"ts\">\nimport SettingDialogContent from '@/components/dialog/content/SettingDialogContent.vue'\nimport SettingDialogHeader from '@/components/dialog/header/SettingDialogHeader.vue'\nimport { useDialogStore } from '@/stores/dialogStore'\n\nimport SidebarIcon from './SidebarIcon.vue'\n\nconst dialogStore = useDialogStore()\nconst showSetting = () => {\n  dialogStore.showDialog({\n    key: 'global-settings',\n    headerComponent: SettingDialogHeader,\n    component: SettingDialogContent\n  })\n}\n</script>\n", "<template>\n  <SidebarIcon\n    :icon=\"icon\"\n    @click=\"toggleTheme\"\n    :tooltip=\"$t('sideToolbar.themeToggle')\"\n    class=\"comfy-vue-theme-toggle\"\n  />\n</template>\n\n<script setup lang=\"ts\">\nimport { computed } from 'vue'\n\nimport { useCommandStore } from '@/stores/commandStore'\nimport { useColorPaletteStore } from '@/stores/workspace/colorPaletteStore'\n\nimport SidebarIcon from './SidebarIcon.vue'\n\nconst colorPaletteStore = useColorPaletteStore()\nconst icon = computed(() =>\n  colorPaletteStore.completedActivePalette.light_theme\n    ? 'pi pi-sun'\n    : 'pi pi-moon'\n)\n\nconst commandStore = useCommandStore()\nconst toggleTheme = () => {\n  commandStore.execute('Comfy.ToggleTheme')\n}\n</script>\n", "<template>\n  <teleport :to=\"teleportTarget\">\n    <nav class=\"side-tool-bar-container\" :class=\"{ 'small-sidebar': isSmall }\">\n      <SidebarIcon\n        v-for=\"tab in tabs\"\n        :key=\"tab.id\"\n        :icon=\"tab.icon\"\n        :iconBadge=\"tab.iconBadge\"\n        :tooltip=\"tab.tooltip + getTabTooltipSuffix(tab)\"\n        :selected=\"tab.id === selectedTab?.id\"\n        :class=\"tab.id + '-tab-button'\"\n        @click=\"onTabClick(tab)\"\n      />\n      <div class=\"side-tool-bar-end\">\n        <SidebarLogoutIcon v-if=\"userStore.isMultiUserServer\" />\n        <SidebarThemeToggleIcon />\n        <SidebarSettingsToggleIcon />\n      </div>\n    </nav>\n  </teleport>\n  <div\n    v-if=\"selectedTab\"\n    class=\"sidebar-content-container h-full overflow-y-auto overflow-x-hidden\"\n  >\n    <ExtensionSlot :extension=\"selectedTab\" />\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { computed } from 'vue'\n\nimport ExtensionSlot from '@/components/common/ExtensionSlot.vue'\nimport { useKeybindingStore } from '@/stores/keybindingStore'\nimport { useSettingStore } from '@/stores/settingStore'\nimport { useUserStore } from '@/stores/userStore'\nimport { useWorkspaceStore } from '@/stores/workspaceStore'\nimport type { SidebarTabExtension } from '@/types/extensionTypes'\n\nimport SidebarIcon from './SidebarIcon.vue'\nimport SidebarLogoutIcon from './SidebarLogoutIcon.vue'\nimport SidebarSettingsToggleIcon from './SidebarSettingsToggleIcon.vue'\nimport SidebarThemeToggleIcon from './SidebarThemeToggleIcon.vue'\n\nconst workspaceStore = useWorkspaceStore()\nconst settingStore = useSettingStore()\nconst userStore = useUserStore()\n\nconst teleportTarget = computed(() =>\n  settingStore.get('Comfy.Sidebar.Location') === 'left'\n    ? '.comfyui-body-left'\n    : '.comfyui-body-right'\n)\n\nconst isSmall = computed(\n  () => settingStore.get('Comfy.Sidebar.Size') === 'small'\n)\n\nconst tabs = computed(() => workspaceStore.getSidebarTabs())\nconst selectedTab = computed(() => workspaceStore.sidebarTab.activeSidebarTab)\nconst onTabClick = (item: SidebarTabExtension) => {\n  workspaceStore.sidebarTab.toggleSidebarTab(item.id)\n}\nconst keybindingStore = useKeybindingStore()\nconst getTabTooltipSuffix = (tab: SidebarTabExtension) => {\n  const keybinding = keybindingStore.getKeybindingByCommandId(\n    `Workspace.ToggleSidebarTab.${tab.id}`\n  )\n  return keybinding ? ` (${keybinding.combo.toString()})` : ''\n}\n</script>\n\n<style scoped>\n.side-tool-bar-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n\n  width: var(--sidebar-width);\n  height: 100%;\n\n  background-color: var(--comfy-menu-secondary-bg);\n  color: var(--fg-color);\n  box-shadow: var(--bar-shadow);\n\n  --sidebar-width: 4rem;\n  --sidebar-icon-size: 1.5rem;\n}\n\n.side-tool-bar-container.small-sidebar {\n  --sidebar-width: 2.5rem;\n  --sidebar-icon-size: 1rem;\n}\n\n.side-tool-bar-end {\n  align-self: flex-end;\n  margin-top: auto;\n}\n</style>\n", "<template>\n  <div class=\"flex p-2 gap-2 workflow-tab\" ref=\"workflowTabRef\" v-bind=\"$attrs\">\n    <span\n      class=\"workflow-label text-sm max-w-[150px] truncate inline-block\"\n      v-tooltip.bottom=\"workflowOption.workflow.key\"\n    >\n      {{ workflowOption.workflow.filename }}\n    </span>\n    <div class=\"relative\">\n      <span\n        class=\"status-indicator\"\n        v-if=\"\n          !workspaceStore.shiftDown &&\n          (workflowOption.workflow.isModified ||\n            !workflowOption.workflow.isPersisted)\n        \"\n        >•</span\n      >\n      <Button\n        class=\"close-button p-0 w-auto\"\n        icon=\"pi pi-times\"\n        text\n        severity=\"secondary\"\n        size=\"small\"\n        @click.stop=\"onCloseWorkflow(workflowOption)\"\n      />\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport Button from 'primevue/button'\nimport { ref } from 'vue'\nimport { useI18n } from 'vue-i18n'\n\nimport {\n  usePragmaticDraggable,\n  usePragmaticDroppable\n} from '@/composables/usePragmaticDragAndDrop'\nimport { useWorkflowService } from '@/services/workflowService'\nimport { ComfyWorkflow } from '@/stores/workflowStore'\nimport { useWorkflowStore } from '@/stores/workflowStore'\nimport { useWorkspaceStore } from '@/stores/workspaceStore'\n\ninterface WorkflowOption {\n  value: string\n  workflow: ComfyWorkflow\n}\n\nconst props = defineProps<{\n  class?: string\n  workflowOption: WorkflowOption\n}>()\n\nconst { t } = useI18n()\n\nconst workspaceStore = useWorkspaceStore()\nconst workflowStore = useWorkflowStore()\nconst workflowTabRef = ref<HTMLElement | null>(null)\n\nconst closeWorkflows = async (options: WorkflowOption[]) => {\n  for (const opt of options) {\n    if (\n      !(await useWorkflowService().closeWorkflow(opt.workflow, {\n        warnIfUnsaved: !workspaceStore.shiftDown,\n        hint: t('sideToolbar.workflowTab.dirtyCloseHint')\n      }))\n    ) {\n      // User clicked cancel\n      break\n    }\n  }\n}\n\nconst onCloseWorkflow = (option: WorkflowOption) => {\n  closeWorkflows([option])\n}\nconst tabGetter = () => workflowTabRef.value as HTMLElement\n\nusePragmaticDraggable(tabGetter, {\n  getInitialData: () => {\n    return {\n      workflowKey: props.workflowOption.workflow.key\n    }\n  }\n})\n\nusePragmaticDroppable(tabGetter, {\n  getData: () => {\n    return {\n      workflowKey: props.workflowOption.workflow.key\n    }\n  },\n  onDrop: (e) => {\n    const fromIndex = workflowStore.openWorkflows.findIndex(\n      (wf) => wf.key === e.source.data.workflowKey\n    )\n    const toIndex = workflowStore.openWorkflows.findIndex(\n      (wf) => wf.key === e.location.current.dropTargets[0]?.data.workflowKey\n    )\n    if (fromIndex !== toIndex) {\n      workflowStore.reorderWorkflows(fromIndex, toIndex)\n    }\n  }\n})\n</script>\n\n<style scoped>\n.status-indicator {\n  @apply absolute font-bold;\n  font-size: 1.5rem;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n}\n</style>\n", "<template>\n  <div class=\"workflow-tabs-container flex flex-row max-w-full h-full\">\n    <ScrollPanel\n      class=\"overflow-hidden no-drag\"\n      :pt:content=\"{\n        class: 'p-0 w-full',\n        onwheel: handleWheel\n      }\"\n      pt:barX=\"h-1\"\n    >\n      <SelectButton\n        class=\"workflow-tabs bg-transparent\"\n        :class=\"props.class\"\n        :modelValue=\"selectedWorkflow\"\n        @update:modelValue=\"onWorkflowChange\"\n        :options=\"options\"\n        optionLabel=\"label\"\n        dataKey=\"value\"\n      >\n        <template #option=\"{ option }\">\n          <WorkflowTab\n            @contextmenu=\"showContextMenu($event, option)\"\n            @click.middle=\"onCloseWorkflow(option)\"\n            :workflow-option=\"option\"\n          />\n        </template>\n      </SelectButton>\n    </ScrollPanel>\n    <Button\n      v-tooltip=\"{ value: $t('sideToolbar.newBlankWorkflow'), showDelay: 300 }\"\n      class=\"new-blank-workflow-button flex-shrink-0 no-drag\"\n      icon=\"pi pi-plus\"\n      text\n      severity=\"secondary\"\n      :aria-label=\"$t('sideToolbar.newBlankWorkflow')\"\n      @click=\"() => commandStore.execute('Comfy.NewBlankWorkflow')\"\n    />\n    <ContextMenu ref=\"menu\" :model=\"contextMenuItems\" />\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport Button from 'primevue/button'\nimport ContextMenu from 'primevue/contextmenu'\nimport ScrollPanel from 'primevue/scrollpanel'\nimport SelectButton from 'primevue/selectbutton'\nimport { computed, ref } from 'vue'\nimport { useI18n } from 'vue-i18n'\n\nimport WorkflowTab from '@/components/topbar/WorkflowTab.vue'\nimport { useWorkflowService } from '@/services/workflowService'\nimport { useCommandStore } from '@/stores/commandStore'\nimport { ComfyWorkflow, useWorkflowBookmarkStore } from '@/stores/workflowStore'\nimport { useWorkflowStore } from '@/stores/workflowStore'\nimport { useWorkspaceStore } from '@/stores/workspaceStore'\n\ninterface WorkflowOption {\n  value: string\n  workflow: ComfyWorkflow\n}\n\nconst props = defineProps<{\n  class?: string\n}>()\n\nconst { t } = useI18n()\nconst workspaceStore = useWorkspaceStore()\nconst workflowStore = useWorkflowStore()\nconst workflowService = useWorkflowService()\nconst workflowBookmarkStore = useWorkflowBookmarkStore()\nconst rightClickedTab = ref<WorkflowOption | undefined>()\nconst menu = ref()\n\nconst workflowToOption = (workflow: ComfyWorkflow): WorkflowOption => ({\n  value: workflow.path,\n  workflow\n})\n\nconst options = computed<WorkflowOption[]>(() =>\n  workflowStore.openWorkflows.map(workflowToOption)\n)\nconst selectedWorkflow = computed<WorkflowOption | null>(() =>\n  workflowStore.activeWorkflow\n    ? workflowToOption(workflowStore.activeWorkflow as ComfyWorkflow)\n    : null\n)\nconst onWorkflowChange = (option: WorkflowOption) => {\n  // Prevent unselecting the current workflow\n  if (!option) {\n    return\n  }\n  // Prevent reloading the current workflow\n  if (selectedWorkflow.value?.value === option.value) {\n    return\n  }\n\n  workflowService.openWorkflow(option.workflow)\n}\n\nconst closeWorkflows = async (options: WorkflowOption[]) => {\n  for (const opt of options) {\n    if (\n      !(await workflowService.closeWorkflow(opt.workflow, {\n        warnIfUnsaved: !workspaceStore.shiftDown\n      }))\n    ) {\n      // User clicked cancel\n      break\n    }\n  }\n}\n\nconst onCloseWorkflow = (option: WorkflowOption) => {\n  closeWorkflows([option])\n}\n\nconst showContextMenu = (event: MouseEvent, option: WorkflowOption) => {\n  rightClickedTab.value = option\n  menu.value.show(event)\n}\nconst contextMenuItems = computed(() => {\n  const tab = rightClickedTab.value as WorkflowOption\n  if (!tab) return []\n  const index = options.value.findIndex((v) => v.workflow === tab.workflow)\n\n  return [\n    {\n      label: t('tabMenu.duplicateTab'),\n      command: () => {\n        workflowService.duplicateWorkflow(tab.workflow)\n      }\n    },\n    {\n      separator: true\n    },\n    {\n      label: t('tabMenu.closeTab'),\n      command: () => onCloseWorkflow(tab)\n    },\n    {\n      label: t('tabMenu.closeTabsToLeft'),\n      command: () => closeWorkflows(options.value.slice(0, index)),\n      disabled: index <= 0\n    },\n    {\n      label: t('tabMenu.closeTabsToRight'),\n      command: () => closeWorkflows(options.value.slice(index + 1)),\n      disabled: index === options.value.length - 1\n    },\n    {\n      label: t('tabMenu.closeOtherTabs'),\n      command: () =>\n        closeWorkflows([\n          ...options.value.slice(index + 1),\n          ...options.value.slice(0, index)\n        ]),\n      disabled: options.value.length <= 1\n    },\n    {\n      label: workflowBookmarkStore.isBookmarked(tab.workflow.path)\n        ? t('tabMenu.removeFromBookmarks')\n        : t('tabMenu.addToBookmarks'),\n      command: () => workflowBookmarkStore.toggleBookmarked(tab.workflow.path),\n      disabled: tab.workflow.isTemporary\n    }\n  ]\n})\nconst commandStore = useCommandStore()\n\n// Horizontal scroll on wheel\nconst handleWheel = (event: WheelEvent) => {\n  const scrollElement = event.currentTarget as HTMLElement\n  const scrollAmount = event.deltaX || event.deltaY\n  scrollElement.scroll({\n    left: scrollElement.scrollLeft + scrollAmount\n  })\n}\n</script>\n\n<style scoped>\n:deep(.p-togglebutton) {\n  @apply p-0 bg-transparent rounded-none flex-shrink-0 relative border-0 border-r border-solid;\n  border-right-color: var(--border-color);\n}\n\n:deep(.p-togglebutton::before) {\n  @apply hidden;\n}\n\n:deep(.p-togglebutton:first-child) {\n  @apply border-l border-solid;\n  border-left-color: var(--border-color);\n}\n\n:deep(.p-togglebutton:not(:first-child)) {\n  @apply border-l-0;\n}\n\n:deep(.p-togglebutton.p-togglebutton-checked) {\n  @apply border-b border-solid h-full;\n  border-bottom-color: var(--p-button-text-primary-color);\n}\n\n:deep(.p-togglebutton:not(.p-togglebutton-checked)) {\n  @apply opacity-75;\n}\n\n:deep(.p-togglebutton-checked) .close-button,\n:deep(.p-togglebutton:hover) .close-button {\n  @apply visible;\n}\n\n:deep(.p-togglebutton:hover) .status-indicator {\n  @apply hidden;\n}\n\n:deep(.p-togglebutton) .close-button {\n  @apply invisible;\n}\n\n:deep(.p-scrollpanel-content) {\n  @apply h-full;\n}\n\n/* Scrollbar half opacity to avoid blocking the active tab bottom border */\n:deep(.p-scrollpanel:hover .p-scrollpanel-bar),\n:deep(.p-scrollpanel:active .p-scrollpanel-bar) {\n  @apply opacity-50;\n}\n\n:deep(.p-selectbutton) {\n  @apply rounded-none h-full;\n}\n</style>\n", "import { LGraphNode } from '@comfyorg/litegraph'\nimport { LiteGraph } from '@comfyorg/litegraph'\nimport { Ref } from 'vue'\n\nimport { usePragmaticDroppable } from '@/composables/usePragmaticDragAndDrop'\nimport { app as comfyApp } from '@/scripts/app'\nimport { useLitegraphService } from '@/services/litegraphService'\nimport { useWorkflowService } from '@/services/workflowService'\nimport { ComfyModelDef } from '@/stores/modelStore'\nimport { ModelNodeProvider } from '@/stores/modelToNodeStore'\nimport { useModelToNodeStore } from '@/stores/modelToNodeStore'\nimport { ComfyNodeDefImpl } from '@/stores/nodeDefStore'\nimport { ComfyWorkflow } from '@/stores/workflowStore'\nimport { RenderedTreeExplorerNode } from '@/types/treeExplorerTypes'\n\nexport const useCanvasDrop = (canvasRef: Ref<HTMLCanvasElement>) => {\n  const modelToNodeStore = useModelToNodeStore()\n  const litegraphService = useLitegraphService()\n  const workflowService = useWorkflowService()\n\n  usePragmaticDroppable(() => canvasRef.value, {\n    getDropEffect: (args): Exclude<DataTransfer['dropEffect'], 'none'> =>\n      args.source.data.type === 'tree-explorer-node' ? 'copy' : 'move',\n    onDrop: (event) => {\n      const loc = event.location.current.input\n      const dndData = event.source.data\n\n      if (dndData.type === 'tree-explorer-node') {\n        const node = dndData.data as RenderedTreeExplorerNode\n        if (node.data instanceof ComfyNodeDefImpl) {\n          const nodeDef = node.data\n          // Add an offset on x to make sure after adding the node, the cursor\n          // is on the node (top left corner)\n          const pos = comfyApp.clientPosToCanvasPos([\n            loc.clientX,\n            loc.clientY + LiteGraph.NODE_TITLE_HEIGHT\n          ])\n          litegraphService.addNodeOnGraph(nodeDef, { pos })\n        } else if (node.data instanceof ComfyModelDef) {\n          const model = node.data\n          const pos = comfyApp.clientPosToCanvasPos([loc.clientX, loc.clientY])\n          const nodeAtPos = comfyApp.graph.getNodeOnPos(pos[0], pos[1])\n          let targetProvider: ModelNodeProvider | null = null\n          let targetGraphNode: LGraphNode | null = null\n          if (nodeAtPos) {\n            const providers = modelToNodeStore.getAllNodeProviders(\n              model.directory\n            )\n            for (const provider of providers) {\n              if (provider.nodeDef.name === nodeAtPos.comfyClass) {\n                targetGraphNode = nodeAtPos\n                targetProvider = provider\n              }\n            }\n          }\n          if (!targetGraphNode) {\n            const provider = modelToNodeStore.getNodeProvider(model.directory)\n            if (provider) {\n              targetGraphNode = litegraphService.addNodeOnGraph(\n                provider.nodeDef,\n                {\n                  pos\n                }\n              )\n              targetProvider = provider\n            }\n          }\n          if (targetGraphNode) {\n            const widget = targetGraphNode.widgets?.find(\n              (widget) => widget.name === targetProvider?.key\n            )\n            if (widget) {\n              widget.value = model.file_name\n            }\n          }\n        } else if (node.data instanceof ComfyWorkflow) {\n          const workflow = node.data\n          const position = comfyApp.clientPosToCanvasPos([\n            loc.clientX,\n            loc.clientY\n          ])\n          workflowService.insertWorkflow(workflow, { position })\n        }\n      }\n    }\n  })\n}\n", "import type {\n  IContextMenuOptions,\n  IContextMenuValue,\n  INodeInputSlot,\n  IWidget\n} from '@comfyorg/litegraph'\nimport { LGraphCanvas, LiteGraph } from '@comfyorg/litegraph'\n\nimport { st, te } from '@/i18n'\nimport { normalizeI18nKey } from '@/utils/formatUtil'\n\n/**\n * Add translation for litegraph context menu.\n */\nexport const useContextMenuTranslation = () => {\n  const f = LGraphCanvas.prototype.getCanvasMenuOptions\n  const getCanvasCenterMenuOptions = function (\n    this: LGraphCanvas,\n    ...args: Parameters<typeof f>\n  ) {\n    const res = f.apply(this, args) as ReturnType<typeof f>\n    for (const item of res) {\n      if (item?.content) {\n        item.content = st(`contextMenu.${item.content}`, item.content)\n      }\n    }\n    return res\n  }\n\n  LGraphCanvas.prototype.getCanvasMenuOptions = getCanvasCenterMenuOptions\n\n  function translateMenus(\n    values: readonly (IContextMenuValue | string | null)[] | undefined,\n    options: IContextMenuOptions\n  ) {\n    if (!values) return\n    const reInput = /Convert (.*) to input/\n    const reWidget = /Convert (.*) to widget/\n    const cvt = st('contextMenu.Convert ', 'Convert ')\n    const tinp = st('contextMenu. to input', ' to input')\n    const twgt = st('contextMenu. to widget', ' to widget')\n    for (const value of values) {\n      if (typeof value === 'string') continue\n\n      translateMenus(value?.submenu?.options, options)\n      if (!value?.content) {\n        continue\n      }\n      if (te(`contextMenu.${value.content}`)) {\n        value.content = st(`contextMenu.${value.content}`, value.content)\n      }\n\n      // for capture translation text of input and widget\n      const extraInfo: any = options.extra || options.parentMenu?.options?.extra\n      // widgets and inputs\n      const matchInput = value.content?.match(reInput)\n      if (matchInput) {\n        let match = matchInput[1]\n        extraInfo?.inputs?.find((i: INodeInputSlot) => {\n          if (i.name != match) return false\n          match = i.label ? i.label : i.name\n        })\n        extraInfo?.widgets?.find((i: IWidget) => {\n          if (i.name != match) return false\n          match = i.label ? i.label : i.name\n        })\n        value.content = cvt + match + tinp\n        continue\n      }\n      const matchWidget = value.content?.match(reWidget)\n      if (matchWidget) {\n        let match = matchWidget[1]\n        extraInfo?.inputs?.find((i: INodeInputSlot) => {\n          if (i.name != match) return false\n          match = i.label ? i.label : i.name\n        })\n        extraInfo?.widgets?.find((i: IWidget) => {\n          if (i.name != match) return false\n          match = i.label ? i.label : i.name\n        })\n        value.content = cvt + match + twgt\n        continue\n      }\n    }\n  }\n\n  const OriginalContextMenu = LiteGraph.ContextMenu\n  function ContextMenu(\n    values: (IContextMenuValue | string)[],\n    options: IContextMenuOptions\n  ) {\n    if (options.title) {\n      options.title = st(\n        `nodeDefs.${normalizeI18nKey(options.title)}.display_name`,\n        options.title\n      )\n    }\n    translateMenus(values, options)\n    const ctx = new OriginalContextMenu(values, options)\n    return ctx\n  }\n\n  LiteGraph.ContextMenu = ContextMenu as unknown as typeof LiteGraph.ContextMenu\n  LiteGraph.ContextMenu.prototype = OriginalContextMenu.prototype\n}\n", "import { useEventListener } from '@vueuse/core'\n\nimport { useCanvasStore } from '@/stores/graphStore'\n\n/**\n * Adds a handler on copy that serializes selected nodes to JSON\n */\nexport const useCopy = () => {\n  const canvasStore = useCanvasStore()\n\n  useEventListener(document, 'copy', (e) => {\n    if (!(e.target instanceof Element)) {\n      return\n    }\n    if (\n      (e.target instanceof HTMLTextAreaElement &&\n        e.target.type === 'textarea') ||\n      (e.target instanceof HTMLInputElement && e.target.type === 'text')\n    ) {\n      // Default system copy\n      return\n    }\n    const isTargetInGraph =\n      e.target.classList.contains('litegraph') ||\n      e.target.classList.contains('graph-canvas-container') ||\n      e.target.id === 'graph-canvas'\n\n    // copy nodes and clear clipboard\n    const canvas = canvasStore.canvas\n    if (isTargetInGraph && canvas?.selectedItems) {\n      canvas.copyToClipboard()\n      // clearData doesn't remove images from clipboard\n      e.clipboardData?.setData('text', ' ')\n      e.preventDefault()\n      e.stopImmediatePropagation()\n      return false\n    }\n  })\n}\n", "import {\n  ContextMenu,\n  DragAndScale,\n  LGraph,\n  LGraphBadge,\n  LGraphCanvas,\n  LGraphGroup,\n  LGraphNode,\n  LLink,\n  LiteGraph\n} from '@comfyorg/litegraph'\n\n/**\n * Assign all properties of LiteGraph to window to make it backward compatible.\n */\nexport const useGlobalLitegraph = () => {\n  // @ts-expect-error fixme ts strict error\n  window['LiteGraph'] = LiteGraph\n  // @ts-expect-error fixme ts strict error\n  window['LGraph'] = LGraph\n  // @ts-expect-error fixme ts strict error\n  window['LLink'] = LLink\n  // @ts-expect-error fixme ts strict error\n  window['LGraphNode'] = LGraphNode\n  // @ts-expect-error fixme ts strict error\n  window['LGraphGroup'] = LGraphGroup\n  // @ts-expect-error fixme ts strict error\n  window['DragAndScale'] = DragAndScale\n  // @ts-expect-error fixme ts strict error\n  window['LGraphCanvas'] = LGraphCanvas\n  // @ts-expect-error fixme ts strict error\n  window['ContextMenu'] = ContextMenu\n  // @ts-expect-error fixme ts strict error\n  window['LGraphBadge'] = LGraphBadge\n}\n", "import { Canvas<PERSON>ointer, LGraphNode, LiteGraph } from '@comfyorg/litegraph'\nimport { watchEffect } from 'vue'\n\nimport { useCanvasStore } from '@/stores/graphStore'\nimport { useSettingStore } from '@/stores/settingStore'\n\n/**\n * Watch for changes in the setting store and update the LiteGraph settings accordingly.\n */\nexport const useLitegraphSettings = () => {\n  const settingStore = useSettingStore()\n  const canvasStore = useCanvasStore()\n\n  watchEffect(() => {\n    const canvasInfoEnabled = settingStore.get('Comfy.Graph.CanvasInfo')\n    if (canvasStore.canvas) {\n      canvasStore.canvas.show_info = canvasInfoEnabled\n    }\n  })\n\n  watchEffect(() => {\n    const zoomSpeed = settingStore.get('Comfy.Graph.ZoomSpeed')\n    if (canvasStore.canvas) {\n      canvasStore.canvas.zoom_speed = zoomSpeed\n    }\n  })\n\n  watchEffect(() => {\n    LiteGraph.snaps_for_comfy = settingStore.get(\n      'Comfy.Node.AutoSnapLinkToSlot'\n    )\n  })\n\n  watchEffect(() => {\n    LiteGraph.snap_highlights_node = settingStore.get(\n      'Comfy.Node.SnapHighlightsNode'\n    )\n  })\n\n  watchEffect(() => {\n    LGraphNode.keepAllLinksOnBypass = settingStore.get(\n      'Comfy.Node.BypassAllLinksOnDelete'\n    )\n  })\n\n  watchEffect(() => {\n    LiteGraph.middle_click_slot_add_default_node = settingStore.get(\n      'Comfy.Node.MiddleClickRerouteNode'\n    )\n  })\n\n  watchEffect(() => {\n    const linkRenderMode = settingStore.get('Comfy.LinkRenderMode')\n    if (canvasStore.canvas) {\n      canvasStore.canvas.links_render_mode = linkRenderMode\n      canvasStore.canvas.setDirty(/* fg */ false, /* bg */ true)\n    }\n  })\n\n  watchEffect(() => {\n    const lowQualityRenderingZoomThreshold = settingStore.get(\n      'LiteGraph.Canvas.LowQualityRenderingZoomThreshold'\n    )\n    if (canvasStore.canvas) {\n      canvasStore.canvas.low_quality_zoom_threshold =\n        lowQualityRenderingZoomThreshold\n      canvasStore.canvas.setDirty(/* fg */ true, /* bg */ true)\n    }\n  })\n\n  watchEffect(() => {\n    const linkMarkerShape = settingStore.get('Comfy.Graph.LinkMarkers')\n    const { canvas } = canvasStore\n    if (canvas) {\n      canvas.linkMarkerShape = linkMarkerShape\n      canvas.setDirty(false, true)\n    }\n  })\n\n  watchEffect(() => {\n    const maximumFps = settingStore.get('LiteGraph.Canvas.MaximumFps')\n    const { canvas } = canvasStore\n    if (canvas) canvas.maximumFps = maximumFps\n  })\n\n  watchEffect(() => {\n    const dragZoomEnabled = settingStore.get('Comfy.Graph.CtrlShiftZoom')\n    const { canvas } = canvasStore\n    if (canvas) canvas.dragZoomEnabled = dragZoomEnabled\n  })\n\n  watchEffect(() => {\n    CanvasPointer.doubleClickTime = settingStore.get(\n      'Comfy.Pointer.DoubleClickTime'\n    )\n  })\n\n  watchEffect(() => {\n    CanvasPointer.bufferTime = settingStore.get('Comfy.Pointer.ClickBufferTime')\n  })\n\n  watchEffect(() => {\n    CanvasPointer.maxClickDrift = settingStore.get('Comfy.Pointer.ClickDrift')\n  })\n\n  watchEffect(() => {\n    LiteGraph.CANVAS_GRID_SIZE = settingStore.get('Comfy.SnapToGrid.GridSize')\n  })\n\n  watchEffect(() => {\n    LiteGraph.alwaysSnapToGrid = settingStore.get('pysssss.SnapToGrid')\n  })\n\n  watchEffect(() => {\n    LiteGraph.context_menu_scaling = settingStore.get(\n      'LiteGraph.ContextMenu.Scaling'\n    )\n  })\n}\n", "import { LiteGraph } from '@comfyorg/litegraph'\nimport type { LGraphNode } from '@comfyorg/litegraph'\nimport { useEventListener } from '@vueuse/core'\n\nimport { ComfyWorkflowJSON } from '@/schemas/comfyWorkflowSchema'\nimport { app } from '@/scripts/app'\nimport { useCanvasStore } from '@/stores/graphStore'\nimport { useWorkspaceStore } from '@/stores/workspaceStore'\nimport { isAudioNode, isImageNode, isVideoNode } from '@/utils/litegraphUtil'\n\n/**\n * Adds a handler on paste that extracts and loads images or workflows from pasted JSON data\n */\nexport const usePaste = () => {\n  const workspaceStore = useWorkspaceStore()\n  const canvasStore = useCanvasStore()\n\n  const pasteItemsOnNode = (\n    items: DataTransferItemList,\n    node: LGraphNode | null,\n    contentType: string\n  ) => {\n    if (!node) return\n\n    const filteredItems = Array.from(items).filter((item) =>\n      item.type.startsWith(contentType)\n    )\n\n    const blob = filteredItems[0]?.getAsFile()\n    if (!blob) return\n\n    node.pasteFile?.(blob)\n    node.pasteFiles?.(\n      Array.from(filteredItems)\n        .map((i) => i.getAsFile())\n        .filter((f) => f !== null)\n    )\n  }\n\n  useEventListener(document, 'paste', async (e) => {\n    // ctrl+shift+v is used to paste nodes with connections\n    // this is handled by litegraph\n    if (workspaceStore.shiftDown) return\n\n    const { canvas } = canvasStore\n    if (!canvas) return\n\n    const { graph } = canvas\n    let data: DataTransfer | string | null = e.clipboardData\n    if (!data) throw new Error('No clipboard data on clipboard event')\n\n    const { items } = data\n\n    const currentNode = canvas.current_node as LGraphNode\n    const isNodeSelected = currentNode?.is_selected\n\n    const isImageNodeSelected = isNodeSelected && isImageNode(currentNode)\n    const isVideoNodeSelected = isNodeSelected && isVideoNode(currentNode)\n    const isAudioNodeSelected = isNodeSelected && isAudioNode(currentNode)\n\n    let imageNode: LGraphNode | null = isImageNodeSelected ? currentNode : null\n    let audioNode: LGraphNode | null = isAudioNodeSelected ? currentNode : null\n    const videoNode: LGraphNode | null = isVideoNodeSelected\n      ? currentNode\n      : null\n\n    // Look for image paste data\n    for (const item of items) {\n      if (item.type.startsWith('image/')) {\n        if (!imageNode) {\n          // No image node selected: add a new one\n          const newNode = LiteGraph.createNode('LoadImage')\n          if (newNode) {\n            newNode.pos = [canvas.graph_mouse[0], canvas.graph_mouse[1]]\n            imageNode = graph?.add(newNode) ?? null\n          }\n          graph?.change()\n        }\n        pasteItemsOnNode(items, imageNode, 'image')\n        return\n      } else if (item.type.startsWith('video/')) {\n        if (!videoNode) {\n          // No video node selected: add a new one\n          // TODO: when video node exists\n        } else {\n          pasteItemsOnNode(items, videoNode, 'video')\n          return\n        }\n      } else if (item.type.startsWith('audio/')) {\n        if (!audioNode) {\n          // No audio node selected: add a new one\n          const newNode = LiteGraph.createNode('LoadAudio')\n          if (newNode) {\n            newNode.pos = [canvas.graph_mouse[0], canvas.graph_mouse[1]]\n            audioNode = graph?.add(newNode) ?? null\n          }\n          graph?.change()\n        }\n        pasteItemsOnNode(items, audioNode, 'audio')\n        return\n      }\n    }\n\n    // No image found. Look for node data\n    data = data.getData('text/plain')\n    let workflow: ComfyWorkflowJSON | null = null\n    try {\n      data = data.slice(data.indexOf('{'))\n      workflow = JSON.parse(data)\n    } catch (err) {\n      try {\n        data = data.slice(data.indexOf('workflow\\n'))\n        data = data.slice(data.indexOf('{'))\n        workflow = JSON.parse(data)\n      } catch (error) {\n        workflow = null\n      }\n    }\n\n    if (workflow && workflow.version && workflow.nodes && workflow.extra) {\n      await app.loadGraphData(workflow)\n    } else {\n      if (\n        (e.target instanceof HTMLTextAreaElement &&\n          e.target.type === 'textarea') ||\n        (e.target instanceof HTMLInputElement && e.target.type === 'text')\n      ) {\n        return\n      }\n\n      // Litegraph default paste\n      canvas.pasteFromClipboard()\n    }\n  })\n}\n", "import { computed, watch, watchEffect } from 'vue'\n\nimport { api } from '@/scripts/api'\nimport { app as comfyApp } from '@/scripts/app'\nimport { getStorageValue, setStorageValue } from '@/scripts/utils'\nimport { useWorkflowService } from '@/services/workflowService'\nimport { useCommandStore } from '@/stores/commandStore'\nimport { useSettingStore } from '@/stores/settingStore'\nimport { useWorkflowStore } from '@/stores/workflowStore'\n\nexport function useWorkflowPersistence() {\n  const workflowStore = useWorkflowStore()\n  const settingStore = useSettingStore()\n\n  const persistCurrentWorkflow = () => {\n    const workflow = JSON.stringify(comfyApp.serializeGraph())\n    localStorage.setItem('workflow', workflow)\n    if (api.clientId) {\n      sessionStorage.setItem(`workflow:${api.clientId}`, workflow)\n    }\n  }\n\n  const loadWorkflowFromStorage = async (\n    json: string | null,\n    workflowName: string | null\n  ) => {\n    if (!json) return false\n    const workflow = JSON.parse(json)\n    await comfyApp.loadGraphData(workflow, true, true, workflowName)\n    return true\n  }\n\n  const loadPreviousWorkflowFromStorage = async () => {\n    const workflowName = getStorageValue('Comfy.PreviousWorkflow')\n    const clientId = api.initialClientId ?? api.clientId\n\n    // Try loading from session storage first\n    if (clientId) {\n      const sessionWorkflow = sessionStorage.getItem(`workflow:${clientId}`)\n      if (await loadWorkflowFromStorage(sessionWorkflow, workflowName)) {\n        return true\n      }\n    }\n\n    // Fall back to local storage\n    const localWorkflow = localStorage.getItem('workflow')\n    return await loadWorkflowFromStorage(localWorkflow, workflowName)\n  }\n\n  const loadDefaultWorkflow = async () => {\n    if (!settingStore.get('Comfy.TutorialCompleted')) {\n      await settingStore.set('Comfy.TutorialCompleted', true)\n      await useWorkflowService().loadBlankWorkflow()\n      await useCommandStore().execute('Comfy.BrowseTemplates')\n    } else {\n      await comfyApp.loadGraphData()\n    }\n  }\n\n  const restorePreviousWorkflow = async () => {\n    try {\n      const restored = await loadPreviousWorkflowFromStorage()\n      if (!restored) {\n        await loadDefaultWorkflow()\n      }\n    } catch (err) {\n      console.error('Error loading previous workflow', err)\n      await loadDefaultWorkflow()\n    }\n  }\n\n  // Setup watchers\n  watchEffect(() => {\n    if (workflowStore.activeWorkflow) {\n      const workflow = workflowStore.activeWorkflow\n      setStorageValue('Comfy.PreviousWorkflow', workflow.key)\n      // When the activeWorkflow changes, the graph has already been loaded.\n      // Saving the current state of the graph to the localStorage.\n      persistCurrentWorkflow()\n    }\n  })\n  api.addEventListener('graphChanged', persistCurrentWorkflow)\n\n  // Restore workflow tabs states\n  const openWorkflows = computed(() => workflowStore.openWorkflows)\n  const activeWorkflow = computed(() => workflowStore.activeWorkflow)\n  const restoreState = computed<{ paths: string[]; activeIndex: number }>(\n    () => {\n      if (!openWorkflows.value || !activeWorkflow.value) {\n        return { paths: [], activeIndex: -1 }\n      }\n\n      const paths = openWorkflows.value\n        .filter((workflow) => workflow?.isPersisted && !workflow.isModified)\n        .map((workflow) => workflow.path)\n      const activeIndex = openWorkflows.value.findIndex(\n        (workflow) => workflow.path === activeWorkflow.value?.path\n      )\n\n      return { paths, activeIndex }\n    }\n  )\n\n  // Get storage values before setting watchers\n  const storedWorkflows = JSON.parse(\n    getStorageValue('Comfy.OpenWorkflowsPaths') || '[]'\n  )\n  const storedActiveIndex = JSON.parse(\n    getStorageValue('Comfy.ActiveWorkflowIndex') || '-1'\n  )\n  watch(restoreState, ({ paths, activeIndex }) => {\n    setStorageValue('Comfy.OpenWorkflowsPaths', JSON.stringify(paths))\n    setStorageValue('Comfy.ActiveWorkflowIndex', JSON.stringify(activeIndex))\n  })\n\n  const restoreWorkflowTabsState = () => {\n    const isRestorable = storedWorkflows?.length > 0 && storedActiveIndex >= 0\n    if (isRestorable) {\n      workflowStore.openWorkflowsInBackground({\n        left: storedWorkflows.slice(0, storedActiveIndex),\n        right: storedWorkflows.slice(storedActiveIndex)\n      })\n    }\n  }\n\n  return {\n    restorePreviousWorkflow,\n    restoreWorkflowTabsState\n  }\n}\n", "import { LinkMarkerShape } from '@comfyorg/litegraph'\nimport { LiteGraph } from '@comfyorg/litegraph'\n\nimport type { ColorPalettes } from '@/schemas/colorPaletteSchema'\nimport type { Keybinding } from '@/schemas/keyBindingSchema'\nimport { NodeBadgeMode } from '@/types/nodeSource'\nimport { LinkReleaseTriggerAction } from '@/types/searchBoxTypes'\nimport type { SettingParams } from '@/types/settingTypes'\n\nexport const CORE_SETTINGS: SettingParams[] = [\n  {\n    id: 'Comfy.Validation.Workflows',\n    name: 'Validate workflows',\n    type: 'boolean',\n    defaultValue: true\n  },\n  {\n    id: 'Comfy.NodeSearchBoxImpl',\n    category: ['Comfy', 'Node Search Box', 'Implementation'],\n    experimental: true,\n    name: 'Node search box implementation',\n    type: 'combo',\n    options: ['default', 'litegraph (legacy)'],\n    defaultValue: 'default'\n  },\n  {\n    id: 'Comfy.LinkRelease.Action',\n    category: ['LiteGraph', 'LinkRelease', 'Action'],\n    name: 'Action on link release (No modifier)',\n    type: 'combo',\n    options: Object.values(LinkReleaseTriggerAction),\n    defaultValue: LinkReleaseTriggerAction.CONTEXT_MENU\n  },\n  {\n    id: 'Comfy.LinkRelease.ActionShift',\n    category: ['LiteGraph', 'LinkRelease', 'ActionShift'],\n    name: 'Action on link release (Shift)',\n    type: 'combo',\n    options: Object.values(LinkReleaseTriggerAction),\n    defaultValue: LinkReleaseTriggerAction.SEARCH_BOX\n  },\n  {\n    id: 'Comfy.NodeSearchBoxImpl.NodePreview',\n    category: ['Comfy', 'Node Search Box', 'NodePreview'],\n    name: 'Node preview',\n    tooltip: 'Only applies to the default implementation',\n    type: 'boolean',\n    defaultValue: true\n  },\n  {\n    id: 'Comfy.NodeSearchBoxImpl.ShowCategory',\n    category: ['Comfy', 'Node Search Box', 'ShowCategory'],\n    name: 'Show node category in search results',\n    tooltip: 'Only applies to the default implementation',\n    type: 'boolean',\n    defaultValue: true\n  },\n  {\n    id: 'Comfy.NodeSearchBoxImpl.ShowIdName',\n    category: ['Comfy', 'Node Search Box', 'ShowIdName'],\n    name: 'Show node id name in search results',\n    tooltip: 'Only applies to the default implementation',\n    type: 'boolean',\n    defaultValue: false\n  },\n  {\n    id: 'Comfy.NodeSearchBoxImpl.ShowNodeFrequency',\n    category: ['Comfy', 'Node Search Box', 'ShowNodeFrequency'],\n    name: 'Show node frequency in search results',\n    tooltip: 'Only applies to the default implementation',\n    type: 'boolean',\n    defaultValue: false\n  },\n  {\n    id: 'Comfy.Sidebar.Location',\n    category: ['Appearance', 'Sidebar', 'Location'],\n    name: 'Sidebar location',\n    type: 'combo',\n    options: ['left', 'right'],\n    defaultValue: 'left'\n  },\n  {\n    id: 'Comfy.Sidebar.Size',\n    category: ['Appearance', 'Sidebar', 'Size'],\n    name: 'Sidebar size',\n    type: 'combo',\n    options: ['normal', 'small'],\n    // Default to small if the window is less than 1536px(2xl) wide.\n    defaultValue: () => (window.innerWidth < 1536 ? 'small' : 'normal')\n  },\n  {\n    id: 'Comfy.TextareaWidget.FontSize',\n    category: ['Appearance', 'Node Widget', 'TextareaWidget', 'FontSize'],\n    name: 'Textarea widget font size',\n    type: 'slider',\n    defaultValue: 10,\n    attrs: {\n      min: 8,\n      max: 24\n    }\n  },\n  {\n    id: 'Comfy.TextareaWidget.Spellcheck',\n    category: ['Comfy', 'Node Widget', 'TextareaWidget', 'Spellcheck'],\n    name: 'Textarea widget spellcheck',\n    type: 'boolean',\n    defaultValue: false\n  },\n  {\n    id: 'Comfy.Workflow.SortNodeIdOnSave',\n    name: 'Sort node IDs when saving workflow',\n    type: 'boolean',\n    defaultValue: false\n  },\n  {\n    id: 'Comfy.Graph.CanvasInfo',\n    category: ['LiteGraph', 'Canvas', 'CanvasInfo'],\n    name: 'Show canvas info on bottom left corner (fps, etc.)',\n    type: 'boolean',\n    defaultValue: true\n  },\n  {\n    id: 'Comfy.Node.ShowDeprecated',\n    name: 'Show deprecated nodes in search',\n    tooltip:\n      'Deprecated nodes are hidden by default in the UI, but remain functional in existing workflows that use them.',\n    type: 'boolean',\n    defaultValue: false\n  },\n  {\n    id: 'Comfy.Node.ShowExperimental',\n    name: 'Show experimental nodes in search',\n    tooltip:\n      'Experimental nodes are marked as such in the UI and may be subject to significant changes or removal in future versions. Use with caution in production workflows',\n    type: 'boolean',\n    defaultValue: true\n  },\n  {\n    id: 'Comfy.Node.Opacity',\n    category: ['Appearance', 'Node', 'Opacity'],\n    name: 'Node opacity',\n    type: 'slider',\n    defaultValue: 1,\n    attrs: {\n      min: 0.01,\n      max: 1,\n      step: 0.01\n    }\n  },\n  {\n    id: 'Comfy.Workflow.ShowMissingNodesWarning',\n    name: 'Show missing nodes warning',\n    type: 'boolean',\n    defaultValue: true\n  },\n  {\n    id: 'Comfy.Workflow.ShowMissingModelsWarning',\n    name: 'Show missing models warning',\n    type: 'boolean',\n    defaultValue: true,\n    experimental: true\n  },\n  {\n    id: 'Comfy.Graph.ZoomSpeed',\n    category: ['LiteGraph', 'Canvas', 'ZoomSpeed'],\n    name: 'Canvas zoom speed',\n    type: 'slider',\n    defaultValue: 1.1,\n    attrs: {\n      min: 1.01,\n      max: 2.5,\n      step: 0.01\n    }\n  },\n  // Bookmarks are stored in the settings store.\n  // Bookmarks are in format of category/display_name. e.g. \"conditioning/CLIPTextEncode\"\n  {\n    id: 'Comfy.NodeLibrary.Bookmarks',\n    name: 'Node library bookmarks with display name (deprecated)',\n    type: 'hidden',\n    defaultValue: [],\n    deprecated: true\n  },\n  {\n    id: 'Comfy.NodeLibrary.Bookmarks.V2',\n    name: 'Node library bookmarks v2 with unique name',\n    type: 'hidden',\n    defaultValue: []\n  },\n  // Stores mapping from bookmark folder name to its customization.\n  {\n    id: 'Comfy.NodeLibrary.BookmarksCustomization',\n    name: 'Node library bookmarks customization',\n    type: 'hidden',\n    defaultValue: {}\n  },\n  // Hidden setting used by the queue for how to fit images\n  {\n    id: 'Comfy.Queue.ImageFit',\n    name: 'Queue image fit',\n    type: 'hidden',\n    defaultValue: 'cover'\n  },\n  {\n    id: 'Comfy.GroupSelectedNodes.Padding',\n    category: ['LiteGraph', 'Group', 'Padding'],\n    name: 'Group selected nodes padding',\n    type: 'slider',\n    defaultValue: 10,\n    attrs: {\n      min: 0,\n      max: 100\n    }\n  },\n  {\n    id: 'Comfy.Node.DoubleClickTitleToEdit',\n    category: ['LiteGraph', 'Node', 'DoubleClickTitleToEdit'],\n    name: 'Double click node title to edit',\n    type: 'boolean',\n    defaultValue: true\n  },\n  {\n    id: 'Comfy.Group.DoubleClickTitleToEdit',\n    category: ['LiteGraph', 'Group', 'DoubleClickTitleToEdit'],\n    name: 'Double click group title to edit',\n    type: 'boolean',\n    defaultValue: true\n  },\n  {\n    id: 'Comfy.Window.UnloadConfirmation',\n    name: 'Show confirmation when closing window',\n    type: 'boolean',\n    defaultValue: true,\n    versionModified: '1.7.12'\n  },\n  {\n    id: 'Comfy.TreeExplorer.ItemPadding',\n    category: ['Appearance', 'Tree Explorer', 'ItemPadding'],\n    name: 'Tree explorer item padding',\n    type: 'slider',\n    defaultValue: 2,\n    attrs: {\n      min: 0,\n      max: 8,\n      step: 1\n    }\n  },\n  {\n    id: 'Comfy.ModelLibrary.AutoLoadAll',\n    name: 'Automatically load all model folders',\n    tooltip:\n      'If true, all folders will load as soon as you open the model library (this may cause delays while it loads). If false, root level model folders will only load once you click on them.',\n    type: 'boolean',\n    defaultValue: false\n  },\n  {\n    id: 'Comfy.ModelLibrary.NameFormat',\n    name: 'What name to display in the model library tree view',\n    tooltip:\n      'Select \"filename\" to render a simplified view of the raw filename (without directory or \".safetensors\" extension) in the model list. Select \"title\" to display the configurable model metadata title.',\n    type: 'combo',\n    options: ['filename', 'title'],\n    defaultValue: 'title'\n  },\n  {\n    id: 'Comfy.Locale',\n    name: 'Language',\n    type: 'combo',\n    options: [\n      { value: 'en', text: 'English' },\n      { value: 'zh', text: '中文' },\n      { value: 'ru', text: 'Русский' },\n      { value: 'ja', text: '日本語' },\n      { value: 'ko', text: '한국어' },\n      { value: 'fr', text: 'Français' }\n    ],\n    defaultValue: () => navigator.language.split('-')[0] || 'en'\n  },\n  {\n    id: 'Comfy.NodeBadge.NodeSourceBadgeMode',\n    category: ['LiteGraph', 'Node', 'NodeSourceBadgeMode'],\n    name: 'Node source badge mode',\n    type: 'combo',\n    options: Object.values(NodeBadgeMode),\n    defaultValue: NodeBadgeMode.HideBuiltIn\n  },\n  {\n    id: 'Comfy.NodeBadge.NodeIdBadgeMode',\n    category: ['LiteGraph', 'Node', 'NodeIdBadgeMode'],\n    name: 'Node ID badge mode',\n    type: 'combo',\n    options: [NodeBadgeMode.None, NodeBadgeMode.ShowAll],\n    defaultValue: NodeBadgeMode.None\n  },\n  {\n    id: 'Comfy.NodeBadge.NodeLifeCycleBadgeMode',\n    category: ['LiteGraph', 'Node', 'NodeLifeCycleBadgeMode'],\n    name: 'Node life cycle badge mode',\n    type: 'combo',\n    options: [NodeBadgeMode.None, NodeBadgeMode.ShowAll],\n    defaultValue: NodeBadgeMode.ShowAll\n  },\n  {\n    id: 'Comfy.ConfirmClear',\n    category: ['Comfy', 'Workflow', 'ConfirmClear'],\n    name: 'Require confirmation when clearing workflow',\n    type: 'boolean',\n    defaultValue: true\n  },\n  {\n    id: 'Comfy.PromptFilename',\n    category: ['Comfy', 'Workflow', 'PromptFilename'],\n    name: 'Prompt for filename when saving workflow',\n    type: 'boolean',\n    defaultValue: true\n  },\n  /**\n   * file format for preview\n   *\n   * format;quality\n   *\n   * ex)\n   * webp;50 -> webp, quality 50\n   * jpeg;80 -> rgb, jpeg, quality 80\n   *\n   * @type {string}\n   */\n  {\n    id: 'Comfy.PreviewFormat',\n    category: ['LiteGraph', 'Node Widget', 'PreviewFormat'],\n    name: 'Preview image format',\n    tooltip:\n      'When displaying a preview in the image widget, convert it to a lightweight image, e.g. webp, jpeg, webp;50, etc.',\n    type: 'text',\n    defaultValue: ''\n  },\n  {\n    id: 'Comfy.DisableSliders',\n    category: ['LiteGraph', 'Node Widget', 'DisableSliders'],\n    name: 'Disable node widget sliders',\n    type: 'boolean',\n    defaultValue: false\n  },\n  {\n    id: 'Comfy.DisableFloatRounding',\n    category: ['LiteGraph', 'Node Widget', 'DisableFloatRounding'],\n    name: 'Disable default float widget rounding.',\n    tooltip:\n      '(requires page reload) Cannot disable round when round is set by the node in the backend.',\n    type: 'boolean',\n    defaultValue: false\n  },\n  {\n    id: 'Comfy.FloatRoundingPrecision',\n    category: ['LiteGraph', 'Node Widget', 'FloatRoundingPrecision'],\n    name: 'Float widget rounding decimal places [0 = auto].',\n    tooltip: '(requires page reload)',\n    type: 'slider',\n    attrs: {\n      min: 0,\n      max: 6,\n      step: 1\n    },\n    defaultValue: 0\n  },\n  {\n    id: 'LiteGraph.Node.TooltipDelay',\n    name: 'Tooltip Delay',\n    type: 'number',\n    attrs: {\n      min: 100,\n      max: 3000,\n      step: 50\n    },\n    defaultValue: 500,\n    versionAdded: '1.9.0'\n  },\n  {\n    id: 'Comfy.EnableTooltips',\n    category: ['LiteGraph', 'Node', 'EnableTooltips'],\n    name: 'Enable Tooltips',\n    type: 'boolean',\n    defaultValue: true\n  },\n  {\n    id: 'Comfy.DevMode',\n    name: 'Enable dev mode options (API save, etc.)',\n    type: 'boolean',\n    defaultValue: false,\n    onChange: (value) => {\n      const element = document.getElementById('comfy-dev-save-api-button')\n      if (element) {\n        element.style.display = value ? 'flex' : 'none'\n      }\n    }\n  },\n  {\n    id: 'Comfy.UseNewMenu',\n    category: ['Comfy', 'Menu', 'UseNewMenu'],\n    defaultValue: 'Top',\n    name: 'Use new menu',\n    type: 'combo',\n    options: ['Disabled', 'Top', 'Bottom'],\n    migrateDeprecatedValue: (value: string) => {\n      // Floating is now supported by dragging the docked actionbar off.\n      if (value === 'Floating') {\n        return 'Top'\n      }\n      return value\n    }\n  },\n  {\n    id: 'Comfy.Workflow.WorkflowTabsPosition',\n    name: 'Opened workflows position',\n    type: 'combo',\n    options: ['Sidebar', 'Topbar', 'Topbar (2nd-row)'],\n    // Default to topbar (2nd-row) if the window is less than 1536px(2xl) wide.\n    defaultValue: () =>\n      window.innerWidth < 1536 ? 'Topbar (2nd-row)' : 'Topbar'\n  },\n  {\n    id: 'Comfy.Graph.CanvasMenu',\n    category: ['LiteGraph', 'Canvas', 'CanvasMenu'],\n    name: 'Show graph canvas menu',\n    type: 'boolean',\n    defaultValue: true\n  },\n  {\n    id: 'Comfy.QueueButton.BatchCountLimit',\n    name: 'Batch count limit',\n    tooltip:\n      'The maximum number of tasks added to the queue at one button click',\n    type: 'number',\n    defaultValue: 100,\n    versionAdded: '1.3.5'\n  },\n  {\n    id: 'Comfy.Keybinding.UnsetBindings',\n    name: 'Keybindings unset by the user',\n    type: 'hidden',\n    defaultValue: [] as Keybinding[],\n    versionAdded: '1.3.7',\n    versionModified: '1.7.3',\n    migrateDeprecatedValue: (value: any[]) => {\n      return value.map((keybinding) => {\n        if (keybinding['targetSelector'] === '#graph-canvas') {\n          keybinding['targetElementId'] = 'graph-canvas'\n        }\n        return keybinding\n      })\n    }\n  },\n  {\n    id: 'Comfy.Keybinding.NewBindings',\n    name: 'Keybindings set by the user',\n    type: 'hidden',\n    defaultValue: [] as Keybinding[],\n    versionAdded: '1.3.7'\n  },\n  {\n    id: 'Comfy.Extension.Disabled',\n    name: 'Disabled extension names',\n    type: 'hidden',\n    defaultValue: [] as string[],\n    versionAdded: '1.3.11'\n  },\n  {\n    id: 'Comfy.Validation.NodeDefs',\n    name: 'Validate node definitions (slow)',\n    type: 'boolean',\n    tooltip:\n      'Recommended for node developers. This will validate all node definitions on startup.',\n    defaultValue: false,\n    versionAdded: '1.3.14'\n  },\n  {\n    id: 'Comfy.LinkRenderMode',\n    category: ['LiteGraph', 'Graph', 'LinkRenderMode'],\n    name: 'Link Render Mode',\n    defaultValue: 2,\n    type: 'combo',\n    options: [\n      { value: LiteGraph.STRAIGHT_LINK, text: 'Straight' },\n      { value: LiteGraph.LINEAR_LINK, text: 'Linear' },\n      { value: LiteGraph.SPLINE_LINK, text: 'Spline' },\n      { value: LiteGraph.HIDDEN_LINK, text: 'Hidden' }\n    ]\n  },\n  {\n    id: 'Comfy.Node.AutoSnapLinkToSlot',\n    category: ['LiteGraph', 'Node', 'AutoSnapLinkToSlot'],\n    name: 'Auto snap link to node slot',\n    tooltip:\n      'When dragging a link over a node, the link automatically snap to a viable input slot on the node',\n    type: 'boolean',\n    defaultValue: true,\n    versionAdded: '1.3.29'\n  },\n  {\n    id: 'Comfy.Node.SnapHighlightsNode',\n    category: ['LiteGraph', 'Node', 'SnapHighlightsNode'],\n    name: 'Snap highlights node',\n    tooltip:\n      'When dragging a link over a node with viable input slot, highlight the node',\n    type: 'boolean',\n    defaultValue: true,\n    versionAdded: '1.3.29'\n  },\n  {\n    id: 'Comfy.Node.BypassAllLinksOnDelete',\n    category: ['LiteGraph', 'Node', 'BypassAllLinksOnDelete'],\n    name: 'Keep all links when deleting nodes',\n    tooltip:\n      'When deleting a node, attempt to reconnect all of its input and output links (bypassing the deleted node)',\n    type: 'boolean',\n    defaultValue: true,\n    versionAdded: '1.3.40'\n  },\n  {\n    id: 'Comfy.Node.MiddleClickRerouteNode',\n    category: ['LiteGraph', 'Node', 'MiddleClickRerouteNode'],\n    name: 'Middle-click creates a new Reroute node',\n    type: 'boolean',\n    defaultValue: true,\n    versionAdded: '1.3.42'\n  },\n  {\n    id: 'Comfy.RerouteBeta',\n    category: ['LiteGraph', 'RerouteBeta'],\n    name: 'Opt-in to the reroute beta test',\n    tooltip: 'No longer has any effect; reroutes are always enabled.',\n    deprecated: true,\n    type: 'boolean',\n    defaultValue: false,\n    versionAdded: '1.3.42',\n    versionModified: '1.13.3'\n  },\n  {\n    id: 'Comfy.Graph.LinkMarkers',\n    category: ['LiteGraph', 'Link', 'LinkMarkers'],\n    name: 'Link midpoint markers',\n    defaultValue: LinkMarkerShape.Circle,\n    type: 'combo',\n    options: [\n      { value: LinkMarkerShape.None, text: 'None' },\n      { value: LinkMarkerShape.Circle, text: 'Circle' },\n      { value: LinkMarkerShape.Arrow, text: 'Arrow' }\n    ],\n    versionAdded: '1.3.42'\n  },\n  {\n    id: 'Comfy.DOMClippingEnabled',\n    category: ['LiteGraph', 'Node', 'DOMClippingEnabled'],\n    name: 'Enable DOM element clipping (enabling may reduce performance)',\n    type: 'boolean',\n    defaultValue: true\n  },\n  {\n    id: 'Comfy.Graph.CtrlShiftZoom',\n    category: ['LiteGraph', 'Canvas', 'CtrlShiftZoom'],\n    name: 'Enable fast-zoom shortcut (Ctrl + Shift + Drag)',\n    type: 'boolean',\n    defaultValue: true,\n    versionAdded: '1.4.0'\n  },\n  {\n    id: 'Comfy.Pointer.ClickDrift',\n    category: ['LiteGraph', 'Pointer', 'ClickDrift'],\n    name: 'Pointer click drift (maximum distance)',\n    tooltip:\n      'If the pointer moves more than this distance while holding a button down, it is considered dragging (rather than clicking).\\n\\nHelps prevent objects from being unintentionally nudged if the pointer is moved whilst clicking.',\n    experimental: true,\n    type: 'slider',\n    attrs: {\n      min: 0,\n      max: 20,\n      step: 1\n    },\n    defaultValue: 6,\n    versionAdded: '1.4.3'\n  },\n  {\n    id: 'Comfy.Pointer.ClickBufferTime',\n    category: ['LiteGraph', 'Pointer', 'ClickBufferTime'],\n    name: 'Pointer click drift delay',\n    tooltip:\n      'After pressing a pointer button down, this is the maximum time (in milliseconds) that pointer movement can be ignored for.\\n\\nHelps prevent objects from being unintentionally nudged if the pointer is moved whilst clicking.',\n    experimental: true,\n    type: 'slider',\n    attrs: {\n      min: 0,\n      max: 1000,\n      step: 25\n    },\n    defaultValue: 150,\n    versionAdded: '1.4.3'\n  },\n  {\n    id: 'Comfy.Pointer.DoubleClickTime',\n    category: ['LiteGraph', 'Pointer', 'DoubleClickTime'],\n    name: 'Double click interval (maximum)',\n    tooltip:\n      'The maximum time in milliseconds between the two clicks of a double-click.  Increasing this value may assist if double-clicks are sometimes not registered.',\n    type: 'slider',\n    attrs: {\n      min: 100,\n      max: 1000,\n      step: 50\n    },\n    defaultValue: 300,\n    versionAdded: '1.4.3'\n  },\n  {\n    id: 'Comfy.SnapToGrid.GridSize',\n    category: ['LiteGraph', 'Canvas', 'GridSize'],\n    name: 'Snap to grid size',\n    type: 'slider',\n    attrs: {\n      min: 1,\n      max: 500\n    },\n    tooltip:\n      'When dragging and resizing nodes while holding shift they will be aligned to the grid, this controls the size of that grid.',\n    defaultValue: LiteGraph.CANVAS_GRID_SIZE\n  },\n  // Keep the 'pysssss.SnapToGrid' setting id so we don't need to migrate setting values.\n  // Using a new setting id can cause existing users to lose their existing settings.\n  {\n    id: 'pysssss.SnapToGrid',\n    category: ['LiteGraph', 'Canvas', 'AlwaysSnapToGrid'],\n    name: 'Always snap to grid',\n    type: 'boolean',\n    defaultValue: false,\n    versionAdded: '1.3.13'\n  },\n  {\n    id: 'Comfy.Server.ServerConfigValues',\n    name: 'Server config values for frontend display',\n    tooltip: 'Server config values used for frontend display only',\n    type: 'hidden',\n    // Mapping from server config id to value.\n    defaultValue: {} as Record<string, any>,\n    versionAdded: '1.4.8'\n  },\n  {\n    id: 'Comfy.Server.LaunchArgs',\n    name: 'Server launch arguments',\n    tooltip:\n      'These are the actual arguments that are passed to the server when it is launched.',\n    type: 'hidden',\n    defaultValue: {} as Record<string, string>,\n    versionAdded: '1.4.8'\n  },\n  {\n    id: 'Comfy.Queue.MaxHistoryItems',\n    name: 'Queue history size',\n    tooltip: 'The maximum number of tasks that show in the queue history.',\n    type: 'slider',\n    attrs: {\n      min: 2,\n      max: 256,\n      step: 2\n    },\n    defaultValue: 64,\n    versionAdded: '1.4.12'\n  },\n  {\n    id: 'LiteGraph.Canvas.MaximumFps',\n    name: 'Maximum FPS',\n    tooltip:\n      'The maximum frames per second that the canvas is allowed to render. Caps GPU usage at the cost of smoothness. If 0, the screen refresh rate is used. Default: 0',\n    type: 'slider',\n    attrs: {\n      min: 0,\n      max: 120\n    },\n    defaultValue: 0,\n    versionAdded: '1.5.1'\n  },\n  {\n    id: 'Comfy.EnableWorkflowViewRestore',\n    category: ['Comfy', 'Workflow', 'EnableWorkflowViewRestore'],\n    name: 'Save and restore canvas position and zoom level in workflows',\n    type: 'boolean',\n    defaultValue: true,\n    versionModified: '1.5.4'\n  },\n  {\n    id: 'Comfy.Workflow.ConfirmDelete',\n    name: 'Show confirmation when deleting workflows',\n    type: 'boolean',\n    defaultValue: true,\n    versionAdded: '1.5.6'\n  },\n  {\n    id: 'Comfy.ColorPalette',\n    name: 'The active color palette id',\n    type: 'hidden',\n    defaultValue: 'dark',\n    versionModified: '1.6.7',\n    migrateDeprecatedValue(value: string) {\n      // Legacy custom palettes were prefixed with 'custom_'\n      return value.startsWith('custom_') ? value.replace('custom_', '') : value\n    }\n  },\n  {\n    id: 'Comfy.CustomColorPalettes',\n    name: 'Custom color palettes',\n    type: 'hidden',\n    defaultValue: {} as ColorPalettes,\n    versionModified: '1.6.7'\n  },\n  {\n    id: 'Comfy.WidgetControlMode',\n    category: ['Comfy', 'Node Widget', 'WidgetControlMode'],\n    name: 'Widget control mode',\n    tooltip:\n      'Controls when widget values are updated (randomize/increment/decrement), either before the prompt is queued or after.',\n    type: 'combo',\n    defaultValue: 'after',\n    options: ['before', 'after'],\n    versionModified: '1.6.10'\n  },\n  {\n    id: 'Comfy.TutorialCompleted',\n    name: 'Tutorial completed',\n    type: 'hidden',\n    defaultValue: false,\n    versionAdded: '1.8.7'\n  },\n  {\n    id: 'LiteGraph.ContextMenu.Scaling',\n    name: 'Scale node combo widget menus (lists) when zoomed in',\n    defaultValue: false,\n    type: 'boolean',\n    versionAdded: '1.8.8'\n  },\n  {\n    id: 'LiteGraph.Canvas.LowQualityRenderingZoomThreshold',\n    name: 'Low quality rendering zoom threshold',\n    tooltip: 'Render low quality shapes when zoomed out',\n    type: 'slider',\n    attrs: {\n      min: 0.1,\n      max: 1,\n      step: 0.01\n    },\n    defaultValue: 0.6,\n    versionAdded: '1.9.1'\n  },\n  {\n    id: 'Comfy.Canvas.SelectionToolbox',\n    category: ['LiteGraph', 'Canvas', 'SelectionToolbox'],\n    name: 'Show selection toolbox',\n    type: 'boolean',\n    defaultValue: true,\n    versionAdded: '1.10.5'\n  }\n]\n", "<template>\n  <!-- Load splitter overlay only after comfyApp is ready. -->\n  <!-- If load immediately, the top-level splitter stateKey won't be correctly\n  synced with the stateStorage (localStorage). -->\n  <LiteGraphCanvasSplitterOverlay\n    v-if=\"comfyAppReady && betaMenuEnabled && !workspaceStore.focusMode\"\n  >\n    <template #side-bar-panel>\n      <SideToolbar />\n    </template>\n    <template #bottom-panel>\n      <BottomPanel />\n    </template>\n    <template #graph-canvas-panel>\n      <SecondRowWorkflowTabs\n        v-if=\"workflowTabsPosition === 'Topbar (2nd-row)'\"\n        class=\"pointer-events-auto\"\n      />\n      <GraphCanvasMenu v-if=\"canvasMenuEnabled\" class=\"pointer-events-auto\" />\n    </template>\n  </LiteGraphCanvasSplitterOverlay>\n  <TitleEditor />\n  <GraphCanvasMenu v-if=\"!betaMenuEnabled && canvasMenuEnabled\" />\n  <canvas\n    ref=\"canvasRef\"\n    id=\"graph-canvas\"\n    tabindex=\"1\"\n    class=\"w-full h-full touch-none\"\n  />\n  <NodeSearchboxPopover />\n  <SelectionOverlay v-if=\"selectionToolboxEnabled\">\n    <SelectionToolbox />\n  </SelectionOverlay>\n  <NodeTooltip v-if=\"tooltipEnabled\" />\n  <NodeBadge />\n  <DomWidgets />\n</template>\n\n<script setup lang=\"ts\">\nimport { computed, onMounted, ref, watch, watchEffect } from 'vue'\n\nimport LiteGraphCanvasSplitterOverlay from '@/components/LiteGraphCanvasSplitterOverlay.vue'\nimport BottomPanel from '@/components/bottomPanel/BottomPanel.vue'\nimport DomWidgets from '@/components/graph/DomWidgets.vue'\nimport GraphCanvasMenu from '@/components/graph/GraphCanvasMenu.vue'\nimport NodeBadge from '@/components/graph/NodeBadge.vue'\nimport NodeTooltip from '@/components/graph/NodeTooltip.vue'\nimport SelectionOverlay from '@/components/graph/SelectionOverlay.vue'\nimport SelectionToolbox from '@/components/graph/SelectionToolbox.vue'\nimport TitleEditor from '@/components/graph/TitleEditor.vue'\nimport NodeSearchboxPopover from '@/components/searchbox/NodeSearchBoxPopover.vue'\nimport SideToolbar from '@/components/sidebar/SideToolbar.vue'\nimport SecondRowWorkflowTabs from '@/components/topbar/SecondRowWorkflowTabs.vue'\nimport { useChainCallback } from '@/composables/functional/useChainCallback'\nimport { useCanvasDrop } from '@/composables/useCanvasDrop'\nimport { useContextMenuTranslation } from '@/composables/useContextMenuTranslation'\nimport { useCopy } from '@/composables/useCopy'\nimport { useGlobalLitegraph } from '@/composables/useGlobalLitegraph'\nimport { useLitegraphSettings } from '@/composables/useLitegraphSettings'\nimport { usePaste } from '@/composables/usePaste'\nimport { useWorkflowPersistence } from '@/composables/useWorkflowPersistence'\nimport { CORE_SETTINGS } from '@/constants/coreSettings'\nimport { i18n } from '@/i18n'\nimport { api } from '@/scripts/api'\nimport { app as comfyApp } from '@/scripts/app'\nimport { ChangeTracker } from '@/scripts/changeTracker'\nimport { IS_CONTROL_WIDGET, updateControlWidgetLabel } from '@/scripts/widgets'\nimport { useColorPaletteService } from '@/services/colorPaletteService'\nimport { useWorkflowService } from '@/services/workflowService'\nimport { useCommandStore } from '@/stores/commandStore'\nimport { useCanvasStore } from '@/stores/graphStore'\nimport { useNodeDefStore } from '@/stores/nodeDefStore'\nimport { useSettingStore } from '@/stores/settingStore'\nimport { useColorPaletteStore } from '@/stores/workspace/colorPaletteStore'\nimport { useWorkspaceStore } from '@/stores/workspaceStore'\n\nconst emit = defineEmits(['ready'])\nconst canvasRef = ref<HTMLCanvasElement | null>(null)\nconst settingStore = useSettingStore()\nconst nodeDefStore = useNodeDefStore()\nconst workspaceStore = useWorkspaceStore()\nconst canvasStore = useCanvasStore()\nconst betaMenuEnabled = computed(\n  () => settingStore.get('Comfy.UseNewMenu') !== 'Disabled'\n)\nconst workflowTabsPosition = computed(() =>\n  settingStore.get('Comfy.Workflow.WorkflowTabsPosition')\n)\nconst canvasMenuEnabled = computed(() =>\n  settingStore.get('Comfy.Graph.CanvasMenu')\n)\nconst tooltipEnabled = computed(() => settingStore.get('Comfy.EnableTooltips'))\nconst selectionToolboxEnabled = computed(() =>\n  settingStore.get('Comfy.Canvas.SelectionToolbox')\n)\n\nwatchEffect(() => {\n  nodeDefStore.showDeprecated = settingStore.get('Comfy.Node.ShowDeprecated')\n})\n\nwatchEffect(() => {\n  nodeDefStore.showExperimental = settingStore.get(\n    'Comfy.Node.ShowExperimental'\n  )\n})\n\nwatchEffect(() => {\n  const spellcheckEnabled = settingStore.get('Comfy.TextareaWidget.Spellcheck')\n  const textareas = document.querySelectorAll<HTMLTextAreaElement>(\n    'textarea.comfy-multiline-input'\n  )\n\n  textareas.forEach((textarea: HTMLTextAreaElement) => {\n    textarea.spellcheck = spellcheckEnabled\n    // Force recheck to ensure visual update\n    textarea.focus()\n    textarea.blur()\n  })\n})\n\nwatch(\n  () => settingStore.get('Comfy.WidgetControlMode'),\n  () => {\n    if (!canvasStore.canvas) return\n\n    for (const n of comfyApp.graph.nodes) {\n      if (!n.widgets) continue\n      for (const w of n.widgets) {\n        // @ts-expect-error fixme ts strict error\n        if (w[IS_CONTROL_WIDGET]) {\n          updateControlWidgetLabel(w)\n          if (w.linkedWidgets) {\n            for (const l of w.linkedWidgets) {\n              updateControlWidgetLabel(l)\n            }\n          }\n        }\n      }\n    }\n    comfyApp.graph.setDirtyCanvas(true)\n  }\n)\n\nconst colorPaletteService = useColorPaletteService()\nconst colorPaletteStore = useColorPaletteStore()\nwatch(\n  [() => canvasStore.canvas, () => settingStore.get('Comfy.ColorPalette')],\n  ([canvas, currentPaletteId]) => {\n    if (!canvas) return\n\n    colorPaletteService.loadColorPalette(currentPaletteId)\n  }\n)\nwatch(\n  () => colorPaletteStore.activePaletteId,\n  (newValue) => {\n    settingStore.set('Comfy.ColorPalette', newValue)\n  }\n)\n\nconst loadCustomNodesI18n = async () => {\n  try {\n    const i18nData = await api.getCustomNodesI18n()\n    Object.entries(i18nData).forEach(([locale, message]) => {\n      i18n.global.mergeLocaleMessage(locale, message)\n    })\n  } catch (error) {\n    console.error('Failed to load custom nodes i18n', error)\n  }\n}\n\nconst comfyAppReady = ref(false)\nconst workflowPersistence = useWorkflowPersistence()\n// @ts-expect-error fixme ts strict error\nuseCanvasDrop(canvasRef)\nuseLitegraphSettings()\n\nonMounted(async () => {\n  useGlobalLitegraph()\n  useContextMenuTranslation()\n  useCopy()\n  usePaste()\n\n  comfyApp.vueAppReady = true\n\n  workspaceStore.spinner = true\n  // ChangeTracker needs to be initialized before setup, as it will overwrite\n  // some listeners of litegraph canvas.\n  ChangeTracker.init(comfyApp)\n  await loadCustomNodesI18n()\n  await settingStore.loadSettingValues()\n  CORE_SETTINGS.forEach((setting) => {\n    settingStore.addSetting(setting)\n  })\n  // @ts-expect-error fixme ts strict error\n  await comfyApp.setup(canvasRef.value)\n  canvasStore.canvas = comfyApp.canvas\n  canvasStore.canvas.render_canvas_border = false\n  workspaceStore.spinner = false\n\n  // @ts-expect-error fixme ts strict error\n  window['app'] = comfyApp\n  // @ts-expect-error fixme ts strict error\n  window['graph'] = comfyApp.graph\n\n  comfyAppReady.value = true\n\n  comfyApp.canvas.onSelectionChange = useChainCallback(\n    comfyApp.canvas.onSelectionChange,\n    () => canvasStore.updateSelectedItems()\n  )\n\n  // Load color palette\n  colorPaletteStore.customPalettes = settingStore.get(\n    'Comfy.CustomColorPalettes'\n  )\n\n  // Restore workflow and workflow tabs state from storage\n  await workflowPersistence.restorePreviousWorkflow()\n  workflowPersistence.restoreWorkflowTabsState()\n\n  // Start watching for locale change after the initial value is loaded.\n  watch(\n    () => settingStore.get('Comfy.Locale'),\n    async () => {\n      await useCommandStore().execute('Comfy.RefreshNodeDefinitions')\n      useWorkflowService().reloadCurrentWorkflow()\n    }\n  )\n\n  emit('ready')\n})\n</script>\n", "<template>\n  <Toast />\n</template>\n\n<script setup lang=\"ts\">\nimport Toast from 'primevue/toast'\nimport { useToast } from 'primevue/usetoast'\nimport { nextTick, watch } from 'vue'\n\nimport { useSettingStore } from '@/stores/settingStore'\nimport { useToastStore } from '@/stores/toastStore'\n\nconst toast = useToast()\nconst toastStore = useToastStore()\nconst settingStore = useSettingStore()\n\nwatch(\n  () => toastStore.messagesToAdd,\n  (newMessages) => {\n    if (newMessages.length === 0) {\n      return\n    }\n\n    newMessages.forEach((message) => {\n      toast.add(message)\n    })\n    toastStore.messagesToAdd = []\n  },\n  { deep: true }\n)\n\nwatch(\n  () => toastStore.messagesToRemove,\n  (messagesToRemove) => {\n    if (messagesToRemove.length === 0) {\n      return\n    }\n\n    messagesToRemove.forEach((message) => {\n      toast.remove(message)\n    })\n    toastStore.messagesToRemove = []\n  },\n  { deep: true }\n)\n\nwatch(\n  () => toastStore.removeAllRequested,\n  (requested) => {\n    if (requested) {\n      toast.removeAllGroups()\n      toastStore.removeAllRequested = false\n    }\n  }\n)\n\nfunction updateToastPosition() {\n  const styleElement =\n    document.getElementById('dynamic-toast-style') || createStyleElement()\n  const rect = document\n    .querySelector('.graph-canvas-container')\n    ?.getBoundingClientRect()\n  if (!rect) return\n\n  styleElement.textContent = `\n    .p-toast.p-component.p-toast-top-right {\n      top: ${rect.top + 20}px !important;\n      right: ${window.innerWidth - (rect.left + rect.width) + 20}px !important;\n    }\n  `\n}\n\nfunction createStyleElement() {\n  const style = document.createElement('style')\n  style.id = 'dynamic-toast-style'\n  document.head.appendChild(style)\n  return style\n}\n\nwatch(\n  () => settingStore.get('Comfy.UseNewMenu'),\n  () => nextTick(updateToastPosition),\n  { immediate: true }\n)\nwatch(\n  () => settingStore.get('Comfy.Sidebar.Location'),\n  () => nextTick(updateToastPosition),\n  { immediate: true }\n)\n</script>\n", "<template>\n  <div\n    class=\"batch-count\"\n    v-tooltip.bottom=\"{\n      value: $t('menu.batchCount'),\n      showDelay: 600\n    }\"\n    :aria-label=\"$t('menu.batchCount')\"\n  >\n    <InputNumber\n      class=\"w-14\"\n      v-model=\"batchCount\"\n      :min=\"minQueueCount\"\n      :max=\"maxQueueCount\"\n      fluid\n      showButtons\n      :pt=\"{\n        incrementButton: {\n          class: 'w-6',\n          onmousedown: () => {\n            handleClick(true)\n          }\n        },\n        decrementButton: {\n          class: 'w-6',\n          onmousedown: () => {\n            handleClick(false)\n          }\n        }\n      }\"\n    />\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { storeToRefs } from 'pinia'\nimport InputNumber from 'primevue/inputnumber'\nimport { computed } from 'vue'\n\nimport { useQueueSettingsStore } from '@/stores/queueStore'\nimport { useSettingStore } from '@/stores/settingStore'\n\nconst queueSettingsStore = useQueueSettingsStore()\nconst { batchCount } = storeToRefs(queueSettingsStore)\nconst minQueueCount = 1\n\nconst settingStore = useSettingStore()\nconst maxQueueCount = computed(() =>\n  settingStore.get('Comfy.QueueButton.BatchCountLimit')\n)\n\nconst handleClick = (increment: boolean) => {\n  let newCount: number\n  if (increment) {\n    const originalCount = batchCount.value - 1\n    newCount = Math.min(originalCount * 2, maxQueueCount.value)\n  } else {\n    const originalCount = batchCount.value + 1\n    newCount = Math.floor(originalCount / 2)\n  }\n\n  batchCount.value = newCount\n}\n</script>\n\n<style scoped>\n:deep(.p-inputtext) {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n}\n</style>\n", "<template>\n  <div class=\"queue-button-group flex\">\n    <SplitButton\n      class=\"comfyui-queue-button\"\n      :label=\"activeQueueModeMenuItem.label\"\n      severity=\"primary\"\n      size=\"small\"\n      @click=\"queuePrompt\"\n      :model=\"queueModeMenuItems\"\n      data-testid=\"queue-button\"\n      v-tooltip.bottom=\"{\n        value: workspaceStore.shiftDown\n          ? $t('menu.runWorkflowFront')\n          : $t('menu.runWorkflow'),\n        showDelay: 600\n      }\"\n    >\n      <template #icon>\n        <i-lucide:list-start v-if=\"workspaceStore.shiftDown\" />\n        <i-lucide:play v-else-if=\"queueMode === 'disabled'\" />\n        <i-lucide:fast-forward v-else-if=\"queueMode === 'instant'\" />\n        <i-lucide:step-forward v-else-if=\"queueMode === 'change'\" />\n      </template>\n      <template #item=\"{ item }\">\n        <Button\n          :label=\"String(item.label)\"\n          :icon=\"item.icon\"\n          :severity=\"item.key === queueMode ? 'primary' : 'secondary'\"\n          size=\"small\"\n          text\n          v-tooltip=\"{\n            value: item.tooltip,\n            showDelay: 600\n          }\"\n        />\n      </template>\n    </SplitButton>\n    <BatchCountEdit />\n    <ButtonGroup class=\"execution-actions flex flex-nowrap\">\n      <Button\n        v-tooltip.bottom=\"{\n          value: $t('menu.interrupt'),\n          showDelay: 600\n        }\"\n        icon=\"pi pi-times\"\n        :severity=\"executingPrompt ? 'danger' : 'secondary'\"\n        :disabled=\"!executingPrompt\"\n        text\n        :aria-label=\"$t('menu.interrupt')\"\n        @click=\"() => commandStore.execute('Comfy.Interrupt')\"\n      >\n      </Button>\n      <Button\n        v-tooltip.bottom=\"{\n          value: $t('sideToolbar.queueTab.clearPendingTasks'),\n          showDelay: 600\n        }\"\n        icon=\"pi pi-stop\"\n        :severity=\"hasPendingTasks ? 'danger' : 'secondary'\"\n        :disabled=\"!hasPendingTasks\"\n        text\n        :aria-label=\"$t('sideToolbar.queueTab.clearPendingTasks')\"\n        @click=\"\n          () => {\n            if (queueCountStore.count.value > 1) {\n              commandStore.execute('Comfy.ClearPendingTasks')\n            }\n            queueMode = 'disabled'\n          }\n        \"\n      />\n    </ButtonGroup>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { storeToRefs } from 'pinia'\nimport Button from 'primevue/button'\nimport ButtonGroup from 'primevue/buttongroup'\nimport SplitButton from 'primevue/splitbutton'\nimport { computed } from 'vue'\nimport { useI18n } from 'vue-i18n'\n\nimport { useCommandStore } from '@/stores/commandStore'\nimport {\n  useQueuePendingTaskCountStore,\n  useQueueSettingsStore\n} from '@/stores/queueStore'\nimport { useWorkspaceStore } from '@/stores/workspaceStore'\n\nimport BatchCountEdit from './BatchCountEdit.vue'\n\nconst workspaceStore = useWorkspaceStore()\nconst queueCountStore = storeToRefs(useQueuePendingTaskCountStore())\nconst { mode: queueMode } = storeToRefs(useQueueSettingsStore())\n\nconst { t } = useI18n()\nconst queueModeMenuItemLookup = computed(() => ({\n  disabled: {\n    key: 'disabled',\n    label: t('menu.run'),\n    tooltip: t('menu.disabledTooltip'),\n    command: () => {\n      queueMode.value = 'disabled'\n    }\n  },\n  instant: {\n    key: 'instant',\n    label: `${t('menu.run')} (${t('menu.instant')})`,\n    tooltip: t('menu.instantTooltip'),\n    command: () => {\n      queueMode.value = 'instant'\n    }\n  },\n  change: {\n    key: 'change',\n    label: `${t('menu.run')} (${t('menu.onChange')})`,\n    tooltip: t('menu.onChangeTooltip'),\n    command: () => {\n      queueMode.value = 'change'\n    }\n  }\n}))\n\nconst activeQueueModeMenuItem = computed(\n  () => queueModeMenuItemLookup.value[queueMode.value]\n)\nconst queueModeMenuItems = computed(() =>\n  Object.values(queueModeMenuItemLookup.value)\n)\n\nconst executingPrompt = computed(() => !!queueCountStore.count.value)\nconst hasPendingTasks = computed(\n  () => queueCountStore.count.value > 1 || queueMode.value !== 'disabled'\n)\n\nconst commandStore = useCommandStore()\nconst queuePrompt = (e: Event) => {\n  const commandId =\n    'shiftKey' in e && e.shiftKey\n      ? 'Comfy.QueuePromptFront'\n      : 'Comfy.QueuePrompt'\n  commandStore.execute(commandId)\n}\n</script>\n\n<style scoped>\n.comfyui-queue-button :deep(.p-splitbutton-dropdown) {\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0;\n}\n</style>\n", "<template>\n  <Panel\n    class=\"actionbar w-fit\"\n    :style=\"style\"\n    :class=\"{ 'is-dragging': isDragging, 'is-docked': isDocked }\"\n  >\n    <div class=\"actionbar-content flex items-center select-none\" ref=\"panelRef\">\n      <span class=\"drag-handle cursor-move mr-2 p-0!\" ref=\"dragHandleRef\">\n      </span>\n      <ComfyQueueButton />\n    </div>\n  </Panel>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  useDraggable,\n  useElementBounding,\n  useEventBus,\n  useEventListener,\n  useLocalStorage,\n  watchDebounced\n} from '@vueuse/core'\nimport { clamp } from 'lodash'\nimport Panel from 'primevue/panel'\nimport { Ref, computed, inject, nextTick, onMounted, ref, watch } from 'vue'\n\nimport { useSettingStore } from '@/stores/settingStore'\n\nimport ComfyQueueButton from './ComfyQueueButton.vue'\n\nconst settingsStore = useSettingStore()\n\nconst visible = computed(\n  () => settingsStore.get('Comfy.UseNewMenu') !== 'Disabled'\n)\n\nconst panelRef = ref<HTMLElement | null>(null)\nconst dragHandleRef = ref<HTMLElement | null>(null)\nconst isDocked = useLocalStorage('Comfy.MenuPosition.Docked', false)\nconst storedPosition = useLocalStorage('Comfy.MenuPosition.Floating', {\n  x: 0,\n  y: 0\n})\nconst {\n  x,\n  y,\n  style: style,\n  isDragging\n} = useDraggable(panelRef, {\n  initialValue: { x: 0, y: 0 },\n  handle: dragHandleRef,\n  containerElement: document.body\n})\n\n// Update storedPosition when x or y changes\nwatchDebounced(\n  [x, y],\n  ([newX, newY]) => {\n    storedPosition.value = { x: newX, y: newY }\n  },\n  { debounce: 300 }\n)\n\n// Set initial position to bottom center\nconst setInitialPosition = () => {\n  if (x.value !== 0 || y.value !== 0) {\n    return\n  }\n  if (storedPosition.value.x !== 0 || storedPosition.value.y !== 0) {\n    x.value = storedPosition.value.x\n    y.value = storedPosition.value.y\n    captureLastDragState()\n    return\n  }\n  if (panelRef.value) {\n    const screenWidth = window.innerWidth\n    const screenHeight = window.innerHeight\n    const menuWidth = panelRef.value.offsetWidth\n    const menuHeight = panelRef.value.offsetHeight\n\n    if (menuWidth === 0 || menuHeight === 0) {\n      return\n    }\n\n    x.value = (screenWidth - menuWidth) / 2\n    y.value = screenHeight - menuHeight - 10 // 10px margin from bottom\n    captureLastDragState()\n  }\n}\nonMounted(setInitialPosition)\nwatch(visible, (newVisible) => {\n  if (newVisible) {\n    nextTick(setInitialPosition)\n  }\n})\n\nconst lastDragState = ref({\n  x: x.value,\n  y: y.value,\n  windowWidth: window.innerWidth,\n  windowHeight: window.innerHeight\n})\nconst captureLastDragState = () => {\n  lastDragState.value = {\n    x: x.value,\n    y: y.value,\n    windowWidth: window.innerWidth,\n    windowHeight: window.innerHeight\n  }\n}\nwatch(\n  isDragging,\n  (newIsDragging) => {\n    if (!newIsDragging) {\n      // Stop dragging\n      captureLastDragState()\n    }\n  },\n  { immediate: true }\n)\n\nconst adjustMenuPosition = () => {\n  if (panelRef.value) {\n    const screenWidth = window.innerWidth\n    const screenHeight = window.innerHeight\n    const menuWidth = panelRef.value.offsetWidth\n    const menuHeight = panelRef.value.offsetHeight\n\n    // Calculate distances to all edges\n    const distanceLeft = lastDragState.value.x\n    const distanceRight =\n      lastDragState.value.windowWidth - (lastDragState.value.x + menuWidth)\n    const distanceTop = lastDragState.value.y\n    const distanceBottom =\n      lastDragState.value.windowHeight - (lastDragState.value.y + menuHeight)\n\n    // Find the smallest distance to determine which edge to anchor to\n    const distances = [\n      { edge: 'left', distance: distanceLeft },\n      { edge: 'right', distance: distanceRight },\n      { edge: 'top', distance: distanceTop },\n      { edge: 'bottom', distance: distanceBottom }\n    ]\n    const closestEdge = distances.reduce((min, curr) =>\n      curr.distance < min.distance ? curr : min\n    )\n\n    // Calculate vertical position as a percentage of screen height\n    const verticalRatio =\n      lastDragState.value.y / lastDragState.value.windowHeight\n    const horizontalRatio =\n      lastDragState.value.x / lastDragState.value.windowWidth\n\n    // Apply positioning based on closest edge\n    if (closestEdge.edge === 'left') {\n      x.value = closestEdge.distance // Maintain exact distance from left\n      y.value = verticalRatio * screenHeight\n    } else if (closestEdge.edge === 'right') {\n      x.value = screenWidth - menuWidth - closestEdge.distance // Maintain exact distance from right\n      y.value = verticalRatio * screenHeight\n    } else if (closestEdge.edge === 'top') {\n      x.value = horizontalRatio * screenWidth\n      y.value = closestEdge.distance // Maintain exact distance from top\n    } else {\n      // bottom\n      x.value = horizontalRatio * screenWidth\n      y.value = screenHeight - menuHeight - closestEdge.distance // Maintain exact distance from bottom\n    }\n\n    // Ensure the menu stays within the screen bounds\n    x.value = clamp(x.value, 0, screenWidth - menuWidth)\n    y.value = clamp(y.value, 0, screenHeight - menuHeight)\n  }\n}\n\nuseEventListener(window, 'resize', adjustMenuPosition)\n\nconst topMenuRef = inject<Ref<HTMLDivElement | null>>('topMenuRef')\nconst topMenuBounds = useElementBounding(topMenuRef)\nconst overlapThreshold = 20 // pixels\nconst isOverlappingWithTopMenu = computed(() => {\n  if (!panelRef.value) {\n    return false\n  }\n  const { height } = panelRef.value.getBoundingClientRect()\n  const actionbarBottom = y.value + height\n  const topMenuBottom = topMenuBounds.bottom.value\n\n  const overlapPixels =\n    Math.min(actionbarBottom, topMenuBottom) -\n    Math.max(y.value, topMenuBounds.top.value)\n  return overlapPixels > overlapThreshold\n})\n\nwatch(isDragging, (newIsDragging) => {\n  if (!newIsDragging) {\n    // Stop dragging\n    isDocked.value = isOverlappingWithTopMenu.value\n  } else {\n    // Start dragging\n    isDocked.value = false\n  }\n})\n\nconst eventBus = useEventBus<string>('topMenu')\nwatch([isDragging, isOverlappingWithTopMenu], ([dragging, overlapping]) => {\n  eventBus.emit('updateHighlight', {\n    isDragging: dragging,\n    isOverlapping: overlapping\n  })\n})\n</script>\n\n<style scoped>\n.actionbar {\n  pointer-events: all;\n  position: fixed;\n  z-index: 1000;\n}\n\n.actionbar.is-docked {\n  position: static;\n  @apply bg-transparent border-none p-0;\n}\n\n.actionbar.is-dragging {\n  user-select: none;\n}\n\n:deep(.p-panel-content) {\n  @apply p-1;\n}\n\n.is-docked :deep(.p-panel-content) {\n  @apply p-0;\n}\n\n:deep(.p-panel-header) {\n  display: none;\n}\n\n.drag-handle {\n  @apply w-3 h-max;\n}\n</style>\n", "<template>\n  <Button\n    v-show=\"bottomPanelStore.bottomPanelTabs.length > 0\"\n    severity=\"secondary\"\n    text\n    :aria-label=\"$t('menu.toggleBottomPanel')\"\n    @click=\"bottomPanelStore.toggleBottomPanel\"\n    v-tooltip=\"{ value: $t('menu.toggleBottomPanel'), showDelay: 300 }\"\n  >\n    <template #icon>\n      <i-material-symbols:dock-to-bottom\n        v-if=\"bottomPanelStore.bottomPanelVisible\"\n      />\n      <i-material-symbols:dock-to-bottom-outline v-else />\n    </template>\n  </Button>\n</template>\n\n<script setup lang=\"ts\">\nimport Button from 'primevue/button'\n\nimport { useBottomPanelStore } from '@/stores/workspace/bottomPanelStore'\n\nconst bottomPanelStore = useBottomPanelStore()\n</script>\n", "<template>\n  <Menubar\n    :model=\"translatedItems\"\n    class=\"top-menubar border-none p-0 bg-transparent\"\n    :pt=\"{\n      rootList: 'gap-0 flex-nowrap w-auto',\n      submenu: `dropdown-direction-${dropdownDirection}`,\n      item: 'relative'\n    }\"\n  >\n    <template #item=\"{ item, props, root }\">\n      <a\n        class=\"p-menubar-item-link\"\n        v-bind=\"props.action\"\n        :href=\"item.url\"\n        target=\"_blank\"\n      >\n        <span v-if=\"item.icon\" class=\"p-menubar-item-icon\" :class=\"item.icon\" />\n        <span class=\"p-menubar-item-label\">{{ item.label }}</span>\n        <span\n          v-if=\"item?.comfyCommand?.keybinding\"\n          class=\"ml-auto border border-surface rounded text-muted text-xs text-nowrap p-1 keybinding-tag\"\n        >\n          {{ item.comfyCommand.keybinding.combo.toString() }}\n        </span>\n        <i v-if=\"!root && item.items\" class=\"ml-auto pi pi-angle-right\" />\n      </a>\n    </template>\n  </Menubar>\n</template>\n\n<script setup lang=\"ts\">\nimport Menubar from 'primevue/menubar'\nimport type { MenuItem } from 'primevue/menuitem'\nimport { computed } from 'vue'\nimport { useI18n } from 'vue-i18n'\n\nimport { useMenuItemStore } from '@/stores/menuItemStore'\nimport { useSettingStore } from '@/stores/settingStore'\nimport { normalizeI18nKey } from '@/utils/formatUtil'\n\nconst settingStore = useSettingStore()\nconst dropdownDirection = computed(() =>\n  settingStore.get('Comfy.UseNewMenu') === 'Top' ? 'down' : 'up'\n)\n\nconst menuItemsStore = useMenuItemStore()\nconst { t } = useI18n()\nconst translateMenuItem = (item: MenuItem): MenuItem => {\n  const label = typeof item.label === 'function' ? item.label() : item.label\n  const translatedLabel = label\n    ? t(`menuLabels.${normalizeI18nKey(label)}`, label)\n    : undefined\n\n  return {\n    ...item,\n    label: translatedLabel,\n    items: item.items?.map(translateMenuItem)\n  }\n}\n\nconst translatedItems = computed(() =>\n  menuItemsStore.menuItems.map(translateMenuItem)\n)\n</script>\n\n<style scoped>\n:deep(.p-menubar-submenu.dropdown-direction-up) {\n  @apply top-auto bottom-full flex-col-reverse;\n}\n\n.keybinding-tag {\n  background: var(--p-content-hover-background);\n  border-color: var(--p-content-border-color);\n  border-style: solid;\n}\n</style>\n", "<template>\n  <div\n    ref=\"topMenuRef\"\n    class=\"comfyui-menu flex items-center\"\n    v-show=\"showTopMenu\"\n    :class=\"{ dropzone: isDropZone, 'dropzone-active': isDroppable }\"\n  >\n    <h1 class=\"comfyui-logo mx-2 app-drag\">ComfyUI</h1>\n    <CommandMenubar />\n    <div class=\"flex-grow min-w-0 app-drag h-full\">\n      <WorkflowTabs v-if=\"workflowTabsPosition === 'Topbar'\" />\n    </div>\n    <div class=\"comfyui-menu-right flex-shrink-0\" ref=\"menuRight\"></div>\n    <Actionbar />\n    <BottomPanelToggleButton class=\"flex-shrink-0\" />\n    <Button\n      class=\"flex-shrink-0\"\n      icon=\"pi pi-bars\"\n      severity=\"secondary\"\n      text\n      v-tooltip=\"{ value: $t('menu.hideMenu'), showDelay: 300 }\"\n      :aria-label=\"$t('menu.hideMenu')\"\n      @click=\"workspaceState.focusMode = true\"\n      @contextmenu=\"showNativeSystemMenu\"\n    />\n    <div\n      v-show=\"menuSetting !== 'Bottom'\"\n      class=\"window-actions-spacer flex-shrink-0\"\n    />\n  </div>\n\n  <!-- Virtual top menu for native window (drag handle) -->\n  <div\n    v-show=\"isNativeWindow() && !showTopMenu\"\n    class=\"fixed top-0 left-0 app-drag w-full h-[var(--comfy-topbar-height)]\"\n  />\n</template>\n\n<script setup lang=\"ts\">\nimport { useEventBus } from '@vueuse/core'\nimport Button from 'primevue/button'\nimport { computed, onMounted, provide, ref } from 'vue'\n\nimport Actionbar from '@/components/actionbar/ComfyActionbar.vue'\nimport BottomPanelToggleButton from '@/components/topbar/BottomPanelToggleButton.vue'\nimport CommandMenubar from '@/components/topbar/CommandMenubar.vue'\nimport WorkflowTabs from '@/components/topbar/WorkflowTabs.vue'\nimport { app } from '@/scripts/app'\nimport { useSettingStore } from '@/stores/settingStore'\nimport { useWorkspaceStore } from '@/stores/workspaceStore'\nimport {\n  electronAPI,\n  isElectron,\n  isNativeWindow,\n  showNativeSystemMenu\n} from '@/utils/envUtil'\n\nconst workspaceState = useWorkspaceStore()\nconst settingStore = useSettingStore()\nconst workflowTabsPosition = computed(() =>\n  settingStore.get('Comfy.Workflow.WorkflowTabsPosition')\n)\nconst menuSetting = computed(() => settingStore.get('Comfy.UseNewMenu'))\nconst betaMenuEnabled = computed(() => menuSetting.value !== 'Disabled')\nconst showTopMenu = computed(\n  () => betaMenuEnabled.value && !workspaceState.focusMode\n)\n\nconst menuRight = ref<HTMLDivElement | null>(null)\n// Menu-right holds legacy topbar elements attached by custom scripts\nonMounted(() => {\n  if (menuRight.value) {\n    menuRight.value.appendChild(app.menu.element)\n  }\n})\n\nconst topMenuRef = ref<HTMLDivElement | null>(null)\nprovide('topMenuRef', topMenuRef)\nconst eventBus = useEventBus<string>('topMenu')\nconst isDropZone = ref(false)\nconst isDroppable = ref(false)\neventBus.on((event: string, payload: any) => {\n  if (event === 'updateHighlight') {\n    isDropZone.value = payload.isDragging\n    isDroppable.value = payload.isOverlapping && payload.isDragging\n  }\n})\n\nonMounted(() => {\n  if (isElectron()) {\n    electronAPI().changeTheme({\n      height: topMenuRef.value?.getBoundingClientRect().height ?? 0\n    })\n  }\n})\n</script>\n\n<style scoped>\n.comfyui-menu {\n  width: 100vw;\n  height: var(--comfy-topbar-height);\n  background: var(--comfy-menu-bg);\n  color: var(--fg-color);\n  box-shadow: var(--bar-shadow);\n  font-family: Arial, Helvetica, sans-serif;\n  font-size: 0.8em;\n  box-sizing: border-box;\n  z-index: 1000;\n  order: 0;\n  grid-column: 1/-1;\n}\n\n.comfyui-menu.dropzone {\n  background: var(--p-highlight-background);\n}\n\n.comfyui-menu.dropzone-active {\n  background: var(--p-highlight-background-focus);\n}\n\n:deep(.p-menubar-item-label) {\n  line-height: revert;\n}\n\n.comfyui-logo {\n  font-size: 1.2em;\n  user-select: none;\n  cursor: default;\n}\n</style>\n", "import {\n  LGraphEventMode,\n  LGraphGroup,\n  LGraphNode,\n  LiteGraph\n} from '@comfyorg/litegraph'\n\nimport {\n  DEFAULT_DARK_COLOR_PALETTE,\n  DEFAULT_LIGHT_COLOR_PALETTE\n} from '@/constants/coreColorPalettes'\nimport { t } from '@/i18n'\nimport { api } from '@/scripts/api'\nimport { app } from '@/scripts/app'\nimport { useDialogService } from '@/services/dialogService'\nimport { useLitegraphService } from '@/services/litegraphService'\nimport { useWorkflowService } from '@/services/workflowService'\nimport type { ComfyCommand } from '@/stores/commandStore'\nimport { useTitleEditorStore } from '@/stores/graphStore'\nimport { useQueueSettingsStore, useQueueStore } from '@/stores/queueStore'\nimport { useSettingStore } from '@/stores/settingStore'\nimport { useToastStore } from '@/stores/toastStore'\nimport { type ComfyWorkflow, useWorkflowStore } from '@/stores/workflowStore'\nimport { useBottomPanelStore } from '@/stores/workspace/bottomPanelStore'\nimport { useColorPaletteStore } from '@/stores/workspace/colorPaletteStore'\nimport { useSearchBoxStore } from '@/stores/workspace/searchBoxStore'\nimport { useWorkspaceStore } from '@/stores/workspaceStore'\n\nexport function useCoreCommands(): ComfyCommand[] {\n  const workflowService = useWorkflowService()\n  const workflowStore = useWorkflowStore()\n  const dialogService = useDialogService()\n  const colorPaletteStore = useColorPaletteStore()\n  const getTracker = () => workflowStore.activeWorkflow?.changeTracker\n\n  const getSelectedNodes = (): LGraphNode[] => {\n    const selectedNodes = app.canvas.selected_nodes\n    const result: LGraphNode[] = []\n    if (selectedNodes) {\n      for (const i in selectedNodes) {\n        const node = selectedNodes[i]\n        result.push(node)\n      }\n    }\n    return result\n  }\n\n  const toggleSelectedNodesMode = (mode: LGraphEventMode) => {\n    getSelectedNodes().forEach((node) => {\n      if (node.mode === mode) {\n        node.mode = LGraphEventMode.ALWAYS\n      } else {\n        node.mode = mode\n      }\n    })\n  }\n\n  return [\n    {\n      id: 'Comfy.NewBlankWorkflow',\n      icon: 'pi pi-plus',\n      label: 'New Blank Workflow',\n      menubarLabel: 'New',\n      function: () => workflowService.loadBlankWorkflow()\n    },\n    {\n      id: 'Comfy.OpenWorkflow',\n      icon: 'pi pi-folder-open',\n      label: 'Open Workflow',\n      menubarLabel: 'Open',\n      function: () => {\n        app.ui.loadFile()\n      }\n    },\n    {\n      id: 'Comfy.LoadDefaultWorkflow',\n      icon: 'pi pi-code',\n      label: 'Load Default Workflow',\n      function: () => workflowService.loadDefaultWorkflow()\n    },\n    {\n      id: 'Comfy.SaveWorkflow',\n      icon: 'pi pi-save',\n      label: 'Save Workflow',\n      menubarLabel: 'Save',\n      function: async () => {\n        const workflow = useWorkflowStore().activeWorkflow as ComfyWorkflow\n        if (!workflow) return\n\n        await workflowService.saveWorkflow(workflow)\n      }\n    },\n    {\n      id: 'Comfy.SaveWorkflowAs',\n      icon: 'pi pi-save',\n      label: 'Save Workflow As',\n      menubarLabel: 'Save As',\n      function: async () => {\n        const workflow = useWorkflowStore().activeWorkflow as ComfyWorkflow\n        if (!workflow) return\n\n        await workflowService.saveWorkflowAs(workflow)\n      }\n    },\n    {\n      id: 'Comfy.ExportWorkflow',\n      icon: 'pi pi-download',\n      label: 'Export Workflow',\n      menubarLabel: 'Export',\n      function: () => {\n        workflowService.exportWorkflow('workflow', 'workflow')\n      }\n    },\n    {\n      id: 'Comfy.ExportWorkflowAPI',\n      icon: 'pi pi-download',\n      label: 'Export Workflow (API Format)',\n      menubarLabel: 'Export (API)',\n      function: () => {\n        workflowService.exportWorkflow('workflow_api', 'output')\n      }\n    },\n    {\n      id: 'Comfy.Undo',\n      icon: 'pi pi-undo',\n      label: 'Undo',\n      function: async () => {\n        await getTracker()?.undo?.()\n      }\n    },\n    {\n      id: 'Comfy.Redo',\n      icon: 'pi pi-refresh',\n      label: 'Redo',\n      function: async () => {\n        await getTracker()?.redo?.()\n      }\n    },\n    {\n      id: 'Comfy.ClearWorkflow',\n      icon: 'pi pi-trash',\n      label: 'Clear Workflow',\n      function: () => {\n        const settingStore = useSettingStore()\n        if (\n          !settingStore.get('Comfy.ComfirmClear') ||\n          confirm('Clear workflow?')\n        ) {\n          app.clean()\n          app.graph.clear()\n          api.dispatchCustomEvent('graphCleared')\n        }\n      }\n    },\n    {\n      id: 'Comfy.Canvas.ResetView',\n      icon: 'pi pi-expand',\n      label: 'Reset View',\n      function: () => {\n        useLitegraphService().resetView()\n      }\n    },\n    {\n      id: 'Comfy.OpenClipspace',\n      icon: 'pi pi-clipboard',\n      label: 'Clipspace',\n      function: () => {\n        app.openClipspace()\n      }\n    },\n    {\n      id: 'Comfy.RefreshNodeDefinitions',\n      icon: 'pi pi-refresh',\n      label: 'Refresh Node Definitions',\n      function: async () => {\n        await app.refreshComboInNodes()\n      }\n    },\n    {\n      id: 'Comfy.Interrupt',\n      icon: 'pi pi-stop',\n      label: 'Interrupt',\n      function: async () => {\n        await api.interrupt()\n        useToastStore().add({\n          severity: 'info',\n          summary: 'Interrupted',\n          detail: 'Execution has been interrupted',\n          life: 1000\n        })\n      }\n    },\n    {\n      id: 'Comfy.ClearPendingTasks',\n      icon: 'pi pi-stop',\n      label: 'Clear Pending Tasks',\n      function: async () => {\n        await useQueueStore().clear(['queue'])\n        useToastStore().add({\n          severity: 'info',\n          summary: 'Confirmed',\n          detail: 'Pending tasks deleted',\n          life: 3000\n        })\n      }\n    },\n    {\n      id: 'Comfy.BrowseTemplates',\n      icon: 'pi pi-folder-open',\n      label: 'Browse Templates',\n      function: () => {\n        dialogService.showTemplateWorkflowsDialog()\n      }\n    },\n    {\n      id: 'Comfy.Canvas.ZoomIn',\n      icon: 'pi pi-plus',\n      label: 'Zoom In',\n      function: () => {\n        const ds = app.canvas.ds\n        ds.changeScale(\n          ds.scale * 1.1,\n          ds.element ? [ds.element.width / 2, ds.element.height / 2] : undefined\n        )\n        app.canvas.setDirty(true, true)\n      }\n    },\n    {\n      id: 'Comfy.Canvas.ZoomOut',\n      icon: 'pi pi-minus',\n      label: 'Zoom Out',\n      function: () => {\n        const ds = app.canvas.ds\n        ds.changeScale(\n          ds.scale / 1.1,\n          ds.element ? [ds.element.width / 2, ds.element.height / 2] : undefined\n        )\n        app.canvas.setDirty(true, true)\n      }\n    },\n    {\n      id: 'Comfy.Canvas.FitView',\n      icon: 'pi pi-expand',\n      label: 'Fit view to selected nodes',\n      function: () => {\n        if (app.canvas.empty) {\n          useToastStore().add({\n            severity: 'error',\n            summary: 'Empty canvas',\n            life: 3000\n          })\n          return\n        }\n        app.canvas.fitViewToSelectionAnimated()\n      }\n    },\n    {\n      id: 'Comfy.Canvas.ToggleLock',\n      icon: 'pi pi-lock',\n      label: 'Canvas Toggle Lock',\n      function: () => {\n        app.canvas['read_only'] = !app.canvas['read_only']\n      }\n    },\n    {\n      id: 'Comfy.Canvas.ToggleLinkVisibility',\n      icon: 'pi pi-eye',\n      label: 'Canvas Toggle Link Visibility',\n      versionAdded: '1.3.6',\n\n      function: (() => {\n        const settingStore = useSettingStore()\n        let lastLinksRenderMode = LiteGraph.SPLINE_LINK\n\n        return () => {\n          const currentMode = settingStore.get('Comfy.LinkRenderMode')\n\n          if (currentMode === LiteGraph.HIDDEN_LINK) {\n            // If links are hidden, restore the last positive value or default to spline mode\n            settingStore.set('Comfy.LinkRenderMode', lastLinksRenderMode)\n          } else {\n            // If links are visible, store the current mode and hide links\n            lastLinksRenderMode = currentMode\n            settingStore.set('Comfy.LinkRenderMode', LiteGraph.HIDDEN_LINK)\n          }\n        }\n      })()\n    },\n    {\n      id: 'Comfy.QueuePrompt',\n      icon: 'pi pi-play',\n      label: 'Queue Prompt',\n      versionAdded: '1.3.7',\n      function: () => {\n        const batchCount = useQueueSettingsStore().batchCount\n        app.queuePrompt(0, batchCount)\n      }\n    },\n    {\n      id: 'Comfy.QueuePromptFront',\n      icon: 'pi pi-play',\n      label: 'Queue Prompt (Front)',\n      versionAdded: '1.3.7',\n      function: () => {\n        const batchCount = useQueueSettingsStore().batchCount\n        app.queuePrompt(-1, batchCount)\n      }\n    },\n    {\n      id: 'Comfy.ShowSettingsDialog',\n      icon: 'pi pi-cog',\n      label: 'Show Settings Dialog',\n      versionAdded: '1.3.7',\n      function: () => {\n        dialogService.showSettingsDialog()\n      }\n    },\n    {\n      id: 'Comfy.Graph.GroupSelectedNodes',\n      icon: 'pi pi-sitemap',\n      label: 'Group Selected Nodes',\n      versionAdded: '1.3.7',\n      function: () => {\n        const { canvas } = app\n        if (!canvas.selectedItems?.size) {\n          useToastStore().add({\n            severity: 'error',\n            summary: 'Nothing to group',\n            detail:\n              'Please select the nodes (or other groups) to create a group for',\n            life: 3000\n          })\n          return\n        }\n        const group = new LGraphGroup()\n        const padding = useSettingStore().get(\n          'Comfy.GroupSelectedNodes.Padding'\n        )\n        group.resizeTo(canvas.selectedItems, padding)\n        canvas.graph?.add(group)\n        useTitleEditorStore().titleEditorTarget = group\n      }\n    },\n    {\n      id: 'Workspace.NextOpenedWorkflow',\n      icon: 'pi pi-step-forward',\n      label: 'Next Opened Workflow',\n      versionAdded: '1.3.9',\n      function: () => {\n        workflowService.loadNextOpenedWorkflow()\n      }\n    },\n    {\n      id: 'Workspace.PreviousOpenedWorkflow',\n      icon: 'pi pi-step-backward',\n      label: 'Previous Opened Workflow',\n      versionAdded: '1.3.9',\n      function: () => {\n        workflowService.loadPreviousOpenedWorkflow()\n      }\n    },\n    {\n      id: 'Comfy.Canvas.ToggleSelectedNodes.Mute',\n      icon: 'pi pi-volume-off',\n      label: 'Mute/Unmute Selected Nodes',\n      versionAdded: '1.3.11',\n      function: () => {\n        toggleSelectedNodesMode(LGraphEventMode.NEVER)\n        app.canvas.setDirty(true, true)\n      }\n    },\n    {\n      id: 'Comfy.Canvas.ToggleSelectedNodes.Bypass',\n      icon: 'pi pi-shield',\n      label: 'Bypass/Unbypass Selected Nodes',\n      versionAdded: '1.3.11',\n      function: () => {\n        toggleSelectedNodesMode(LGraphEventMode.BYPASS)\n        app.canvas.setDirty(true, true)\n      }\n    },\n    {\n      id: 'Comfy.Canvas.ToggleSelectedNodes.Pin',\n      icon: 'pi pi-pin',\n      label: 'Pin/Unpin Selected Nodes',\n      versionAdded: '1.3.11',\n      function: () => {\n        getSelectedNodes().forEach((node) => {\n          node.pin(!node.pinned)\n        })\n        app.canvas.setDirty(true, true)\n      }\n    },\n    {\n      id: 'Comfy.Canvas.ToggleSelected.Pin',\n      icon: 'pi pi-pin',\n      label: 'Pin/Unpin Selected Items',\n      versionAdded: '1.3.33',\n      function: () => {\n        for (const item of app.canvas.selectedItems) {\n          if (item instanceof LGraphNode || item instanceof LGraphGroup) {\n            item.pin(!item.pinned)\n          }\n        }\n        app.canvas.setDirty(true, true)\n      }\n    },\n    {\n      id: 'Comfy.Canvas.ToggleSelectedNodes.Collapse',\n      icon: 'pi pi-minus',\n      label: 'Collapse/Expand Selected Nodes',\n      versionAdded: '1.3.11',\n      function: () => {\n        getSelectedNodes().forEach((node) => {\n          node.collapse()\n        })\n        app.canvas.setDirty(true, true)\n      }\n    },\n    {\n      id: 'Comfy.ToggleTheme',\n      icon: 'pi pi-moon',\n      label: 'Toggle Theme (Dark/Light)',\n      versionAdded: '1.3.12',\n      function: (() => {\n        let previousDarkTheme: string = DEFAULT_DARK_COLOR_PALETTE.id\n        let previousLightTheme: string = DEFAULT_LIGHT_COLOR_PALETTE.id\n\n        return () => {\n          const settingStore = useSettingStore()\n          const theme = colorPaletteStore.completedActivePalette\n          if (theme.light_theme) {\n            previousLightTheme = theme.id\n            settingStore.set('Comfy.ColorPalette', previousDarkTheme)\n          } else {\n            previousDarkTheme = theme.id\n            settingStore.set('Comfy.ColorPalette', previousLightTheme)\n          }\n        }\n      })()\n    },\n    {\n      id: 'Workspace.ToggleBottomPanel',\n      icon: 'pi pi-list',\n      label: 'Toggle Bottom Panel',\n      versionAdded: '1.3.22',\n      function: () => {\n        useBottomPanelStore().toggleBottomPanel()\n      }\n    },\n    {\n      id: 'Workspace.ToggleFocusMode',\n      icon: 'pi pi-eye',\n      label: 'Toggle Focus Mode',\n      versionAdded: '1.3.27',\n      function: () => {\n        useWorkspaceStore().toggleFocusMode()\n      }\n    },\n    {\n      id: 'Comfy.Graph.FitGroupToContents',\n      icon: 'pi pi-expand',\n      label: 'Fit Group To Contents',\n      versionAdded: '1.4.9',\n      function: () => {\n        for (const group of app.canvas.selectedItems) {\n          if (group instanceof LGraphGroup) {\n            group.recomputeInsideNodes()\n            const padding = useSettingStore().get(\n              'Comfy.GroupSelectedNodes.Padding'\n            )\n            group.resizeTo(group.children, padding)\n            app.graph.change()\n          }\n        }\n      }\n    },\n    {\n      id: 'Comfy.Help.OpenComfyUIIssues',\n      icon: 'pi pi-github',\n      label: 'Open ComfyUI Issues',\n      menubarLabel: 'ComfyUI Issues',\n      versionAdded: '1.5.5',\n      function: () => {\n        window.open(\n          'https://github.com/comfyanonymous/ComfyUI/issues',\n          '_blank'\n        )\n      }\n    },\n    {\n      id: 'Comfy.Help.OpenComfyUIDocs',\n      icon: 'pi pi-info-circle',\n      label: 'Open ComfyUI Docs',\n      menubarLabel: 'ComfyUI Docs',\n      versionAdded: '1.5.5',\n      function: () => {\n        window.open('https://docs.comfy.org/', '_blank')\n      }\n    },\n    {\n      id: 'Comfy.Help.OpenComfyOrgDiscord',\n      icon: 'pi pi-discord',\n      label: 'Open Comfy-Org Discord',\n      menubarLabel: 'Comfy-Org Discord',\n      versionAdded: '1.5.5',\n      function: () => {\n        window.open('https://www.comfy.org/discord', '_blank')\n      }\n    },\n    {\n      id: 'Workspace.SearchBox.Toggle',\n      icon: 'pi pi-search',\n      label: 'Toggle Search Box',\n      versionAdded: '1.5.7',\n      function: () => {\n        useSearchBoxStore().toggleVisible()\n      }\n    },\n    {\n      id: 'Comfy.Help.AboutComfyUI',\n      icon: 'pi pi-info-circle',\n      label: 'Open About ComfyUI',\n      menubarLabel: 'About ComfyUI',\n      versionAdded: '1.6.4',\n      function: () => {\n        dialogService.showSettingsDialog('about')\n      }\n    },\n    {\n      id: 'Comfy.DuplicateWorkflow',\n      icon: 'pi pi-clone',\n      label: 'Duplicate Current Workflow',\n      versionAdded: '1.6.15',\n      function: () => {\n        workflowService.duplicateWorkflow(workflowStore.activeWorkflow!)\n      }\n    },\n    {\n      id: 'Workspace.CloseWorkflow',\n      icon: 'pi pi-times',\n      label: 'Close Current Workflow',\n      versionAdded: '1.7.3',\n      function: () => {\n        if (workflowStore.activeWorkflow)\n          workflowService.closeWorkflow(workflowStore.activeWorkflow)\n      }\n    },\n    {\n      id: 'Comfy.Feedback',\n      icon: 'pi pi-megaphone',\n      label: 'Give Feedback',\n      versionAdded: '1.8.2',\n      function: () => {\n        dialogService.showIssueReportDialog({\n          title: t('g.feedback'),\n          subtitle: t('issueReport.feedbackTitle'),\n          panelProps: {\n            errorType: 'Feedback',\n            defaultFields: ['SystemStats', 'Settings']\n          }\n        })\n      }\n    },\n    {\n      id: 'Comfy.Help.OpenComfyUIForum',\n      icon: 'pi pi-comments',\n      label: 'Open ComfyUI Forum',\n      menubarLabel: 'ComfyUI Forum',\n      versionAdded: '1.8.2',\n      function: () => {\n        window.open('https://forum.comfy.org/', '_blank')\n      }\n    },\n    {\n      id: 'Comfy.Canvas.DeleteSelectedItems',\n      icon: 'pi pi-trash',\n      label: 'Delete Selected Items',\n      versionAdded: '1.10.5',\n      function: () => {\n        app.canvas.deleteSelected()\n        app.canvas.setDirty(true, true)\n      }\n    },\n    {\n      id: 'Comfy.Manager.CustomNodesManager',\n      icon: 'pi pi-puzzle',\n      label: 'Custom Nodes Manager',\n      versionAdded: '1.12.10',\n      function: () => {\n        dialogService.showManagerDialog()\n      }\n    },\n    {\n      id: 'Comfy.Manager.ToggleManagerProgressDialog',\n      icon: 'pi pi-spinner',\n      label: 'Toggle Progress Dialog',\n      versionAdded: '1.13.9',\n      function: () => {\n        dialogService.showManagerProgressDialog()\n      }\n    }\n  ]\n}\n", "export enum LatentPreviewMethod {\n  NoPreviews = 'none',\n  Auto = 'auto',\n  Latent2RGB = 'latent2rgb',\n  TAESD = 'taesd'\n}\n\nexport enum LogLevel {\n  DEBUG = 'DEBUG',\n  INFO = 'INFO',\n  WARNING = 'WARNING',\n  ERROR = 'ERROR',\n  CRITICAL = 'CRITICAL'\n}\n\nexport enum HashFunction {\n  MD5 = 'md5',\n  SHA1 = 'sha1',\n  SHA256 = 'sha256',\n  SHA512 = 'sha512'\n}\n\nexport enum AutoLaunch {\n  // Let server decide whether to auto launch based on the current environment\n  Auto = 'auto',\n  // Disable auto launch\n  Disable = 'disable',\n  // Enable auto launch\n  Enable = 'enable'\n}\n\nexport enum CudaMalloc {\n  // Let server decide whether to use CUDA malloc based on the current environment\n  Auto = 'auto',\n  // Disable CUDA malloc\n  Disable = 'disable',\n  // Enable CUDA malloc\n  Enable = 'enable'\n}\n\nexport enum FloatingPointPrecision {\n  AUTO = 'auto',\n  FP64 = 'fp64',\n  FP32 = 'fp32',\n  FP16 = 'fp16',\n  BF16 = 'bf16',\n  FP8E4M3FN = 'fp8_e4m3fn',\n  FP8E5M2 = 'fp8_e5m2'\n}\n\nexport enum CrossAttentionMethod {\n  Auto = 'auto',\n  Split = 'split',\n  Quad = 'quad',\n  Pytorch = 'pytorch'\n}\n\nexport enum VramManagement {\n  Auto = 'auto',\n  GPUOnly = 'gpu-only',\n  HighVram = 'highvram',\n  NormalVram = 'normalvram',\n  LowVram = 'lowvram',\n  NoVram = 'novram',\n  CPU = 'cpu'\n}\n", "import {\n  AutoLaunch,\n  CrossAttentionMethod,\n  CudaMalloc,\n  FloatingPointPrecision,\n  HashFunction,\n  LatentPreviewMethod,\n  LogLevel,\n  VramManagement\n} from '@/types/serverArgs'\nimport { FormItem } from '@/types/settingTypes'\n\nexport type ServerConfigValue = string | number | true | null | undefined\n\nexport interface ServerConfig<T> extends FormItem {\n  id: string\n  defaultValue: T\n  category?: string[]\n  // Override the default value getter with a custom function.\n  getValue?: (value: T) => Record<string, ServerConfigValue>\n}\n\nexport const WEB_ONLY_CONFIG_ITEMS: ServerConfig<any>[] = [\n  // Launch behavior\n  {\n    id: 'auto-launch',\n    name: 'Automatically opens in the browser on startup',\n    category: ['Launch'],\n    type: 'combo',\n    options: Object.values(AutoLaunch),\n    defaultValue: AutoLaunch.Auto,\n    getValue: (value: AutoLaunch) => {\n      switch (value) {\n        case AutoLaunch.Auto:\n          return {}\n        case AutoLaunch.Enable:\n          return {\n            ['auto-launch']: true\n          }\n        case AutoLaunch.Disable:\n          return {\n            ['disable-auto-launch']: true\n          }\n      }\n    }\n  }\n]\n\nexport const SERVER_CONFIG_ITEMS: ServerConfig<any>[] = [\n  // Network settings\n  {\n    id: 'listen',\n    name: 'Host: The IP address to listen on',\n    category: ['Network'],\n    type: 'text',\n    defaultValue: '127.0.0.1'\n  },\n  {\n    id: 'port',\n    name: 'Port: The port to listen on',\n    category: ['Network'],\n    type: 'number',\n    // The default launch port for desktop app is 8000 instead of 8188.\n    defaultValue: 8000\n  },\n  {\n    id: 'tls-keyfile',\n    name: 'TLS Key File: Path to TLS key file for HTTPS',\n    category: ['Network'],\n    type: 'text',\n    defaultValue: ''\n  },\n  {\n    id: 'tls-certfile',\n    name: 'TLS Certificate File: Path to TLS certificate file for HTTPS',\n    category: ['Network'],\n    type: 'text',\n    defaultValue: ''\n  },\n  {\n    id: 'enable-cors-header',\n    name: 'Enable CORS header: Use \"*\" for all origins or specify domain',\n    category: ['Network'],\n    type: 'text',\n    defaultValue: ''\n  },\n  {\n    id: 'max-upload-size',\n    name: 'Maximum upload size (MB)',\n    category: ['Network'],\n    type: 'number',\n    defaultValue: 100\n  },\n\n  // CUDA settings\n  {\n    id: 'cuda-device',\n    name: 'CUDA device index to use',\n    category: ['CUDA'],\n    type: 'number',\n    defaultValue: null\n  },\n  {\n    id: 'cuda-malloc',\n    name: 'Use CUDA malloc for memory allocation',\n    category: ['CUDA'],\n    type: 'combo',\n    options: Object.values(CudaMalloc),\n    defaultValue: CudaMalloc.Auto,\n    getValue: (value: CudaMalloc) => {\n      switch (value) {\n        case CudaMalloc.Auto:\n          return {}\n        case CudaMalloc.Enable:\n          return {\n            ['cuda-malloc']: true\n          }\n        case CudaMalloc.Disable:\n          return {\n            ['disable-cuda-malloc']: true\n          }\n      }\n    }\n  },\n\n  // Precision settings\n  {\n    id: 'global-precision',\n    name: 'Global floating point precision',\n    category: ['Inference'],\n    type: 'combo',\n    options: [\n      FloatingPointPrecision.AUTO,\n      FloatingPointPrecision.FP32,\n      FloatingPointPrecision.FP16\n    ],\n    defaultValue: FloatingPointPrecision.AUTO,\n    tooltip: 'Global floating point precision',\n    getValue: (value: FloatingPointPrecision) => {\n      switch (value) {\n        case FloatingPointPrecision.AUTO:\n          return {}\n        case FloatingPointPrecision.FP32:\n          return {\n            ['force-fp32']: true\n          }\n        case FloatingPointPrecision.FP16:\n          return {\n            ['force-fp16']: true\n          }\n        default:\n          return {}\n      }\n    }\n  },\n\n  // UNET precision\n  {\n    id: 'unet-precision',\n    name: 'UNET precision',\n    category: ['Inference'],\n    type: 'combo',\n    options: [\n      FloatingPointPrecision.AUTO,\n      FloatingPointPrecision.FP64,\n      FloatingPointPrecision.FP32,\n      FloatingPointPrecision.FP16,\n      FloatingPointPrecision.BF16,\n      FloatingPointPrecision.FP8E4M3FN,\n      FloatingPointPrecision.FP8E5M2\n    ],\n    defaultValue: FloatingPointPrecision.AUTO,\n    tooltip: 'UNET precision',\n    getValue: (value: FloatingPointPrecision) => {\n      switch (value) {\n        case FloatingPointPrecision.AUTO:\n          return {}\n        default:\n          return {\n            [`${value.toLowerCase()}-unet`]: true\n          }\n      }\n    }\n  },\n\n  // VAE settings\n  {\n    id: 'vae-precision',\n    name: 'VAE precision',\n    category: ['Inference'],\n    type: 'combo',\n    options: [\n      FloatingPointPrecision.AUTO,\n      FloatingPointPrecision.FP16,\n      FloatingPointPrecision.FP32,\n      FloatingPointPrecision.BF16\n    ],\n    defaultValue: FloatingPointPrecision.AUTO,\n    tooltip: 'VAE precision',\n    getValue: (value: FloatingPointPrecision) => {\n      switch (value) {\n        case FloatingPointPrecision.AUTO:\n          return {}\n        default:\n          return {\n            [`${value.toLowerCase()}-vae`]: true\n          }\n      }\n    }\n  },\n  {\n    id: 'cpu-vae',\n    name: 'Run VAE on CPU',\n    category: ['Inference'],\n    type: 'boolean',\n    defaultValue: false\n  },\n\n  // Text Encoder settings\n  {\n    id: 'text-encoder-precision',\n    name: 'Text Encoder precision',\n    category: ['Inference'],\n    type: 'combo',\n    options: [\n      FloatingPointPrecision.AUTO,\n      FloatingPointPrecision.FP8E4M3FN,\n      FloatingPointPrecision.FP8E5M2,\n      FloatingPointPrecision.FP16,\n      FloatingPointPrecision.FP32\n    ],\n    defaultValue: FloatingPointPrecision.AUTO,\n    tooltip: 'Text Encoder precision',\n    getValue: (value: FloatingPointPrecision) => {\n      switch (value) {\n        case FloatingPointPrecision.AUTO:\n          return {}\n        default:\n          return {\n            [`${value.toLowerCase()}-text-enc`]: true\n          }\n      }\n    }\n  },\n\n  // Memory and performance settings\n  {\n    id: 'force-channels-last',\n    name: 'Force channels-last memory format',\n    category: ['Memory'],\n    type: 'boolean',\n    defaultValue: false\n  },\n  {\n    id: 'directml',\n    name: 'DirectML device index',\n    category: ['Memory'],\n    type: 'number',\n    defaultValue: null\n  },\n  {\n    id: 'disable-ipex-optimize',\n    name: 'Disable IPEX optimization',\n    category: ['Memory'],\n    type: 'boolean',\n    defaultValue: false\n  },\n\n  // Preview settings\n  {\n    id: 'preview-method',\n    name: 'Method used for latent previews',\n    category: ['Preview'],\n    type: 'combo',\n    options: Object.values(LatentPreviewMethod),\n    defaultValue: LatentPreviewMethod.NoPreviews\n  },\n  {\n    id: 'preview-size',\n    name: 'Size of preview images',\n    category: ['Preview'],\n    type: 'slider',\n    defaultValue: 512,\n    attrs: {\n      min: 128,\n      max: 2048,\n      step: 128\n    }\n  },\n\n  // Cache settings\n  {\n    id: 'cache-classic',\n    name: 'Use classic cache system',\n    category: ['Cache'],\n    type: 'boolean',\n    defaultValue: false\n  },\n  {\n    id: 'cache-lru',\n    name: 'Use LRU caching with a maximum of N node results cached.',\n    category: ['Cache'],\n    type: 'number',\n    defaultValue: null,\n    tooltip: 'May use more RAM/VRAM.'\n  },\n\n  // Attention settings\n  {\n    id: 'cross-attention-method',\n    name: 'Cross attention method',\n    category: ['Attention'],\n    type: 'combo',\n    options: Object.values(CrossAttentionMethod),\n    defaultValue: CrossAttentionMethod.Auto,\n    getValue: (value: CrossAttentionMethod) => {\n      switch (value) {\n        case CrossAttentionMethod.Auto:\n          return {}\n        default:\n          return {\n            [`use-${value.toLowerCase()}-cross-attention`]: true\n          }\n      }\n    }\n  },\n  {\n    id: 'disable-xformers',\n    name: 'Disable xFormers optimization',\n    type: 'boolean',\n    defaultValue: false\n  },\n  {\n    id: 'force-upcast-attention',\n    name: 'Force attention upcast',\n    category: ['Attention'],\n    type: 'boolean',\n    defaultValue: false\n  },\n  {\n    id: 'dont-upcast-attention',\n    name: 'Prevent attention upcast',\n    category: ['Attention'],\n    type: 'boolean',\n    defaultValue: false\n  },\n\n  // VRAM management\n  {\n    id: 'vram-management',\n    name: 'VRAM management mode',\n    category: ['Memory'],\n    type: 'combo',\n    options: Object.values(VramManagement),\n    defaultValue: VramManagement.Auto,\n    getValue: (value: VramManagement) => {\n      switch (value) {\n        case VramManagement.Auto:\n          return {}\n        default:\n          return {\n            [value]: true\n          }\n      }\n    }\n  },\n  {\n    id: 'reserve-vram',\n    name: 'Reserved VRAM (GB)',\n    category: ['Memory'],\n    type: 'number',\n    defaultValue: null,\n    tooltip:\n      'Set the amount of vram in GB you want to reserve for use by your OS/other software. By default some amount is reverved depending on your OS.'\n  },\n\n  // Misc settings\n  {\n    id: 'default-hashing-function',\n    name: 'Default hashing function for model files',\n    type: 'combo',\n    options: Object.values(HashFunction),\n    defaultValue: HashFunction.SHA256\n  },\n  {\n    id: 'disable-smart-memory',\n    name: 'Disable smart memory management',\n    tooltip:\n      'Force ComfyUI to aggressively offload to regular ram instead of keeping models in vram when it can.',\n    category: ['Memory'],\n    type: 'boolean',\n    defaultValue: false\n  },\n  {\n    id: 'deterministic',\n    name: 'Make pytorch use slower deterministic algorithms when it can.',\n    type: 'boolean',\n    defaultValue: false,\n    tooltip: 'Note that this might not make images deterministic in all cases.'\n  },\n  {\n    id: 'fast',\n    name: 'Enable some untested and potentially quality deteriorating optimizations.',\n    type: 'boolean',\n    defaultValue: false\n  },\n  {\n    id: 'dont-print-server',\n    name: \"Don't print server output to console.\",\n    type: 'boolean',\n    defaultValue: false\n  },\n  {\n    id: 'disable-metadata',\n    name: 'Disable saving prompt metadata in files.',\n    type: 'boolean',\n    defaultValue: false\n  },\n  {\n    id: 'disable-all-custom-nodes',\n    name: 'Disable loading all custom nodes.',\n    type: 'boolean',\n    defaultValue: false\n  },\n  {\n    id: 'log-level',\n    name: 'Logging verbosity level',\n    type: 'combo',\n    options: Object.values(LogLevel),\n    defaultValue: LogLevel.INFO,\n    getValue: (value: LogLevel) => {\n      return {\n        verbose: value\n      }\n    }\n  },\n  // Directories\n  {\n    id: 'input-directory',\n    name: 'Input directory',\n    category: ['Directories'],\n    type: 'text',\n    defaultValue: ''\n  },\n  {\n    id: 'output-directory',\n    name: 'Output directory',\n    category: ['Directories'],\n    type: 'text',\n    defaultValue: ''\n  }\n]\n", "import { api } from '@/scripts/api'\nimport { app } from '@/scripts/app'\nimport {\n  useQueuePendingTaskCountStore,\n  useQueueSettingsStore\n} from '@/stores/queueStore'\n\nexport function setupAutoQueueHandler() {\n  const queueCountStore = useQueuePendingTaskCountStore()\n  const queueSettingsStore = useQueueSettingsStore()\n\n  let graphHasChanged = false\n  let internalCount = 0 // Use an internal counter here so it is instantly updated when re-queuing\n  api.addEventListener('graphChanged', () => {\n    if (queueSettingsStore.mode === 'change') {\n      if (internalCount) {\n        graphHasChanged = true\n      } else {\n        graphHasChanged = false\n        app.queuePrompt(0, queueSettingsStore.batchCount)\n        internalCount++\n      }\n    }\n  })\n\n  queueCountStore.$subscribe(\n    () => {\n      internalCount = queueCountStore.count\n      if (!internalCount && !app.lastExecutionError) {\n        if (\n          queueSettingsStore.mode === 'instant' ||\n          (queueSettingsStore.mode === 'change' && graphHasChanged)\n        ) {\n          graphHasChanged = false\n          app.queuePrompt(0, queueSettingsStore.batchCount)\n        }\n      }\n    },\n    { detached: true }\n  )\n}\n", "<template>\n  <div class=\"comfyui-body grid h-screen w-screen overflow-hidden\">\n    <div class=\"comfyui-body-top\" id=\"comfyui-body-top\">\n      <TopMenubar v-if=\"useNewMenu === 'Top'\" />\n    </div>\n    <div class=\"comfyui-body-bottom\" id=\"comfyui-body-bottom\">\n      <TopMenubar v-if=\"useNewMenu === 'Bottom'\" />\n    </div>\n    <div class=\"comfyui-body-left\" id=\"comfyui-body-left\" />\n    <div class=\"comfyui-body-right\" id=\"comfyui-body-right\" />\n    <div class=\"graph-canvas-container\" id=\"graph-canvas-container\">\n      <GraphCanvas @ready=\"onGraphReady\" />\n    </div>\n  </div>\n\n  <GlobalToast />\n  <UnloadWindowConfirmDialog v-if=\"!isElectron()\" />\n  <BrowserTabTitle />\n  <MenuHamburger />\n</template>\n\n<script setup lang=\"ts\">\nimport { useEventListener } from '@vueuse/core'\nimport type { ToastMessageOptions } from 'primevue/toast'\nimport { useToast } from 'primevue/usetoast'\nimport { computed, onBeforeUnmount, onMounted, watch, watchEffect } from 'vue'\nimport { useI18n } from 'vue-i18n'\n\nimport BrowserTabTitle from '@/components/BrowserTabTitle.vue'\nimport MenuHamburger from '@/components/MenuHamburger.vue'\nimport UnloadWindowConfirmDialog from '@/components/dialog/UnloadWindowConfirmDialog.vue'\nimport GraphCanvas from '@/components/graph/GraphCanvas.vue'\nimport GlobalToast from '@/components/toast/GlobalToast.vue'\nimport TopMenubar from '@/components/topbar/TopMenubar.vue'\nimport { useCoreCommands } from '@/composables/useCoreCommands'\nimport { useErrorHandling } from '@/composables/useErrorHandling'\nimport { SERVER_CONFIG_ITEMS } from '@/constants/serverConfig'\nimport { i18n } from '@/i18n'\nimport { StatusWsMessageStatus } from '@/schemas/apiSchema'\nimport { api } from '@/scripts/api'\nimport { app } from '@/scripts/app'\nimport { setupAutoQueueHandler } from '@/services/autoQueueService'\nimport { useKeybindingService } from '@/services/keybindingService'\nimport { useCommandStore } from '@/stores/commandStore'\nimport { useExecutionStore } from '@/stores/executionStore'\nimport { useMenuItemStore } from '@/stores/menuItemStore'\nimport { useModelStore } from '@/stores/modelStore'\nimport { useNodeDefStore, useNodeFrequencyStore } from '@/stores/nodeDefStore'\nimport {\n  useQueuePendingTaskCountStore,\n  useQueueStore\n} from '@/stores/queueStore'\nimport { useServerConfigStore } from '@/stores/serverConfigStore'\nimport { useSettingStore } from '@/stores/settingStore'\nimport { useBottomPanelStore } from '@/stores/workspace/bottomPanelStore'\nimport { useColorPaletteStore } from '@/stores/workspace/colorPaletteStore'\nimport { useSidebarTabStore } from '@/stores/workspace/sidebarTabStore'\nimport { useWorkspaceStore } from '@/stores/workspaceStore'\nimport { electronAPI, isElectron } from '@/utils/envUtil'\n\nsetupAutoQueueHandler()\n\nconst { t } = useI18n()\nconst toast = useToast()\nconst settingStore = useSettingStore()\nconst executionStore = useExecutionStore()\nconst colorPaletteStore = useColorPaletteStore()\nconst queueStore = useQueueStore()\n\nwatch(\n  () => colorPaletteStore.completedActivePalette,\n  (newTheme) => {\n    const DARK_THEME_CLASS = 'dark-theme'\n    if (newTheme.light_theme) {\n      document.body.classList.remove(DARK_THEME_CLASS)\n    } else {\n      document.body.classList.add(DARK_THEME_CLASS)\n    }\n\n    if (isElectron()) {\n      electronAPI().changeTheme({\n        color: 'rgba(0, 0, 0, 0)',\n        symbolColor: newTheme.colors.comfy_base['input-text']\n      })\n    }\n  },\n  { immediate: true }\n)\n\nif (isElectron()) {\n  watch(\n    () => queueStore.tasks,\n    (newTasks, oldTasks) => {\n      // Report tasks that previously running but are now completed (i.e. in history)\n      const oldRunningTaskIds = new Set(\n        oldTasks.filter((task) => task.isRunning).map((task) => task.promptId)\n      )\n      newTasks\n        .filter(\n          (task) => oldRunningTaskIds.has(task.promptId) && task.isHistory\n        )\n        .forEach((task) => {\n          electronAPI().Events.incrementUserProperty(\n            `execution:${task.displayStatus.toLowerCase()}`,\n            1\n          )\n          electronAPI().Events.trackEvent('execution', {\n            status: task.displayStatus.toLowerCase()\n          })\n        })\n    },\n    { deep: true }\n  )\n}\n\nwatchEffect(() => {\n  const fontSize = settingStore.get('Comfy.TextareaWidget.FontSize')\n  document.documentElement.style.setProperty(\n    '--comfy-textarea-font-size',\n    `${fontSize}px`\n  )\n})\n\nwatchEffect(() => {\n  const padding = settingStore.get('Comfy.TreeExplorer.ItemPadding')\n  document.documentElement.style.setProperty(\n    '--comfy-tree-explorer-item-padding',\n    `${padding}px`\n  )\n})\n\nwatchEffect(() => {\n  const locale = settingStore.get('Comfy.Locale')\n  if (locale) {\n    i18n.global.locale.value = locale as 'en' | 'zh' | 'ru' | 'ja'\n  }\n})\n\nconst useNewMenu = computed(() => {\n  return settingStore.get('Comfy.UseNewMenu')\n})\nwatchEffect(() => {\n  if (useNewMenu.value === 'Disabled') {\n    app.ui.menuContainer.style.setProperty('display', 'block')\n    app.ui.restoreMenuPosition()\n  } else {\n    app.ui.menuContainer.style.setProperty('display', 'none')\n  }\n})\n\nwatchEffect(() => {\n  queueStore.maxHistoryItems = settingStore.get('Comfy.Queue.MaxHistoryItems')\n})\n\nconst init = () => {\n  const coreCommands = useCoreCommands()\n  useCommandStore().registerCommands(coreCommands)\n  useMenuItemStore().registerCoreMenuCommands()\n  useKeybindingService().registerCoreKeybindings()\n  useSidebarTabStore().registerCoreSidebarTabs()\n  useBottomPanelStore().registerCoreBottomPanelTabs()\n  app.extensionManager = useWorkspaceStore()\n}\n\nconst queuePendingTaskCountStore = useQueuePendingTaskCountStore()\nconst onStatus = async (e: CustomEvent<StatusWsMessageStatus>) => {\n  queuePendingTaskCountStore.update(e)\n  await queueStore.update()\n}\n\nconst reconnectingMessage: ToastMessageOptions = {\n  severity: 'error',\n  summary: t('g.reconnecting')\n}\n\nconst onReconnecting = () => {\n  toast.remove(reconnectingMessage)\n  toast.add(reconnectingMessage)\n}\n\nconst onReconnected = () => {\n  toast.remove(reconnectingMessage)\n  toast.add({\n    severity: 'success',\n    summary: t('g.reconnected'),\n    life: 2000\n  })\n}\n\nonMounted(() => {\n  api.addEventListener('status', onStatus)\n  api.addEventListener('reconnecting', onReconnecting)\n  api.addEventListener('reconnected', onReconnected)\n  executionStore.bindExecutionEvents()\n\n  try {\n    init()\n  } catch (e) {\n    console.error('Failed to init ComfyUI frontend', e)\n  }\n})\n\nonBeforeUnmount(() => {\n  api.removeEventListener('status', onStatus)\n  api.removeEventListener('reconnecting', onReconnecting)\n  api.removeEventListener('reconnected', onReconnected)\n  executionStore.unbindExecutionEvents()\n})\n\nuseEventListener(window, 'keydown', useKeybindingService().keybindHandler)\n\nconst { wrapWithErrorHandling, wrapWithErrorHandlingAsync } = useErrorHandling()\nconst onGraphReady = () => {\n  requestIdleCallback(\n    () => {\n      // Setting values now available after comfyApp.setup.\n      // Load keybindings.\n      wrapWithErrorHandling(useKeybindingService().registerUserKeybindings)()\n\n      // Load server config\n      wrapWithErrorHandling(useServerConfigStore().loadServerConfig)(\n        SERVER_CONFIG_ITEMS,\n        settingStore.get('Comfy.Server.ServerConfigValues')\n      )\n\n      // Load model folders\n      wrapWithErrorHandlingAsync(useModelStore().loadModelFolders)()\n\n      // Non-blocking load of node frequencies\n      wrapWithErrorHandlingAsync(useNodeFrequencyStore().loadNodeFrequencies)()\n\n      // Node defs now available after comfyApp.setup.\n      // Explicitly initialize nodeSearchService to avoid indexing delay when\n      // node search is triggered\n      useNodeDefStore().nodeSearchService.endsWithFilterStartSequence('')\n    },\n    { timeout: 1000 }\n  )\n}\n</script>\n\n<style scoped>\n.comfyui-body {\n  grid-template-columns: auto 1fr auto;\n  grid-template-rows: auto 1fr auto;\n}\n\n/**\n  +------------------+------------------+------------------+\n  |                                                        |\n  |  .comfyui-body-                                        |\n  |       top                                              |\n  | (spans all cols)                                       |\n  |                                                        |\n  +------------------+------------------+------------------+\n  |                  |                  |                  |\n  | .comfyui-body-   |   #graph-canvas  | .comfyui-body-   |\n  |      left        |                  |      right       |\n  |                  |                  |                  |\n  |                  |                  |                  |\n  +------------------+------------------+------------------+\n  |                                                        |\n  |  .comfyui-body-                                        |\n  |      bottom                                            |\n  | (spans all cols)                                       |\n  |                                                        |\n  +------------------+------------------+------------------+\n*/\n\n.comfyui-body-top {\n  order: -5;\n  /* Span across all columns */\n  grid-column: 1/-1;\n  /* Position at the first row */\n  grid-row: 1;\n  /* Top menu bar dropdown needs to be above of graph canvas splitter overlay which is z-index: 999 */\n  /* Top menu bar z-index needs to be higher than bottom menu bar z-index as by default\n  pysssss's image feed is located at body-bottom, and it can overlap with the queue button, which\n  is located in body-top. */\n  z-index: 1001;\n  display: flex;\n  flex-direction: column;\n}\n\n.comfyui-body-left {\n  order: -4;\n  /* Position in the first column */\n  grid-column: 1;\n  /* Position below the top element */\n  grid-row: 2;\n  z-index: 10;\n  display: flex;\n}\n\n.graph-canvas-container {\n  width: 100%;\n  height: 100%;\n  order: -3;\n  grid-column: 2;\n  grid-row: 2;\n  position: relative;\n  overflow: hidden;\n}\n\n.comfyui-body-right {\n  order: -2;\n  z-index: 10;\n  grid-column: 3;\n  grid-row: 2;\n}\n\n.comfyui-body-bottom {\n  order: 4;\n  /* Span across all columns */\n  grid-column: 1/-1;\n  grid-row: 3;\n  /* Bottom menu bar dropdown needs to be above of graph canvas splitter overlay which is z-index: 999 */\n  z-index: 1000;\n  display: flex;\n  flex-direction: column;\n}\n</style>\n"], "names": ["t", "comfyApp", "_", "LinkReleaseTriggerMode", "LinkReleaseTriggerAction", "_sfc_main", "AutoComplete", "options", "widget", "ContextMenu", "clamp", "LatentPreviewMethod", "LogLevel", "HashFunction", "AutoLaunch", "CudaMalloc", "FloatingPointPrecision", "CrossAttentionMethod", "VramManagement"], "mappings": ";;;;;;;;AAcA,MAAM,gBAAgB;AACtB,MAAM,eAAe;;;;AAErB,UAAM,iBAAiB;AACvB,UAAM,gBAAgB;AAAA,MAAS,MAC7B,eAAe,SAAS,KAAK,IAAI,eAAe,iBAAiB;AAAA,IAAA;AAGnE,UAAM,eAAe;AACrB,UAAM,kBAAkB;AAAA,MACtB,MAAM,aAAa,IAAI,kBAAkB,MAAM;AAAA,IAAA;AAGjD,UAAM,gBAAgB;AACtB,UAAM,gBAAgB;AAAA,MAAS,MAC7B,cAAc,gBAAgB,cAC9B,CAAC,cAAc,gBAAgB,cAC3B,OACA;AAAA,IAAA;AAEA,UAAA,mBAAmB,SAAS,MAAM;AAChC,YAAA,eAAe,cAAc,gBAAgB;AACnD,aAAO,eACH,cAAc,QAAQ,eAAe,eACrC;AAAA,IAAA,CACL;AAED,UAAM,qBAAqB;AAAA,MAAS,MAClC,eAAe,iBAAiB,eAAe,wBAC3C,GAAG,cAAc,KAAK,IAAI,eAAe,qBAAqB,MAAM,eAAe,cAAc,IAAI,KACrG;AAAA,IAAA;AAGN,UAAM,gBAAgB;AAAA,MACpB,MACE,cAAc,SACb,gBAAgB,QAAQ,iBAAiB,QAAQ;AAAA,IAAA;AAGtD,UAAM,QAAQ,SAAS,MAAM,mBAAmB,SAAS,cAAc,KAAK;AAC5E,aAAS,KAAK;;;;;;;;;;ACxBd,UAAM,iBAAiB;AACvB,UAAM,eAAe;AACrB,UAAM,gBAAgB,6BAAM;AAC1B,qBAAe,YAAY;AAAA,IAAA,GADP;AAItB,gBAAY,MAAM;AAChB,UAAI,aAAa,IAAI,kBAAkB,MAAM,YAAY;AACvD;AAAA,MACF;AACA,UAAI,eAAe,WAAW;AACxB,YAAA,GAAG,cAAc,MAAM,UAAU;AAAA,MAAA,OAChC;AACD,YAAA,GAAG,cAAc,MAAM,UAAU;AAAA,MACvC;AAAA,IAAA,CACD;AAED,UAAM,cAAc,SAAS,MAAM,aAAa,IAAI,kBAAkB,CAAC;AACvE,UAAM,cAAc;AAAA,MAAwB;AAAA;AAAA;AAAA,QAG1C,YAAY,UAAU,WAClB,EAAE,QAAQ,OAAO,OAAO,MAAA,IACxB,EAAE,KAAK,OAAO,OAAO,MAAM;AAAA;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpCjC,UAAM,eAAe;AACrB,UAAM,gBAAgB;AAEhB,UAAA,qBAAqB,wBAAC,UAA6B;AACvD,UACE,aAAa,IAAI,iCAAiC,KAClD,cAAc,kBAAkB,SAAS,GACzC;AACA,cAAM,eAAe;AACd,eAAA;AAAA,MACT;AACO,aAAA;AAAA,IAAA,GARkB;AAW3B,cAAU,MAAM;AACP,aAAA,iBAAiB,gBAAgB,kBAAkB;AAAA,IAAA,CAC3D;AAED,oBAAgB,MAAM;AACb,aAAA,oBAAoB,gBAAgB,kBAAkB;AAAA,IAAA,CAC9D;;;;;;;;;ACmBD,UAAM,eAAe;AACrB,UAAM,kBAAkB;AAAA,MAA2B,MACjD,aAAa,IAAI,wBAAwB;AAAA,IAAA;AAG3C,UAAM,sBAAsB;AAAA,MAC1B,MAAM,mBAAmB,EAAE,qBAAqB;AAAA,IAAA;AAElD,UAAM,qBAAqB;AAAA,MACzB,MAAM,oBAAsB,EAAA;AAAA,IAAA;AAE9B,UAAM,qBAAqB;AAAA,MACzB,MAAM,mBAAqB,EAAA;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/C7B,UAAM,QAAQ;AAIR,UAAA,uBAAuB,wBAAC,WAA4B,OAAoB;AAC5E,gBAAU,OAAO,EAAE;AAAA,IAAA,GADQ;AAI7B,oBAAgB,MAAM;AACpB,UAAI,MAAM,UAAU,SAAS,YAAY,MAAM,UAAU,SAAS;AAChE,cAAM,UAAU;MAClB;AAAA,IAAA,CACD;;;;;;;;;;;;;;;;;;;;;;;ACiBD,UAAM,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnClB,SAAS,sBAAsB;AACpC,QAAM,cAAc;AACpB,QAAM,QAAQ,IAAmB;AAAA,IAC/B,UAAU;AAAA,IACV,MAAM;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,EAAA,CACT;AAQK,QAAA,iBAAiB,wBACrB,QACA,eACG;AACG,UAAA,EAAE,KAAK,MAAM,QAAQ,YAAY,QAAQ,IAAI,SAAS,EAAM,IAAA;AAClE,UAAM,CAAC,MAAM,GAAG,IAAI,IAAI,qBAAqB,GAAG;AAC1C,UAAA,CAAC,OAAO,MAAM,IAAI;AAExB,UAAM,QAAQ;AAAA,MACZ,GAAG,MAAM;AAAA,MACT,MAAM,GAAG,IAAI;AAAA,MACb,KAAK,GAAG,GAAG;AAAA,MACX,OAAO,GAAG,QAAQ,KAAK;AAAA,MACvB,QAAQ,GAAG,SAAS,KAAK;AAAA,MACzB,GAAG;AAAA,IAAA;AAAA,EACL,GAfqB;AAyBjB,QAAA,8BAA8B,wBAClC,QACA,eACG;AACG,UAAA,EAAE,KAAK,MAAM,QAAQ,YAAY,QAAQ,IAAI,SAAS,EAAM,IAAA;AAClE,UAAM,CAAC,MAAM,GAAG,IAAI,IAAI,qBAAqB,GAAG;AAC1C,UAAA,CAAC,OAAO,MAAM,IAAI;AAExB,UAAM,QAAQ;AAAA,MACZ,GAAG,MAAM;AAAA,MACT,iBAAiB;AAAA,MACjB,WAAW,SAAS,KAAK;AAAA,MACzB,MAAM,GAAG,IAAI;AAAA,MACb,KAAK,GAAG,GAAG;AAAA,MACX,OAAO,GAAG,KAAK;AAAA,MACf,QAAQ,GAAG,MAAM;AAAA,MACjB,GAAG;AAAA,IAAA;AAAA,EACL,GAjBkC;AAoB7B,SAAA;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EAAA;AAEJ;AAlEgB;ACHhB,SAAS,UAAU,GAAS,GAAkD;AAC5E,QAAM,KAAK,KAAK,IAAI,EAAE,GAAG,EAAE,CAAC;AAC5B,QAAM,KAAK,KAAK,IAAI,EAAE,GAAG,EAAE,CAAC;AACtB,QAAA,KAAK,KAAK,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK;AAC1C,QAAA,KAAK,KAAK,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM;AAE9C,MAAA,MAAM,MAAM,MAAM,IAAI;AACjB,WAAA;AAAA,EACT;AAEA,SAAO,CAAC,IAAI,IAAI,KAAK,IAAI,KAAK,EAAE;AAClC;AAXS;AAiBF,MAAM,iBAAiB,wBAAC,UAA2B,OAAO;AACzD,QAAA,QAAQ,IAAmB,CAAA,CAAE;AAC7B,QAAA,EAAE,SAAS,EAAM,IAAA;AAKvB,QAAM,oBAAoB,wBACxB,aACA,YACA,YACA,iBAQW;AACP,QAAA,CAAC,cAAc,cAAc;AACzB,YAAA,EAAE,OAAO,OAAW,IAAA;AAG1B,YAAM,eAAe;AAAA,QACnB;AAAA,UACE,GAAG,YAAY,OAAO,WAAW;AAAA,UACjC,GAAG,YAAY,MAAM,WAAW;AAAA,UAChC,OAAO,YAAY;AAAA,UACnB,QAAQ,YAAY;AAAA,QACtB;AAAA,QACA;AAAA,UACE,IAAI,aAAa,IAAI,OAAO,CAAC,IAAI,UAAU;AAAA,UAC3C,IAAI,aAAa,IAAI,OAAO,CAAC,IAAI,UAAU;AAAA,UAC3C,QAAQ,aAAa,QAAQ,IAAI,UAAU;AAAA,UAC3C,SAAS,aAAa,SAAS,IAAI,UAAU;AAAA,QAC/C;AAAA,MAAA;AAGF,UAAI,CAAC,cAAc;AACV,eAAA;AAAA,MACT;AAGM,YAAA,SACH,aAAa,CAAC,IAAI,YAAY,OAAO,WAAW,QAAQ,QAAQ;AAC7D,YAAA,SACH,aAAa,CAAC,IAAI,YAAY,MAAM,WAAW,OAAO,QAAQ;AACjE,YAAM,YAAY,aAAa,CAAC,IAAI,QAAQ;AAC5C,YAAM,aAAa,aAAa,CAAC,IAAI,QAAQ;AAEtC,aAAA,2BAA2B,KAAK,UAAU,KAAK,IAAI,KAAK,UAAU,KAAK,MAAM,SAAS,KAAK,KAAK,UAAU,KAAK,MAAM,SAAS,UAAU,KAAK,MAAM,UAAU,MAAM,KAAK,SAAS,KAAK,MAAM,UAAU,MAAM,KAAK;AAAA,IAC1N;AAEO,WAAA;AAAA,EAAA,GA/CiB;AAqD1B,QAAM,iBAAiB,wBACrB,SACA,eACA,YACA,iBAQG;AACG,UAAA,cAAc,QAAQ;AACtB,UAAA,aAAa,cAAc;AAEjC,UAAM,WAAW;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IAAA;AAGF,UAAM,QAAQ;AAAA,MACZ,UAAU,YAAY;AAAA,MACtB,YAAY;AAAA,IAAA;AAAA,EACd,GA1BqB;AA6BhB,SAAA;AAAA,IACL;AAAA,IACA;AAAA,EAAA;AAEJ,GA7F8B;;;;;;;;;;ACU9B,UAAM,OAAO;AAIb,UAAM,gBAAgB;AAEtB,UAAM,EAAE,OAAO,eAAe,gCAC5B,oBAAoB;AACtB,UAAM,EAAE,OAAO,eAAe,mBAAmB,eAAe;AAC1D,UAAA,QAAQ,SAAwB,OAAO;AAAA,MAC3C,GAAG,cAAc;AAAA,MACjB,GAAI,kBAAkB,QAAQ,cAAc,QAAQ,CAAC;AAAA,MACrD,QAAQ,QAAA,YAAY;AAAA,MACpB,eAAe,QAAW,YAAC,WAAW,SAAS;AAAA,IAC/C,EAAA;AAEF,UAAM,cAAc;AACpB,UAAM,eAAe;AACrB,UAAM,oBAAoB;AAAA,MAAS,MACjC,aAAa,IAAI,0BAA0B;AAAA,IAAA;AAG7C,UAAM,oBAAoB,6BAAM;AAC9B,YAAM,WAAW,YAAY;AAC7B,UAAI,CAAC,YAAY,CAAC,cAAc,MAAO;AAEvC,YAAM,eAAe,OAAO;AAAA,QAC1B,SAAS,kBAAkB,CAAC;AAAA,QAC5B,CAAC;AACG,YAAA,OAAO,eAAO;AACpB,YAAM,aAAa,iBAAiB;AACpC,YAAM,aAAa,cAAc;AAC3B,YAAA,SAAS,SAAS,GAAG;AACrB,YAAA,QAAQ,SAAS,GAAG;AAC1B,YAAM,qBAAqB,aACvB;AAAA,QACE,GAAG,WAAW,CAAC;AAAA,QACf,GAAG,WAAW,CAAC;AAAA,QACf,OAAO,WAAW,CAAC;AAAA,QACnB,QAAQ,WAAW,CAAC;AAAA,QACpB;AAAA,QACA,QAAQ,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,MAE/B,IAAA;AAEJ;AAAA,QACE,cAAc;AAAA,QACd,SAAS;AAAA,QACT;AAAA,QACA;AAAA,MAAA;AAAA,IACF,GA5BwB;AA+B1B;AAAA,MACE,MAAM,QAAW;AAAA,MACjB,CAAC,aAAa;AACZ,oCAA4B,QAAQ;AACpC,YAAI,kBAAkB,OAAO;AACT;QACpB;AAAA,MACF;AAAA,MACA,EAAE,MAAM,KAAK;AAAA,IAAA;AAGf;AAAA,MACE,MAAM,QAAW,YAAC;AAAA,MAClB,CAAC,YAAY,eAAe;AACtB,YAAA,CAAC,cAAc,YAAY;AAC7B,yBAAO,QAAQ,SAAS,QAAM,MAAA;AAAA,QAChC;AAAA,MACF;AAAA,IAAA;AAGE,QAAA,YAAY,QAAM,MAAA,GAAG;AACnB,UAAA,QAAM,OAAC,QAAQ,MAAM;AACN,yBAAA,UAAU,aAAa,CAAC,UAAU;AACjD,cAAI,CAAC,QAAA,OAAO,QAAQ,SAAS,MAAM,MAAqB,GAAG;2BAClD,QAAQ;UACjB;AAAA,QAAA,CACD;AAAA,MACH;AAEW,iBAAA,OAAO,QAAA,OAAO,QAAQ,YAAY,CAAC,SAAS,OAAO,GAAG;AAC/D,yBAAiB,QAAM,OAAC,SAAS,KAAK,MAAM;AAC1C,gBAAM,WAAW,YAAY;AACnB,oBAAA,WAAW,QAAM,OAAC,IAAI;AACtB,oBAAA,aAAa,QAAM,OAAC,IAAI;AAAA,QAAA,CACnC;AAAA,MACH;AAAA,IACF;AAEA,UAAM,YAAY,QAAA,OAAO,KAAK,YAAY;AAC1C,UAAM,UAAU,WAAW,SAAS,QAAM,OAAC,IAAI,GAAG;AAElD,cAAU,MAAM;AACd,UAAI,YAAY,QAAA,MAAM,KAAK,cAAc,OAAO;AAC9C,sBAAc,MAAM,YAAY,QAAA,OAAO,OAAO;AAAA,MAChD;AAAA,IAAA,CACD;;;;;;;;;;;;;;;;;;;;;;;ACzGD,MAAM,iBAAiB;;;;AATvB,UAAM,iBAAiB;AACvB,UAAM,UAAU;AAAA,MAAS,MACvB,MAAM;AAAA,QACJ,eAAe,gBAAgB,OAAO;AAAA,MAGxC;AAAA,IAAA;AAIF,UAAM,gBAAgB,6BAAM;AAC1B,YAAM,WAAW,YAAY;AAC7B,UAAI,CAAC,SAAU;AAEf,YAAM,aAAa,SAAS;AAC5B,iBAAW,UAAU,eAAe,gBAAgB,OAAA,GAAU;AAC5D,cAAM,OAAO,OAAO;AACpB,cAAM,cAAc,eAAe,aAAa,IAAI,OAAO,EAAE;AAE7D,YAAI,CAAC,YAAa;AAEZ,cAAA,UACJ,SAAS,cAAc,IAAI,KAC3B,EAAE,OAAO,QAAQ,cAAc,eAC/B,OAAO,UAAU;AAEnB,oBAAY,UAAU;AACtB,YAAI,SAAS;AACL,gBAAA,SAAS,OAAO,QAAQ,UAAU;AACxC,sBAAY,MAAM,CAAC,KAAK,IAAI,CAAC,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,SAAS,OAAO,CAAC;AACxE,sBAAY,OAAO;AAAA,aAChB,OAAO,SAAS,KAAK,SAAS,SAAS;AAAA,aACvC,OAAO,kBAAkB,MAAM,SAAS;AAAA,UAAA;AAG3C,sBAAY,SAAS,SAAS,OAAO,MAAM,QAAQ,IAAI,KAAK;AAC5D,sBAAY,WAAW,SAAS;AAAA,QAClC;AAAA,MACF;AAAA,IAAA,GA5BoB;AA+BtB,UAAM,cAAc;AACpB;AAAA,MACE,MAAM,YAAY;AAAA,MAClB,CAAC,aAAa;AACZ,YAAI,CAAC,SAAU;AAEf,iBAAS,mBAAmB;AAAA,UAC1B,SAAS;AAAA,UACT,MAAM;AACU;UAChB;AAAA,QAAA;AAAA,MAEJ;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACJI,UAAA,EAAE,GAAAA,OAAM;AACd,UAAM,eAAe;AACrB,UAAM,cAAc;AACpB,UAAM,eAAe;AAErB,UAAM,aAAa;AAAA,MACjB,MAAM,aAAa,IAAI,sBAAsB,MAAM,UAAU;AAAA,IAAA;AAG/D,QAAI,WAA0B;AACxB,UAAA,SAAS,wBAAC,YAAoB;AAClC,UAAI,SAAU;AACd,YAAM,MAAM,6BAAM,aAAa,QAAQ,OAAO,GAAlC;AACR;AACO,iBAAA,OAAO,YAAY,KAAK,GAAG;AAAA,IAAA,GAJzB;AAMf,UAAM,aAAa,6BAAM;AACvB,UAAI,UAAU;AACZ,sBAAc,QAAQ;AACX,mBAAA;AAAA,MACb;AAAA,IAAA,GAJiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrEnB,UAAM,eAAe;AACrB,UAAM,oBAAoB;AAE1B,UAAM,sBAAsB;AAAA,MAC1B,MAAM,aAAa,IAAI,qCAAqC;AAAA,IAAA;AAE9D,UAAM,kBAAkB;AAAA,MACtB,MAAM,aAAa,IAAI,iCAAiC;AAAA,IAAA;AAE1D,UAAM,yBAAyB;AAAA,MAC7B,MACE,aAAa,IAAI,wCAAwC;AAAA,IAAA;AAG7D,UAAM,CAAC,qBAAqB,iBAAiB,sBAAsB,GAAG,MAAM;AACtE,UAAA,OAAO,eAAe,MAAM,IAAI;AAAA,IAAA,CACrC;AAED,UAAM,eAAe;AACZ,aAAA,iBACP,SACA,WACS;AACT,aAAO,EACL,cAAc,cAAc,QAC3B,SAAS,cAAc,cAAc,cAAc;AAAA,IAExD;AARS;AAUT,cAAU,MAAM;AACd,UAAI,kBAAkB;AAAA,QACpB,MAAM;AAAA,QACN,YAAY,MAAkB;AAC5B,eAAK,gBAAgB,cAAc;AAE7B,gBAAA,QAAQ,SAAS,MAAM;AACrB,kBAAA,UAAU,aAAa,eAAe,IAAI;AAChD,mBAAO,IAAI,YAAY;AAAA,cACrB,MAAM,EAAE;AAAA,gBACN;AAAA,kBACE,iBAAiB,SAAS,gBAAgB,KAAK,IAC3C,IAAI,KAAK,EAAE,KACX;AAAA,kBACJ,iBAAiB,SAAS,uBAAuB,KAAK,IAClD,SAAS,0BAA0B,KACnC;AAAA,kBACJ,iBAAiB,SAAS,oBAAoB,KAAK,IAC/C,SAAS,YAAY,aAAa,KAClC;AAAA,gBAAA,EAEH,OAAO,CAAC,MAAM,EAAE,SAAS,CAAC,EAC1B,KAAK,GAAG;AAAA,gBACX;AAAA,kBACE,QAAQ;AAAA,gBACV;AAAA,cACF;AAAA,cACA,SACE,kBAAkB,uBAAuB,OAAO,eAC7C;AAAA,cACL,SACE,kBAAkB,uBAAuB,OAAO,eAC7C;AAAA,YAAA,CACN;AAAA,UAAA,CACF;AAED,eAAK,OAAO,KAAK,MAAM,MAAM,KAAK;AAAA,QACpC;AAAA,MAAA,CACD;AAAA,IAAA,CACF;;;;;;;;;AC5DG,QAAA;AACJ,UAAM,eAAe;AACrB,UAAM,eAAe;AACrB,UAAM,aAAa;AACb,UAAA,cAAc,IAAI,EAAE;AAC1B,UAAM,OAAO;AACb,UAAM,MAAM;AAEN,UAAA,cAAc,6BAAO,YAAY,QAAQ,IAA3B;AAEd,UAAA,cAAc,8BAAO,YAAuC;AAChE,UAAI,CAAC,QAAS;AAEd,WAAK,QAAQC,IAAS,OAAO,MAAM,CAAC,IAAI;AACxC,UAAI,QAAQA,IAAS,OAAO,MAAM,CAAC,IAAI;AACvC,kBAAY,QAAQ;AAEpB,YAAM,SAAS;AAET,YAAA,OAAO,WAAW,OAAO,sBAAsB;AACrD,UAAI,CAAC,KAAM;AAEP,UAAA,KAAK,QAAQ,OAAO,YAAY;AAClC,aAAK,QAAQA,IAAS,OAAO,MAAM,CAAC,IAAI,KAAK,QAAQ;AAAA,MACvD;AAEI,UAAA,KAAK,MAAM,GAAG;AAChB,YAAI,QAAQA,IAAS,OAAO,MAAM,CAAC,IAAI,KAAK,SAAS;AAAA,MACvD;AAAA,IAAA,GAlBkB;AAqBpB,UAAM,SAAS,6BAAM;AACb,YAAA,EAAE,OAAW,IAAAA;AACnB,YAAM,OAAO,OAAO;AACpB,UAAI,CAAC,KAAM;AAEX,YAAM,OAAO,KAAK;AAClB,YAAM,UAAU,aAAa,eAAe,KAAK,QAAQ,EAAE;AAGzD,UAAA,KAAK,eAAe,UAAU,YAC9B,OAAO,YAAY,CAAC,IAAI,KAAK,IAAI,CAAC,GAClC;AACO,eAAA,YAAY,QAAQ,WAAW;AAAA,MACxC;AAEI,UAAA,KAAK,OAAO,UAAW;AAE3B,YAAM,YAAY;AAAA,QAChB;AAAA,QACA,OAAO,YAAY,CAAC;AAAA,QACpB,OAAO,YAAY,CAAC;AAAA,QACpB,CAAC,GAAG,CAAC;AAAA,MAAA;AAEP,UAAI,cAAc,IAAI;AACpB,cAAM,YAAY,KAAK,OAAO,SAAS,EAAE;AACzC,cAAM,oBAAoB;AAAA,UACxB,YAAY,iBAAiB,KAAK,QAAQ,EAAE,CAAC,WAAW,iBAAiB,SAAS,CAAC;AAAA,UACnF,QAAQ,OAAO,SAAS,GAAG,WAAW;AAAA,QAAA;AAExC,eAAO,YAAY,iBAAiB;AAAA,MACtC;AAEA,YAAM,aAAa;AAAA,QACjB;AAAA,QACA,OAAO,YAAY,CAAC;AAAA,QACpB,OAAO,YAAY,CAAC;AAAA,QACpB,CAAC,GAAG,CAAC;AAAA,MAAA;AAEP,UAAI,eAAe,IAAI;AACrB,cAAM,oBAAoB;AAAA,UACxB,YAAY,iBAAiB,KAAK,QAAQ,EAAE,CAAC,YAAY,UAAU;AAAA,UACnE,QAAQ,QAAQ,UAAU,GAAG,WAAW;AAAA,QAAA;AAE1C,eAAO,YAAY,iBAAiB;AAAA,MACtC;AAEM,YAAA,SAASA,IAAS,OAAO,kBAAkB;AAEjD,UAAI,UAAU,CAAC,YAAY,MAAM,GAAG;AAClC,cAAM,oBAAoB;AAAA,UACxB,YAAY,iBAAiB,KAAK,QAAQ,EAAE,CAAC,WAAW,iBAAiB,OAAO,IAAI,CAAC;AAAA,UACrF,QAAQ,OAAO,OAAO,IAAI,GAAG,WAAW;AAAA,QAAA;AAGnC,eAAA,YAAY,OAAO,WAAW,iBAAiB;AAAA,MACxD;AAAA,IAAA,GAvDa;AA0DT,UAAA,cAAc,wBAAC,MAAkB;AACzB;AACZ,mBAAa,WAAW;AAEnB,UAAA,EAAE,OAAgB,aAAa,SAAU;AAC9C,oBAAc,OAAO;AAAA,QACnB;AAAA,QACA,aAAa,IAAI,6BAA6B;AAAA,MAAA;AAAA,IAChD,GARkB;AAWH,qBAAA,QAAQ,aAAa,WAAW;AAChC,qBAAA,QAAQ,SAAS,WAAW;;;;;;;;;;;;;;;;ACzG7C,UAAM,cAAc;AACpB,UAAM,EAAE,OAAO,eAAe,IAAI,oBAAoB;AAEhD,UAAA,UAAU,IAAI,KAAK;AACnB,UAAA,aAAa,IAAI,KAAK;AAEtB,UAAA,2BAA2B,wBAAC,WAAyB;AACzD,YAAM,gBAAgB,OAAO;AAClB,iBAAA,QAAQ,cAAc,OAAO;AAEpC,UAAA,CAAC,cAAc,MAAM;AACvB,gBAAQ,QAAQ;AAChB;AAAA,MACF;AAEA,cAAQ,QAAQ;AACV,YAAA,SAAS,aAAa,aAAa;AACzC,UAAI,QAAQ;AACK,uBAAA;AAAA,UACb,KAAK,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,UAC1B,MAAM,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,QAAA,CAC5B;AAAA,MACH;AAAA,IAAA,GAhB+B;AAoBjC;AAAA,MACE,MAAM,YAAY;AAAA,MAClB,CAAC,WAAgC;AAC/B,YAAI,CAAC,OAAQ;AAEb,eAAO,oBAAoB;AAAA,UAAiB,OAAO;AAAA,UAAmB,MACpE,yBAAyB,MAAM;AAAA,QAAA;AAAA,MAEnC;AAAA,MACA,EAAE,WAAW,KAAK;AAAA,IAAA;AAGpB;AAAA,MACE,MAAM;AACJ,cAAM,SAAS,YAAY;AACvB,YAAA,CAAC,OAAe,QAAA;AACb,eAAA;AAAA,UACL,OAAO,OAAO,GAAG,MAAM;AAAA,UACvB,QAAQ,CAAC,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,OAAO,GAAG,MAAM,OAAO,CAAC,CAAC;AAAA,QAAA;AAAA,MAEjE;AAAA,MACA,CAAC,UAAU;AACT,YAAI,CAAC,MAAO;AAEZ,iCAAyB,YAAY,MAAsB;AAAA,MAC7D;AAAA,IAAA;AAGF;AAAA,MACE,MAAM,YAAY,QAAQ,OAAO;AAAA,MACjC,CAAC,kBAAkB;AAKjB,YAAI,kBAAkB,OAAO;AAC3B,qBAAW,MAAM;AACf,oBAAQ,QAAQ;AAChB,qCAAyB,YAAY,MAAsB;AAAA,aAC1D,GAAG;AAAA,QAAA,OACD;AACL,kBAAQ,QAAQ;AAAA,QAClB;AAAA,MACF;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvCI,UAAA,EAAE,GAAAD,OAAM;AACd,UAAM,cAAc;AACpB,UAAM,oBAAoB;AAC1B,UAAM,eAAe;AAAA,MACnB,MAAM,kBAAkB,uBAAuB;AAAA,IAAA;AAE3C,UAAA,oBAAoB,wBAAC,UACzB,YAAY,OAAO,EAAE,WAAW,KAAK,GADb;AAGpB,UAAA,kBAAkB,IAAI,KAAK;AAWjC,UAAM,kBAA+B;AAAA,MACnC,MAAM;AAAA,MACN,eAAeA,GAAE,eAAe;AAAA,MAChC,OAAO;AAAA,QACL,MAAM,UAAU;AAAA,QAChB,OAAO,kBAAkB,UAAU,oBAAoB;AAAA,MACzD;AAAA,IAAA;AAEF,UAAM,eAA8B;AAAA,MAClC;AAAA,MACA,GAAG,OAAO,QAAQ,aAAa,WAAW,EAAE,IAAI,CAAC,CAAC,MAAM,KAAK,OAAO;AAAA,QAClE;AAAA,QACA,eAAeA,GAAE,SAAS,IAAI,EAAE;AAAA,QAChC,OAAO;AAAA,UACL,MAAM,MAAM;AAAA,UACZ,OAAO,kBAAkB,MAAM,OAAO;AAAA,QACxC;AAAA,MAAA,EACA;AAAA,IAAA;AAGE,UAAA,sBAAsB,IAAwB,IAAI;AAClD,UAAA,aAAa,wBAAC,gBAAoC;AAChD,YAAA,YAAY,aAAa,QAAQ,gBAAgB;AACvD,YAAM,oBACJ,cAAc,gBAAgB,OAC1B,OACA,aAAa,YAAY,SAAS;AAE7B,iBAAA,QAAQ,YAAY,eAAe;AACxC,YAAA,YAAY,IAAI,GAAG;AACrB,eAAK,eAAe,iBAAiB;AAAA,QACvC;AAAA,MACF;AAEY,kBAAA,QAAQ,SAAS,MAAM,IAAI;AACvC,yBAAmB,QAAQ;AAC3B,sBAAgB,QAAQ;AAAA,IAAA,GAfP;AAkBb,UAAA,qBAAqB,IAA8B,IAAI;AAC7D,UAAM,eAAe;AAAA,MAAS,MAC5B,mBAAmB,QACf,aAAa,QACX,kBAAkB,mBAAmB,OAAO,OAAO,IACnD,mBAAmB,OAAO,UAC5B;AAAA,IAAA;AAGN;AAAA,MACE,MAAM,YAAY;AAAA,MAClB,CAAC,qBAAqB;AACpB,wBAAgB,QAAQ;AACxB,4BAAoB,QAAQ;AACT,2BAAA,QAAQ,oBAAoB,gBAAgB;AAAA,MACjE;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjHF,MAAM,sBAAsB,wBAAC,WAC3B,aAAa,UAAU,OAAO,OAAO,YAAY,YADvB;AAMrB,MAAM,0BAA0B,6BAAM;AAC3C,QAAM,aAAa;AACnB,QAAM,eAAe;AACf,QAAA,gBAAgB,IAAkB,CAAA,CAAE;AACpC,QAAA,qBAAqB,IAAI,KAAK;AAEpC,cAAY,MAAM;AAChB,kBAAc,QAAQ,WAAW,cAAc,OAAO,YAAY;AAClE,uBAAmB,QACjB,WAAW,QAAQ,OAAO,OAAO,MAAM,CAAC,SAAS,CAAC,CAAC,KAAK,QAAQ,KAAK;AAAA,EAAA,CACxE;AAED,QAAM,qBAAqB;AAAA,IAAS,MAClC,cAAc,MAAM;AAAA,MAClB,CAAC,SAAS,KAAK,SAAS,OAAO,mBAAmB,KAAK,CAAC;AAAA,IAC1D;AAAA,EAAA;AAGF,QAAM,gBAAgB;AAAA,IACpB,MAAM,mBAAmB,MAAM,SAAS,KAAK,mBAAmB;AAAA,EAAA;AAGlE,iBAAe,kBAAkB;AAC3B,QAAA,CAAC,cAAc,MAAO;AAE1B,QAAI,mBAAmB,OAAO;AAC5B,mBAAa,QAAQ,8BAA8B;AAAA,IAAA,OAC9C;AACC,YAAA,QAAQ,IAAI,mBAAmB,MAAM,IAAI,CAAC,SAAS,KAAK,QAAS,CAAA,CAAC;AAAA,IAC1E;AAAA,EACF;AARe;AAUR,SAAA;AAAA,IACL;AAAA,IACA;AAAA,EAAA;AAEJ,GApCuC;;;;ACgEvC,UAAM,eAAe;AACrB,UAAM,cAAc;AACpB,UAAM,mBAAmB;AACzB,UAAM,EAAE,eAAe,gBAAgB,IAAI,wBAAwB;AACnE,UAAM,eAAe;AAAA,MAAS,MAC5B,YAAY,cAAc,KAAK,YAAY;AAAA,IAAA;AAE7C,UAAM,gBAAgB;AAAA,MAAS,MAC7B,YAAY,cAAc,KAAK,aAAa;AAAA,IAAA;AAGxC,UAAA,2BAA2B,SAAyB,MAAM;AAC9D,YAAM,aAAa,IAAI;AAAA,QACrB,YAAY,cACT;AAAA,UACC,CAAC,SACC,iBACG,iBAAiB,+BAA+B,IAAI,EACpD,KAAK;AAAA,UAEX,KAAK;AAAA,MAAA;AAEV,aAAO,MAAM,KAAK,UAAU,EACzB,IAAI,CAAC,cAAc,aAAa,WAAW,SAAS,CAAC,EACrD,OAAO,CAAC,YAAY,YAAY,MAAS;AAAA,IAAA,CAC7C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnFD,UAAM,eAAe;AAEf,UAAA,YAAY,IAAI,KAAK;AACrB,UAAA,cAAc,IAAI,EAAE;AAC1B,UAAM,EAAE,OAAO,YAAY,mBAAmB,oBAAoB;AAElE,UAAM,mBAAmB;AACzB,UAAM,cAAc;AACd,UAAA,0BAA0B,IAAI,IAAI;AAElC,UAAA,SAAS,wBAAC,aAAqB;AACnC,UAAI,iBAAiB,qBAAqB,SAAS,KAAA,MAAW,IAAI;AAC/C,yBAAA,kBAAkB,QAAQ,SAAS,KAAK;AACrD,YAAA,MAAM,eAAe,MAAM,IAAI;AAAA,MACrC;AACA,gBAAU,QAAQ;AAClB,uBAAiB,oBAAoB;AACzB,kBAAA,OAAQ,mBAAmB,wBAAwB;AAAA,IAAA,GAPlD;AAUf;AAAA,MACE,MAAM,iBAAiB;AAAA,MACvB,CAAC,WAAW;AACV,YAAI,WAAW,MAAM;AACnB;AAAA,QACF;AACA,oBAAY,QAAQ,OAAO;AAC3B,kBAAU,QAAQ;AAClB,cAAM,SAAS,YAAY;AAC3B,gCAAwB,QAAQ,OAAO;AACvC,eAAO,mBAAmB;AACpB,cAAA,QAAQ,OAAO,GAAG;AAExB,YAAI,kBAAkB,aAAa;AACjC,gBAAM,QAAQ;AACd;AAAA,YACE;AAAA,cACE,KAAK,MAAM;AAAA,cACX,MAAM,CAAC,MAAM,KAAK,CAAC,GAAG,MAAM,WAAW;AAAA,YACzC;AAAA,YACA,EAAE,UAAU,GAAG,MAAM,YAAY,KAAK,KAAK;AAAA,UAAA;AAAA,QAC7C,WACS,kBAAkB,YAAY;AACvC,gBAAM,OAAO;AACb,gBAAM,CAAC,GAAG,CAAC,IAAI,KAAK,YAAY;AAChC;AAAA,YACE;AAAA,cACE,KAAK,CAAC,GAAG,CAAC;AAAA,cACV,MAAM,CAAC,KAAK,OAAO,UAAU,iBAAiB;AAAA,YAChD;AAAA,YACA,EAAE,UAAU,GAAG,KAAK,KAAK,KAAK;AAAA,UAAA;AAAA,QAElC;AAAA,MACF;AAAA,IAAA;AAGI,UAAA,qBAAqB,wBAAC,UAAgC;AACtD,UAAA,MAAM,OAAO,YAAY,sBAAsB;AACjD,YAAI,CAAC,aAAa,IAAI,oCAAoC,GAAG;AAC3D;AAAA,QACF;AAEM,cAAA,QAAqB,MAAM,OAAO;AACxC,cAAM,CAACE,IAAG,CAAC,IAAI,MAAM;AAEf,cAAA,IAAI,MAAM,OAAO;AACjB,cAAA,YAAY,EAAE,UAAU;AAE1B,YAAA,aAAa,MAAM,aAAa;AAClC,2BAAiB,oBAAoB;AAAA,QACvC;AAAA,MACS,WAAA,MAAM,OAAO,YAAY,qBAAqB;AACvD,YAAI,CAAC,aAAa,IAAI,mCAAmC,GAAG;AAC1D;AAAA,QACF;AAEM,cAAA,OAAmB,MAAM,OAAO;AACtC,cAAM,CAACA,IAAG,CAAC,IAAI,KAAK;AAEd,cAAA,IAAI,MAAM,OAAO;AACjB,cAAA,YAAY,EAAE,UAAU;AAE9B,YAAI,aAAa,GAAG;AAClB,2BAAiB,oBAAoB;AAAA,QACvC;AAAA,MACF;AAAA,IAAA,GA7ByB;AAgCV,qBAAA,UAAU,oBAAoB,kBAAkB;;;;;;;;;;;;;;;;;AC/GpD,MAAA,oBAAoB,YAAY,aAAa,MAAM;AACxD,QAAA,UAAU,IAAI,KAAK;AACzB,WAAS,gBAAgB;AACf,YAAA,QAAQ,CAAC,QAAQ;AAAA,EAC3B;AAFS;AAIF,SAAA;AAAA,IACL;AAAA,IACA;AAAA,EAAA;AAEJ,CAAC;ACDM,MAAM,mBAA6C;AAAA,SAAA;AAAA;AAAA;AAAA,EACxD,YACS,MACA,MACA,OACA,QACA,KACA,gBACP;AANO,SAAA,OAAA;AACA,SAAA,OAAA;AACA,SAAA,QAAA;AACA,SAAA,SAAA;AACA,SAAA,MAAA;AACA,SAAA,iBAAA;AAAA,EACN;AAAA,EAEH,OAAO,sBAAsB,KAAqB;AAChD,WAAO,IAAI;AAAA,MACT,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,IAAA;AAAA,EAER;AAAA,EAEA,IAAI,OAAyB;AACrB,UAAA,SAAS,KAAK,QAAQ,KAAK,MAAM,OAAO,KAAK,QAAQ,QAAQ;AAC5D,WAAA,WAAW,KAAK,OAAO;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,kBAAsC;AACjC,WAAA,KAAK,SAAS,UAAU;AAAA,EACjC;AAAA,EAEA,UAAU,SAAqB;AAC7B,UAAM,eACJ,KAAK,oBAAoB,WAAW,QAAQ,UAAU,QAAQ;AAChE,QAAI,CAAC,aAAc;AAEnB,UAAM,cAAc,aAAa;AAAA,MAC/B,CAAC,SACC,KAAK,QAAQ,UAAU,kBAAkB,KAAK,MAAM,KAAK,IAAI;AAAA,IAAA;AAGjE,QAAI,gBAAgB,IAAI;AACd,cAAA;AAAA,QACN,iCAAiC,KAAK,IAAI,YAAY,QAAQ,KAAK;AAAA,MAAA;AAErE;AAAA,IACF;AAEI,QAAA,KAAK,oBAAoB,SAAS;AACpC,WAAK,KAAK,QAAQ,KAAK,MAAM,SAAS,aAAa,KAAK,cAAc;AAAA,IAAA,OACjE;AACL,cAAQ,QAAQ,aAAa,KAAK,MAAM,KAAK,MAAM,KAAK,cAAc;AAAA,IACxE;AAAA,EACF;AACF;ACrEY,IAAA,2CAAAC,4BAAL;AACLA,0BAAA,QAAS,IAAA;AACTA,0BAAA,YAAa,IAAA;AACbA,0BAAA,gBAAiB,IAAA;AAHPA,SAAAA;AAAA,GAAA,0BAAA,CAAA,CAAA;AAMA,IAAA,6CAAAC,8BAAL;AACLA,4BAAA,cAAe,IAAA;AACfA,4BAAA,YAAa,IAAA;AACbA,4BAAA,WAAY,IAAA;AAHFA,SAAAA;AAAA,GAAA,4BAAA,CAAA,CAAA;ACFZ,MAAKC,cAAU;AAAA,EACb,MAAM;AAAA,EACN,SAASC;AAAAA,EACT,OAAO,CAAC,wBAAwB;AAAA,EAChC,OAAO;AACL,WAAO;AAAA;AAAA,MAEL,aAAa;AAAA,IACf;AAAA,EACD;AAAA,EACD,UAAU;AACR,QAAI,OAAOA,SAAa,YAAY,YAAY;AAC9CA,eAAa,QAAQ,KAAK,IAAI;AAAA,IAChC;AAGA,UAAM,UAAU,KAAK,IAAI,cAAc,OAAO;AAC9C,QAAI,SAAS;AACX,cAAQ,iBAAiB,oBAAoB,MAAM;AACjD,aAAK,cAAc;AAAA,OACpB;AACD,cAAQ,iBAAiB,kBAAkB,MAAM;AAC/C,aAAK,cAAc;AAAA,OACpB;AAAA,IACH;AAEA,SAAK;AAAA,MACH,MAAM,KAAK;AAAA,MACX,CAAC,QAAQ,WAAW;AAElB,aAAK,MAAM,0BAA0B,MAAM;AAAA,MAC7C;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,UAAU,OAAO;AACf,UAAI,MAAM,QAAQ,WAAW,KAAK,aAAa;AAC7C,cAAM,eAAe;AACrB,cAAM,gBAAgB;AACtB;AAAA,MACF;AAEAA,eAAa,QAAQ,UAAU,KAAK,MAAM,KAAK;AAAA,IACjD;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;ACYA,UAAM,eAAe;AACrB,UAAM,eAAe;AAAA,MAAS,MAC5B,aAAa,IAAI,sCAAsC;AAAA,IAAA;AAEzD,UAAM,aAAa;AAAA,MAAS,MAC1B,aAAa,IAAI,oCAAoC;AAAA,IAAA;AAEvD,UAAM,oBAAoB;AAAA,MAAS,MACjC,aAAa,IAAI,2CAA2C;AAAA,IAAA;AAE9D,UAAM,qBAAqB;AAC3B,UAAM,gBAAgB;AAAA,MAAS,MAC7B,mBAAmB,iBAAiB,MAAM,OAAO;AAAA,IAAA;AAGnD,UAAM,oBAAoB;AAC1B,UAAM,eAAe;AAAA,MAAS,MAC5B,kBAAkB,aAAa,MAAM,OAAO;AAAA,IAAA;AAG9C,UAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACYd,UAAM,eAAe;AACf,UAAA,EAAE,GAAAN,OAAM;AAEd,UAAM,oBAAoB;AAAA,MAAS,MACjC,aAAa,IAAI,qCAAqC;AAAA,IAAA;AAQlD,UAAA,0BAA0B,IAAI,KAAK;AACzC,UAAM,UAAU,mCAAmC,KAAK,OAAA,CAAQ;AAC1D,UAAA,cAAc,IAAwB,CAAA,CAAE;AACxC,UAAA,oBAAoB,IAA6B,IAAI;AACrD,UAAA,eAAe,IAAI,EAAE;AACrB,UAAA,cAAc,SAAS,MAAM;AACjC,aAAO,QAAO,QAAC,WAAW,IAAIA,GAAE,eAAe,IAAI,QAAQ;AAAA,IAAA,CAC5D;AAED,UAAM,eAAe;AACrB,UAAM,qBAAqB;AACrB,UAAA,SAAS,wBAAC,UAAkB;AAChC,YAAM,eAAe,UAAU,MAAM,gBAAQ,WAAW;AACxD,mBAAa,QAAQ;AACT,kBAAA,QAAQ,eAChB,mBAAmB,cACnB;AAAA,QACE,GAAG,aAAa,kBAAkB,WAAW,OAAO,QAAA,SAAS;AAAA,UAC3D,OAAO,QAAA;AAAA,QAAA,CACR;AAAA,MAAA;AAAA,IACH,GATS;AAYf,UAAM,OAAO;AAEb,QAAI,eAAwC;AAC5C,UAAM,eAAe,6BAAM;AACR,uBAAA,SAAS,eAAe,OAAO;AAChD,UAAI,cAAc;AAChB,qBAAa,KAAK;AACT,iBAAA,MAAM,cAAc,MAAA,CAAO;AAAA,MACtC;AAAA,IAAA,GALmB;AAQrB,cAAU,YAAY;AAChB,UAAA,cAAc,wBAAC,mBAAmC;AACtD,8BAAwB,QAAQ;AAChC,WAAK,aAAa,cAAc;AAAA,IAAA,GAFd;AAId,UAAA,iBAAiB,wBAAC,OAAc,mBAAmC;AACvE,YAAM,gBAAgB;AACtB,YAAM,eAAe;AACrB,WAAK,gBAAgB,cAAc;AACtB;IAAA,GAJQ;AAMjB,UAAA,qBAAqB,wBAAC,UAAkB;AAC5C,UAAI,UAAU,IAAI;AAChB,0BAAkB,QAAQ;AAC1B;AAAA,MACF;AACM,YAAA,QAAQ,YAAY,MAAM,KAAK;AACrC,wBAAkB,QAAQ;AAAA,IAAA,GAND;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7F3B,UAAM,eAAe;AACrB,UAAM,mBAAmB;AAEzB,UAAM,EAAE,QAAY,IAAA,YAAY,kBAAmB,CAAA;AAC7C,UAAA,cAAc,IAAI,IAAI;AACtB,UAAA,eAAe,IAAiC,IAAI;AAC1D,UAAM,qBAAqB,6BAAe;AACpC,UAAA,CAAC,aAAa,OAAO;AACvB,eAAO,iBAAiB;MAC1B;AAEM,YAAA,gBAAiB,aAAa,MAAM,OACvC;AACH,aAAO,CAAC,cAAc,SAAS,cAAc,OAAO;AAAA,IAAA,GAP3B;AASrB,UAAA,cAAc,IAAsB,CAAA,CAAE;AACtC,UAAA,YAAY,wBAAC,WAA2B;AAChC,kBAAA,MAAM,KAAK,MAAM;AAAA,IAAA,GADb;AAGZ,UAAA,eAAe,wBAAC,WAA2B;AACnC,kBAAA,QAAQ,YAAY,MAAM;AAAA,QACpC,CAAC,MAAM,MAAM,CAAC,MAAM,MAAM,MAAM;AAAA,MAAA;AAAA,IAClC,GAHmB;AAKrB,UAAM,eAAe,6BAAM;AACzB,kBAAY,QAAQ;IAAC,GADF;AAGrB,UAAM,cAAc,6BAAM;AACxB,cAAQ,QAAQ;AAAA,IAAA,GADE;AAId,UAAA,UAAU,wBAAC,YAA8B;AACvC,YAAA,OAAO,iBAAiB,eAAe,SAAS;AAAA,QACpD,KAAK,mBAAmB;AAAA,MAAA,CACzB;AAEK,YAAA,cAAc,aAAa,OAAO;AACpC,UAAA,eAAe,YAAY,YAAY,iBAAiB;AAE1D,oBAAY,mBAAmB,MAAM,QAAQ,CAAC,SAAyB;AACrE,6BAAmB,sBAAsB,IAAI,EAAE,UAAU,IAAI;AAAA,QAAA,CAC9D;AAAA,MACH;AAKA,aAAO,WAAW,MAAM;AACV;SACX,GAAG;AAAA,IAAA,GAlBQ;AAqBhB,UAAM,sBAAsB;AAAA,MAC1B,MAAM,aAAa,IAAI,yBAAyB,MAAM;AAAA,IAAA;AAElD,UAAA,gBAAgB,wBAAC,MAA4B;AACjD,YAAM,SAAS,EAAE;AACjB,UAAI,oBAAoB,OAAO;AACzB,YAAA,OAAO,eAAe,gBAAgB,SAAS;AACjD,qBAAW,MAAM;AACf,6BAAiB,CAAC;AAAA,aACjB,GAAG;AAAA,QAAA,OACD;AACL,2BAAiB,CAAC;AAAA,QACpB;AAAA,MAAA,OACK;AAEO,oBAAA,OAAO,cAAc,OAAO,aAAa;AAAA,MACvD;AAAA,IAAA,GAboB;AAgBtB,UAAM,eAAe;AACf,UAAA,mBAAmB,wBAAC,MAA4B;AAChD,UAAA,EAAE,OAAO,YAAY,iBAAiB;AAClC,cAAA,QAAQ,EAAE,OAAO,mBAAmB;AACtC,YAAA,MAAM,WAAW,GAAG;AACtB,kBAAQ,KAAK,uDAAuD;AACpE;AAAA,QACF;AACA,cAAM,YAAY,mBAAmB,sBAAsB,MAAM,CAAC,CAAC;AAC7D,cAAA,SAAS,aAAa,kBAAkB;AAAA,UAC5C,UAAU;AAAA,QAAA;AAGN,cAAA,WAAW,UAAU,KAAK,SAAS;AAE/B,kBAAA,CAAC,QAAQ,QAAQ,CAAC;AAAA,MAC9B;AAEA,cAAQ,QAAQ;AAChB,mBAAa,QAAQ;AAGrB,kBAAY,QAAQ;AACpB,iBAAW,MAAM;AACf,oBAAY,QAAQ;AAAA,SACnB,GAAG;AAAA,IAAA,GAxBiB;AA2BnB,UAAA,kBAAkB,wBAAC,MAA4B;AAC/C,UAAA,EAAE,OAAO,YAAY,iBAAiB;AACxC;AAAA,MACF;AAEM,YAAA,QAAQ,EAAE,OAAO,mBAAmB;AACtC,UAAA,MAAM,WAAW,GAAG;AACtB,gBAAQ,KAAK,uDAAuD;AACpE;AAAA,MACF;AAEA,YAAM,YAAY,mBAAmB,sBAAsB,MAAM,CAAC,CAAC;AAC7D,YAAA,aAAa,EAAE,OAAO;AAC5B,YAAM,gBAAgB;AAAA,QACpB,GAAG;AAAA,QACH,iBAAiB;AAAA,QACjB,eAAe,6BAAM,cAAc,CAAC,GAArB;AAAA,MAAqB;AAEhC,YAAA,oBAAoB,UAAU,SAChC;AAAA,QACE,UAAU,UAAU;AAAA,QACpB,UAAU,UAAU;AAAA,QACpB,gBAAgB,UAAU;AAAA,MAAA,IAE5B;AAAA,QACE,QAAQ,UAAU;AAAA,QAClB,QAAQ,UAAU;AAAA,QAClB,gBAAgB,UAAU;AAAA,MAAA;AAGhC,kBAAY,OAAO,mBAAmB;AAAA,QACpC,GAAG;AAAA,QACH,GAAG;AAAA,MAAA,CACJ;AAAA,IAAA,GAjCqB;AAqCxB,UAAM,cAAc;AACpB,gBAAY,MAAM;AAChB,UAAI,YAAY,QAAQ;AACtB,kBAAU,mCAAmC;AAC7C,oBAAY,OAAO,kBAAkB;AAAA,MACvC;AAAA,IAAA,CACD;AAEK,UAAA,qBAAqB,wBAAC,MAA4B;AAClD,UAAA,EAAE,OAAO,YAAY,sBAAsB;AAC7C,sBAAc,CAAC;AAAA,MACN,WAAA,EAAE,OAAO,YAAY,iBAAiB;AAC/C,iCAAyB,CAAC;AAAA,MACjB,WAAA,EAAE,OAAO,YAAY,sBAAsB;AAC9C,cAAA,QAAQ,EAAE,OAAO;AACvB,cAAM,CAACE,IAAG,CAAC,IAAI,MAAM;AACrB,cAAM,YAAY,EAAE,OAAO,cAAc,UAAU;AAE/C,YAAA,YAAY,MAAM,aAAa;AACjC,wBAAc,CAAC;AAAA,QACjB;AAAA,MACF;AAAA,IAAA,GAbyB;AAgBrB,UAAA,oBAAoB,SAAS,MAAM;AAChC,aAAA,aAAa,IAAI,0BAA0B;AAAA,IAAA,CACnD;AAEK,UAAA,yBAAyB,SAAS,MAAM;AACrC,aAAA,aAAa,IAAI,+BAA+B;AAAA,IAAA,CACxD;AAEK,UAAA,2BAA2B,wBAAC,MAA4B;AAC5D,YAAM,SAAS,EAAE;AACX,YAAA,eAAe,OAAO,cAAc;AAE1C,YAAM,SAAS,eACX,uBAAuB,QACvB,kBAAkB;AACtB,cAAQ,QAAQ;AAAA,QACd,KAAK,yBAAyB;AAC5B,wBAAc,CAAC;AACf;AAAA,QACF,KAAK,yBAAyB;AAC5B,0BAAgB,CAAC;AACjB;AAAA,QACF,KAAK,yBAAyB;AAAA,QAC9B;AACE;AAAA,MACJ;AAAA,IAAA,GAjB+B;AAoBhB,qBAAA,UAAU,oBAAoB,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrNjE,UAAM,QAAQ;AAiBd,UAAM,OAAO;AACb,UAAM,eAAe;AAAA,MAAS,MAC5B,OAAO,MAAM,cAAc,aACvB,MAAM,UAAe,KAAA,KACrB,MAAM;AAAA,IAAA;AAEZ,UAAM,kBAAkB,SAAS,MAAM,CAAC,CAAC,aAAa,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5CrD,UAAA,EAAE,GAAAF,OAAM;AACd,UAAM,YAAY;AAElB,UAAM,UAAU;AAAA,MACd,MAAM,GAAGA,GAAE,oBAAoB,CAAC,KAAK,UAAU,aAAa,QAAQ;AAAA,IAAA;AAEtE,UAAM,SAAS,6BAAM;AACnB,gBAAU,OAAO;AACjB,aAAO,SAAS;IAAO,GAFV;;;;;;;;;;;;;ACFf,UAAM,cAAc;AACpB,UAAM,cAAc,6BAAM;AACxB,kBAAY,WAAW;AAAA,QACrB,KAAK;AAAA,QACL,iBAAiB;AAAA,QACjB,WAAW;AAAA,MAAA,CACZ;AAAA,IAAA,GALiB;;;;;;;;;;;;;;ACApB,UAAM,oBAAoB;AAC1B,UAAM,OAAO;AAAA,MAAS,MACpB,kBAAkB,uBAAuB,cACrC,cACA;AAAA,IAAA;AAGN,UAAM,eAAe;AACrB,UAAM,cAAc,6BAAM;AACxB,mBAAa,QAAQ,mBAAmB;AAAA,IAAA,GADtB;;;;;;;;;;;;;;;;;;;ACkBpB,UAAM,iBAAiB;AACvB,UAAM,eAAe;AACrB,UAAM,YAAY;AAElB,UAAM,iBAAiB;AAAA,MAAS,MAC9B,aAAa,IAAI,wBAAwB,MAAM,SAC3C,uBACA;AAAA,IAAA;AAGN,UAAM,UAAU;AAAA,MACd,MAAM,aAAa,IAAI,oBAAoB,MAAM;AAAA,IAAA;AAGnD,UAAM,OAAO,SAAS,MAAM,eAAe,eAAgB,CAAA;AAC3D,UAAM,cAAc,SAAS,MAAM,eAAe,WAAW,gBAAgB;AACvE,UAAA,aAAa,wBAAC,SAA8B;AACjC,qBAAA,WAAW,iBAAiB,KAAK,EAAE;AAAA,IAAA,GADjC;AAGnB,UAAM,kBAAkB;AAClB,UAAA,sBAAsB,wBAAC,QAA6B;AACxD,YAAM,aAAa,gBAAgB;AAAA,QACjC,8BAA8B,IAAI,EAAE;AAAA,MAAA;AAEtC,aAAO,aAAa,KAAK,WAAW,MAAM,SAAU,CAAA,MAAM;AAAA,IAAA,GAJhC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACd5B,UAAM,QAAQ;AAKR,UAAA,EAAE,GAAAA,OAAM;AAEd,UAAM,iBAAiB;AACvB,UAAM,gBAAgB;AAChB,UAAA,iBAAiB,IAAwB,IAAI;AAE7C,UAAA,iBAAiB,8BAAO,YAA8B;AAC1D,iBAAW,OAAO,SAAS;AACzB,YACE,CAAE,MAAM,mBAAA,EAAqB,cAAc,IAAI,UAAU;AAAA,UACvD,eAAe,CAAC,eAAe;AAAA,UAC/B,MAAMA,GAAE,wCAAwC;AAAA,QAAA,CACjD,GACD;AAEA;AAAA,QACF;AAAA,MACF;AAAA,IAAA,GAXqB;AAcjB,UAAA,kBAAkB,wBAAC,WAA2B;AACnC,qBAAA,CAAC,MAAM,CAAC;AAAA,IAAA,GADD;AAGlB,UAAA,YAAY,6BAAM,eAAe,OAArB;AAElB,0BAAsB,WAAW;AAAA,MAC/B,gBAAgB,6BAAM;AACb,eAAA;AAAA,UACL,aAAa,MAAM,eAAe,SAAS;AAAA,QAAA;AAAA,MAE/C,GAJgB;AAAA,IAIhB,CACD;AAED,0BAAsB,WAAW;AAAA,MAC/B,SAAS,6BAAM;AACN,eAAA;AAAA,UACL,aAAa,MAAM,eAAe,SAAS;AAAA,QAAA;AAAA,MAE/C,GAJS;AAAA,MAKT,QAAQ,wBAAC,MAAM;AACP,cAAA,YAAY,cAAc,cAAc;AAAA,UAC5C,CAAC,OAAO,GAAG,QAAQ,EAAE,OAAO,KAAK;AAAA,QAAA;AAE7B,cAAA,UAAU,cAAc,cAAc;AAAA,UAC1C,CAAC,OAAO,GAAG,QAAQ,EAAE,SAAS,QAAQ,YAAY,CAAC,GAAG,KAAK;AAAA,QAAA;AAE7D,YAAI,cAAc,SAAS;AACX,wBAAA,iBAAiB,WAAW,OAAO;AAAA,QACnD;AAAA,MACF,GAVQ;AAAA,IAUR,CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3CD,UAAM,QAAQ;AAIR,UAAA,EAAE,GAAAA,OAAM;AACd,UAAM,iBAAiB;AACvB,UAAM,gBAAgB;AACtB,UAAM,kBAAkB;AACxB,UAAM,wBAAwB;AAC9B,UAAM,kBAAkB;AACxB,UAAM,OAAO;AAEP,UAAA,mBAAmB,wBAAC,cAA6C;AAAA,MACrE,OAAO,SAAS;AAAA,MAChB;AAAA,IAAA,IAFuB;AAKzB,UAAM,UAAU;AAAA,MAA2B,MACzC,cAAc,cAAc,IAAI,gBAAgB;AAAA,IAAA;AAElD,UAAM,mBAAmB;AAAA,MAAgC,MACvD,cAAc,iBACV,iBAAiB,cAAc,cAA+B,IAC9D;AAAA,IAAA;AAEA,UAAA,mBAAmB,wBAAC,WAA2B;AAEnD,UAAI,CAAC,QAAQ;AACX;AAAA,MACF;AAEA,UAAI,iBAAiB,OAAO,UAAU,OAAO,OAAO;AAClD;AAAA,MACF;AAEgB,sBAAA,aAAa,OAAO,QAAQ;AAAA,IAAA,GAVrB;AAanB,UAAA,iBAAiB,8BAAOO,aAA8B;AAC1D,iBAAW,OAAOA,UAAS;AACzB,YACE,CAAE,MAAM,gBAAgB,cAAc,IAAI,UAAU;AAAA,UAClD,eAAe,CAAC,eAAe;AAAA,QAAA,CAChC,GACD;AAEA;AAAA,QACF;AAAA,MACF;AAAA,IAAA,GAVqB;AAajB,UAAA,kBAAkB,wBAAC,WAA2B;AACnC,qBAAA,CAAC,MAAM,CAAC;AAAA,IAAA,GADD;AAIlB,UAAA,kBAAkB,wBAAC,OAAmB,WAA2B;AACrE,sBAAgB,QAAQ;AACnB,WAAA,MAAM,KAAK,KAAK;AAAA,IAAA,GAFC;AAIlB,UAAA,mBAAmB,SAAS,MAAM;AACtC,YAAM,MAAM,gBAAgB;AACxB,UAAA,CAAC,IAAK,QAAO;AACX,YAAA,QAAQ,QAAQ,MAAM,UAAU,CAAC,MAAM,EAAE,aAAa,IAAI,QAAQ;AAEjE,aAAA;AAAA,QACL;AAAA,UACE,OAAOP,GAAE,sBAAsB;AAAA,UAC/B,SAAS,6BAAM;AACG,4BAAA,kBAAkB,IAAI,QAAQ;AAAA,UAChD,GAFS;AAAA,QAGX;AAAA,QACA;AAAA,UACE,WAAW;AAAA,QACb;AAAA,QACA;AAAA,UACE,OAAOA,GAAE,kBAAkB;AAAA,UAC3B,SAAS,6BAAM,gBAAgB,GAAG,GAAzB;AAAA,QACX;AAAA,QACA;AAAA,UACE,OAAOA,GAAE,yBAAyB;AAAA,UAClC,SAAS,6BAAM,eAAe,QAAQ,MAAM,MAAM,GAAG,KAAK,CAAC,GAAlD;AAAA,UACT,UAAU,SAAS;AAAA,QACrB;AAAA,QACA;AAAA,UACE,OAAOA,GAAE,0BAA0B;AAAA,UACnC,SAAS,6BAAM,eAAe,QAAQ,MAAM,MAAM,QAAQ,CAAC,CAAC,GAAnD;AAAA,UACT,UAAU,UAAU,QAAQ,MAAM,SAAS;AAAA,QAC7C;AAAA,QACA;AAAA,UACE,OAAOA,GAAE,wBAAwB;AAAA,UACjC,SAAS,6BACP,eAAe;AAAA,YACb,GAAG,QAAQ,MAAM,MAAM,QAAQ,CAAC;AAAA,YAChC,GAAG,QAAQ,MAAM,MAAM,GAAG,KAAK;AAAA,UAAA,CAChC,GAJM;AAAA,UAKT,UAAU,QAAQ,MAAM,UAAU;AAAA,QACpC;AAAA,QACA;AAAA,UACE,OAAO,sBAAsB,aAAa,IAAI,SAAS,IAAI,IACvDA,GAAE,6BAA6B,IAC/BA,GAAE,wBAAwB;AAAA,UAC9B,SAAS,6BAAM,sBAAsB,iBAAiB,IAAI,SAAS,IAAI,GAA9D;AAAA,UACT,UAAU,IAAI,SAAS;AAAA,QACzB;AAAA,MAAA;AAAA,IACF,CACD;AACD,UAAM,eAAe;AAGf,UAAA,cAAc,wBAAC,UAAsB;AACzC,YAAM,gBAAgB,MAAM;AACtB,YAAA,eAAe,MAAM,UAAU,MAAM;AAC3C,oBAAc,OAAO;AAAA,QACnB,MAAM,cAAc,aAAa;AAAA,MAAA,CAClC;AAAA,IAAA,GALiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3JP,MAAA,gBAAgB,wBAAC,cAAsC;AAClE,QAAM,mBAAmB;AACzB,QAAM,mBAAmB;AACzB,QAAM,kBAAkB;AAEF,wBAAA,MAAM,UAAU,OAAO;AAAA,IAC3C,eAAe,wBAAC,SACd,KAAK,OAAO,KAAK,SAAS,uBAAuB,SAAS,QAD7C;AAAA,IAEf,QAAQ,wBAAC,UAAU;AACX,YAAA,MAAM,MAAM,SAAS,QAAQ;AAC7B,YAAA,UAAU,MAAM,OAAO;AAEzB,UAAA,QAAQ,SAAS,sBAAsB;AACzC,cAAM,OAAO,QAAQ;AACjB,YAAA,KAAK,gBAAgB,kBAAkB;AACzC,gBAAM,UAAU,KAAK;AAGf,gBAAA,MAAMC,IAAS,qBAAqB;AAAA,YACxC,IAAI;AAAA,YACJ,IAAI,UAAU,UAAU;AAAA,UAAA,CACzB;AACD,2BAAiB,eAAe,SAAS,EAAE,IAAK,CAAA;AAAA,QAAA,WACvC,KAAK,gBAAgB,eAAe;AAC7C,gBAAM,QAAQ,KAAK;AACb,gBAAA,MAAMA,IAAS,qBAAqB,CAAC,IAAI,SAAS,IAAI,OAAO,CAAC;AAC9D,gBAAA,YAAYA,IAAS,MAAM,aAAa,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AAC5D,cAAI,iBAA2C;AAC/C,cAAI,kBAAqC;AACzC,cAAI,WAAW;AACb,kBAAM,YAAY,iBAAiB;AAAA,cACjC,MAAM;AAAA,YAAA;AAER,uBAAW,YAAY,WAAW;AAChC,kBAAI,SAAS,QAAQ,SAAS,UAAU,YAAY;AAChC,kCAAA;AACD,iCAAA;AAAA,cACnB;AAAA,YACF;AAAA,UACF;AACA,cAAI,CAAC,iBAAiB;AACpB,kBAAM,WAAW,iBAAiB,gBAAgB,MAAM,SAAS;AACjE,gBAAI,UAAU;AACZ,gCAAkB,iBAAiB;AAAA,gBACjC,SAAS;AAAA,gBACT;AAAA,kBACE;AAAA,gBACF;AAAA,cAAA;AAEe,+BAAA;AAAA,YACnB;AAAA,UACF;AACA,cAAI,iBAAiB;AACb,kBAAA,SAAS,gBAAgB,SAAS;AAAA,cACtC,CAACO,YAAWA,QAAO,SAAS,gBAAgB;AAAA,YAAA;AAE9C,gBAAI,QAAQ;AACV,qBAAO,QAAQ,MAAM;AAAA,YACvB;AAAA,UACF;AAAA,QAAA,WACS,KAAK,gBAAgB,eAAe;AAC7C,gBAAM,WAAW,KAAK;AAChB,gBAAA,WAAWP,IAAS,qBAAqB;AAAA,YAC7C,IAAI;AAAA,YACJ,IAAI;AAAA,UAAA,CACL;AACD,0BAAgB,eAAe,UAAU,EAAE,SAAU,CAAA;AAAA,QACvD;AAAA,MACF;AAAA,IACF,GA7DQ;AAAA,EA6DR,CACD;AACH,GAvE6B;ACDtB,MAAM,4BAA4B,6BAAM;AACvC,QAAA,IAAI,aAAa,UAAU;AAC3B,QAAA,6BAA6B,mCAE9B,MACH;AACA,UAAM,MAAM,EAAE,MAAM,MAAM,IAAI;AAC9B,eAAW,QAAQ,KAAK;AACtB,UAAI,MAAM,SAAS;AACjB,aAAK,UAAU,GAAG,eAAe,KAAK,OAAO,IAAI,KAAK,OAAO;AAAA,MAC/D;AAAA,IACF;AACO,WAAA;AAAA,EAAA,GAV0B;AAanC,eAAa,UAAU,uBAAuB;AAErC,WAAA,eACP,QACA,SACA;AACA,QAAI,CAAC,OAAQ;AACb,UAAM,UAAU;AAChB,UAAM,WAAW;AACX,UAAA,MAAM,GAAG,wBAAwB,UAAU;AAC3C,UAAA,OAAO,GAAG,yBAAyB,WAAW;AAC9C,UAAA,OAAO,GAAG,0BAA0B,YAAY;AACtD,eAAW,SAAS,QAAQ;AACtB,UAAA,OAAO,UAAU,SAAU;AAEhB,qBAAA,OAAO,SAAS,SAAS,OAAO;AAC3C,UAAA,CAAC,OAAO,SAAS;AACnB;AAAA,MACF;AACA,UAAI,GAAG,eAAe,MAAM,OAAO,EAAE,GAAG;AACtC,cAAM,UAAU,GAAG,eAAe,MAAM,OAAO,IAAI,MAAM,OAAO;AAAA,MAClE;AAGA,YAAM,YAAiB,QAAQ,SAAS,QAAQ,YAAY,SAAS;AAErE,YAAM,aAAa,MAAM,SAAS,MAAM,OAAO;AAC/C,UAAI,YAAY;AACV,YAAA,QAAQ,WAAW,CAAC;AACb,mBAAA,QAAQ,KAAK,CAAC,MAAsB;AACzC,cAAA,EAAE,QAAQ,MAAc,QAAA;AAC5B,kBAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE;AAAA,QAAA,CAC/B;AACU,mBAAA,SAAS,KAAK,CAAC,MAAe;AACnC,cAAA,EAAE,QAAQ,MAAc,QAAA;AAC5B,kBAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE;AAAA,QAAA,CAC/B;AACK,cAAA,UAAU,MAAM,QAAQ;AAC9B;AAAA,MACF;AACA,YAAM,cAAc,MAAM,SAAS,MAAM,QAAQ;AACjD,UAAI,aAAa;AACX,YAAA,QAAQ,YAAY,CAAC;AACd,mBAAA,QAAQ,KAAK,CAAC,MAAsB;AACzC,cAAA,EAAE,QAAQ,MAAc,QAAA;AAC5B,kBAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE;AAAA,QAAA,CAC/B;AACU,mBAAA,SAAS,KAAK,CAAC,MAAe;AACnC,cAAA,EAAE,QAAQ,MAAc,QAAA;AAC5B,kBAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE;AAAA,QAAA,CAC/B;AACK,cAAA,UAAU,MAAM,QAAQ;AAC9B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AArDS;AAuDT,QAAM,sBAAsB,UAAU;AAC7B,WAAAQ,aACP,QACA,SACA;AACA,QAAI,QAAQ,OAAO;AACjB,cAAQ,QAAQ;AAAA,QACd,YAAY,iBAAiB,QAAQ,KAAK,CAAC;AAAA,QAC3C,QAAQ;AAAA,MAAA;AAAA,IAEZ;AACA,mBAAe,QAAQ,OAAO;AAC9B,UAAM,MAAM,IAAI,oBAAoB,QAAQ,OAAO;AAC5C,WAAA;AAAA,EACT;AAbS,SAAAA,cAAA;AAeT,YAAU,cAAcA;AACd,YAAA,YAAY,YAAY,oBAAoB;AACxD,GA1FyC;ACPlC,MAAM,UAAU,6BAAM;AAC3B,QAAM,cAAc;AAEH,mBAAA,UAAU,QAAQ,CAAC,MAAM;AACpC,QAAA,EAAE,EAAE,kBAAkB,UAAU;AAClC;AAAA,IACF;AACA,QACG,EAAE,kBAAkB,uBACnB,EAAE,OAAO,SAAS,cACnB,EAAE,kBAAkB,oBAAoB,EAAE,OAAO,SAAS,QAC3D;AAEA;AAAA,IACF;AACA,UAAM,kBACJ,EAAE,OAAO,UAAU,SAAS,WAAW,KACvC,EAAE,OAAO,UAAU,SAAS,wBAAwB,KACpD,EAAE,OAAO,OAAO;AAGlB,UAAM,SAAS,YAAY;AACvB,QAAA,mBAAmB,QAAQ,eAAe;AAC5C,aAAO,gBAAgB;AAErB,QAAA,eAAe,QAAQ,QAAQ,GAAG;AACpC,QAAE,eAAe;AACjB,QAAE,yBAAyB;AACpB,aAAA;AAAA,IACT;AAAA,EAAA,CACD;AACH,GA/BuB;ACQhB,MAAM,qBAAqB,6BAAM;AAEtC,SAAO,WAAW,IAAI;AAEtB,SAAO,QAAQ,IAAI;AAEnB,SAAO,OAAO,IAAI;AAElB,SAAO,YAAY,IAAI;AAEvB,SAAO,aAAa,IAAI;AAExB,SAAO,cAAc,IAAI;AAEzB,SAAO,cAAc,IAAI;AAEzB,SAAO,aAAa,IAAI;AAExB,SAAO,aAAa,IAAI;AAC1B,GAnBkC;ACN3B,MAAM,uBAAuB,6BAAM;AACxC,QAAM,eAAe;AACrB,QAAM,cAAc;AAEpB,cAAY,MAAM;AACV,UAAA,oBAAoB,aAAa,IAAI,wBAAwB;AACnE,QAAI,YAAY,QAAQ;AACtB,kBAAY,OAAO,YAAY;AAAA,IACjC;AAAA,EAAA,CACD;AAED,cAAY,MAAM;AACV,UAAA,YAAY,aAAa,IAAI,uBAAuB;AAC1D,QAAI,YAAY,QAAQ;AACtB,kBAAY,OAAO,aAAa;AAAA,IAClC;AAAA,EAAA,CACD;AAED,cAAY,MAAM;AAChB,cAAU,kBAAkB,aAAa;AAAA,MACvC;AAAA,IAAA;AAAA,EACF,CACD;AAED,cAAY,MAAM;AAChB,cAAU,uBAAuB,aAAa;AAAA,MAC5C;AAAA,IAAA;AAAA,EACF,CACD;AAED,cAAY,MAAM;AAChB,eAAW,uBAAuB,aAAa;AAAA,MAC7C;AAAA,IAAA;AAAA,EACF,CACD;AAED,cAAY,MAAM;AAChB,cAAU,qCAAqC,aAAa;AAAA,MAC1D;AAAA,IAAA;AAAA,EACF,CACD;AAED,cAAY,MAAM;AACV,UAAA,iBAAiB,aAAa,IAAI,sBAAsB;AAC9D,QAAI,YAAY,QAAQ;AACtB,kBAAY,OAAO,oBAAoB;AACvC,kBAAY,OAAO;AAAA;AAAA,QAAkB;AAAA;AAAA,QAAgB;AAAA,MAAA;AAAA,IACvD;AAAA,EAAA,CACD;AAED,cAAY,MAAM;AAChB,UAAM,mCAAmC,aAAa;AAAA,MACpD;AAAA,IAAA;AAEF,QAAI,YAAY,QAAQ;AACtB,kBAAY,OAAO,6BACjB;AACF,kBAAY,OAAO;AAAA;AAAA,QAAkB;AAAA;AAAA,QAAe;AAAA,MAAA;AAAA,IACtD;AAAA,EAAA,CACD;AAED,cAAY,MAAM;AACV,UAAA,kBAAkB,aAAa,IAAI,yBAAyB;AAC5D,UAAA,EAAE,OAAW,IAAA;AACnB,QAAI,QAAQ;AACV,aAAO,kBAAkB;AAClB,aAAA,SAAS,OAAO,IAAI;AAAA,IAC7B;AAAA,EAAA,CACD;AAED,cAAY,MAAM;AACV,UAAA,aAAa,aAAa,IAAI,6BAA6B;AAC3D,UAAA,EAAE,OAAW,IAAA;AACf,QAAA,eAAe,aAAa;AAAA,EAAA,CACjC;AAED,cAAY,MAAM;AACV,UAAA,kBAAkB,aAAa,IAAI,2BAA2B;AAC9D,UAAA,EAAE,OAAW,IAAA;AACf,QAAA,eAAe,kBAAkB;AAAA,EAAA,CACtC;AAED,cAAY,MAAM;AAChB,kBAAc,kBAAkB,aAAa;AAAA,MAC3C;AAAA,IAAA;AAAA,EACF,CACD;AAED,cAAY,MAAM;AACF,kBAAA,aAAa,aAAa,IAAI,+BAA+B;AAAA,EAAA,CAC5E;AAED,cAAY,MAAM;AACF,kBAAA,gBAAgB,aAAa,IAAI,0BAA0B;AAAA,EAAA,CAC1E;AAED,cAAY,MAAM;AACN,cAAA,mBAAmB,aAAa,IAAI,2BAA2B;AAAA,EAAA,CAC1E;AAED,cAAY,MAAM;AACN,cAAA,mBAAmB,aAAa,IAAI,oBAAoB;AAAA,EAAA,CACnE;AAED,cAAY,MAAM;AAChB,cAAU,uBAAuB,aAAa;AAAA,MAC5C;AAAA,IAAA;AAAA,EACF,CACD;AACH,GA7GoC;ACI7B,MAAM,WAAW,6BAAM;AAC5B,QAAM,iBAAiB;AACvB,QAAM,cAAc;AAEpB,QAAM,mBAAmB,wBACvB,OACA,MACA,gBACG;AACH,QAAI,CAAC,KAAM;AAEX,UAAM,gBAAgB,MAAM,KAAK,KAAK,EAAE;AAAA,MAAO,CAAC,SAC9C,KAAK,KAAK,WAAW,WAAW;AAAA,IAAA;AAGlC,UAAM,OAAO,cAAc,CAAC,GAAG,UAAU;AACzC,QAAI,CAAC,KAAM;AAEX,SAAK,YAAY,IAAI;AAChB,SAAA;AAAA,MACH,MAAM,KAAK,aAAa,EACrB,IAAI,CAAC,MAAM,EAAE,UAAA,CAAW,EACxB,OAAO,CAAC,MAAM,MAAM,IAAI;AAAA,IAAA;AAAA,EAC7B,GAnBuB;AAsBR,mBAAA,UAAU,SAAS,OAAO,MAAM;AAG/C,QAAI,eAAe,UAAW;AAExB,UAAA,EAAE,OAAW,IAAA;AACnB,QAAI,CAAC,OAAQ;AAEP,UAAA,EAAE,MAAU,IAAA;AAClB,QAAI,OAAqC,EAAE;AAC3C,QAAI,CAAC,KAAY,OAAA,IAAI,MAAM,sCAAsC;AAE3D,UAAA,EAAE,MAAU,IAAA;AAElB,UAAM,cAAc,OAAO;AAC3B,UAAM,iBAAiB,aAAa;AAE9B,UAAA,sBAAsB,kBAAkB,YAAY,WAAW;AAC/D,UAAA,sBAAsB,kBAAkB,YAAY,WAAW;AAC/D,UAAA,sBAAsB,kBAAkB,YAAY,WAAW;AAEjE,QAAA,YAA+B,sBAAsB,cAAc;AACnE,QAAA,YAA+B,sBAAsB,cAAc;AACjE,UAAA,YAA+B,sBACjC,cACA;AAGJ,eAAW,QAAQ,OAAO;AACxB,UAAI,KAAK,KAAK,WAAW,QAAQ,GAAG;AAClC,YAAI,CAAC,WAAW;AAER,gBAAA,UAAU,UAAU,WAAW,WAAW;AAChD,cAAI,SAAS;AACH,oBAAA,MAAM,CAAC,OAAO,YAAY,CAAC,GAAG,OAAO,YAAY,CAAC,CAAC;AAC/C,wBAAA,OAAO,IAAI,OAAO,KAAK;AAAA,UACrC;AACA,iBAAO,OAAO;AAAA,QAChB;AACiB,yBAAA,OAAO,WAAW,OAAO;AAC1C;AAAA,MACS,WAAA,KAAK,KAAK,WAAW,QAAQ,GAAG;AACzC,YAAI,CAAC,WAAW;AAAA,QAAA,OAGT;AACY,2BAAA,OAAO,WAAW,OAAO;AAC1C;AAAA,QACF;AAAA,MACS,WAAA,KAAK,KAAK,WAAW,QAAQ,GAAG;AACzC,YAAI,CAAC,WAAW;AAER,gBAAA,UAAU,UAAU,WAAW,WAAW;AAChD,cAAI,SAAS;AACH,oBAAA,MAAM,CAAC,OAAO,YAAY,CAAC,GAAG,OAAO,YAAY,CAAC,CAAC;AAC/C,wBAAA,OAAO,IAAI,OAAO,KAAK;AAAA,UACrC;AACA,iBAAO,OAAO;AAAA,QAChB;AACiB,yBAAA,OAAO,WAAW,OAAO;AAC1C;AAAA,MACF;AAAA,IACF;AAGO,WAAA,KAAK,QAAQ,YAAY;AAChC,QAAI,WAAqC;AACrC,QAAA;AACF,aAAO,KAAK,MAAM,KAAK,QAAQ,GAAG,CAAC;AACxB,iBAAA,KAAK,MAAM,IAAI;AAAA,aACnB,KAAK;AACR,UAAA;AACF,eAAO,KAAK,MAAM,KAAK,QAAQ,YAAY,CAAC;AAC5C,eAAO,KAAK,MAAM,KAAK,QAAQ,GAAG,CAAC;AACxB,mBAAA,KAAK,MAAM,IAAI;AAAA,eACnB,OAAO;AACH,mBAAA;AAAA,MACb;AAAA,IACF;AAEA,QAAI,YAAY,SAAS,WAAW,SAAS,SAAS,SAAS,OAAO;AAC9D,YAAA,IAAI,cAAc,QAAQ;AAAA,IAAA,OAC3B;AACL,UACG,EAAE,kBAAkB,uBACnB,EAAE,OAAO,SAAS,cACnB,EAAE,kBAAkB,oBAAoB,EAAE,OAAO,SAAS,QAC3D;AACA;AAAA,MACF;AAGA,aAAO,mBAAmB;AAAA,IAC5B;AAAA,EAAA,CACD;AACH,GAzHwB;ACHjB,SAAS,yBAAyB;AACvC,QAAM,gBAAgB;AACtB,QAAM,eAAe;AAErB,QAAM,yBAAyB,6BAAM;AACnC,UAAM,WAAW,KAAK,UAAUR,IAAS,eAAgB,CAAA;AAC5C,iBAAA,QAAQ,YAAY,QAAQ;AACzC,QAAI,IAAI,UAAU;AAChB,qBAAe,QAAQ,YAAY,IAAI,QAAQ,IAAI,QAAQ;AAAA,IAC7D;AAAA,EAAA,GAL6B;AAQzB,QAAA,0BAA0B,8BAC9B,MACA,iBACG;AACC,QAAA,CAAC,KAAa,QAAA;AACZ,UAAA,WAAW,KAAK,MAAM,IAAI;AAChC,UAAMA,IAAS,cAAc,UAAU,MAAM,MAAM,YAAY;AACxD,WAAA;AAAA,EAAA,GAPuB;AAUhC,QAAM,kCAAkC,mCAAY;AAC5C,UAAA,eAAe,gBAAgB,wBAAwB;AACvD,UAAA,WAAW,IAAI,mBAAmB,IAAI;AAG5C,QAAI,UAAU;AACZ,YAAM,kBAAkB,eAAe,QAAQ,YAAY,QAAQ,EAAE;AACrE,UAAI,MAAM,wBAAwB,iBAAiB,YAAY,GAAG;AACzD,eAAA;AAAA,MACT;AAAA,IACF;AAGM,UAAA,gBAAgB,aAAa,QAAQ,UAAU;AAC9C,WAAA,MAAM,wBAAwB,eAAe,YAAY;AAAA,EAAA,GAd1B;AAiBxC,QAAM,sBAAsB,mCAAY;AACtC,QAAI,CAAC,aAAa,IAAI,yBAAyB,GAAG;AAC1C,YAAA,aAAa,IAAI,2BAA2B,IAAI;AAChD,YAAA,mBAAA,EAAqB;AACrB,YAAA,gBAAkB,EAAA,QAAQ,uBAAuB;AAAA,IAAA,OAClD;AACL,YAAMA,IAAS;IACjB;AAAA,EAAA,GAP0B;AAU5B,QAAM,0BAA0B,mCAAY;AACtC,QAAA;AACI,YAAA,WAAW,MAAM;AACvB,UAAI,CAAC,UAAU;AACb,cAAM,oBAAoB;AAAA,MAC5B;AAAA,aACO,KAAK;AACJ,cAAA,MAAM,mCAAmC,GAAG;AACpD,YAAM,oBAAoB;AAAA,IAC5B;AAAA,EAAA,GAT8B;AAahC,cAAY,MAAM;AAChB,QAAI,cAAc,gBAAgB;AAChC,YAAM,WAAW,cAAc;AACf,sBAAA,0BAA0B,SAAS,GAAG;AAG/B;IACzB;AAAA,EAAA,CACD;AACG,MAAA,iBAAiB,gBAAgB,sBAAsB;AAG3D,QAAM,gBAAgB,SAAS,MAAM,cAAc,aAAa;AAChE,QAAM,iBAAiB,SAAS,MAAM,cAAc,cAAc;AAClE,QAAM,eAAe;AAAA,IACnB,MAAM;AACJ,UAAI,CAAC,cAAc,SAAS,CAAC,eAAe,OAAO;AACjD,eAAO,EAAE,OAAO,CAAA,GAAI,aAAa,GAAG;AAAA,MACtC;AAEA,YAAM,QAAQ,cAAc,MACzB,OAAO,CAAC,aAAa,UAAU,eAAe,CAAC,SAAS,UAAU,EAClE,IAAI,CAAC,aAAa,SAAS,IAAI;AAC5B,YAAA,cAAc,cAAc,MAAM;AAAA,QACtC,CAAC,aAAa,SAAS,SAAS,eAAe,OAAO;AAAA,MAAA;AAGjD,aAAA,EAAE,OAAO;IAClB;AAAA,EAAA;AAIF,QAAM,kBAAkB,KAAK;AAAA,IAC3B,gBAAgB,0BAA0B,KAAK;AAAA,EAAA;AAEjD,QAAM,oBAAoB,KAAK;AAAA,IAC7B,gBAAgB,2BAA2B,KAAK;AAAA,EAAA;AAElD,QAAM,cAAc,CAAC,EAAE,OAAO,kBAAkB;AAC9C,oBAAgB,4BAA4B,KAAK,UAAU,KAAK,CAAC;AACjE,oBAAgB,6BAA6B,KAAK,UAAU,WAAW,CAAC;AAAA,EAAA,CACzE;AAED,QAAM,2BAA2B,6BAAM;AACrC,UAAM,eAAe,iBAAiB,SAAS,KAAK,qBAAqB;AACzE,QAAI,cAAc;AAChB,oBAAc,0BAA0B;AAAA,QACtC,MAAM,gBAAgB,MAAM,GAAG,iBAAiB;AAAA,QAChD,OAAO,gBAAgB,MAAM,iBAAiB;AAAA,MAAA,CAC/C;AAAA,IACH;AAAA,EAAA,GAP+B;AAU1B,SAAA;AAAA,IACL;AAAA,IACA;AAAA,EAAA;AAEJ;AAvHgB;ACDT,MAAM,gBAAiC;AAAA,EAC5C;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,SAAS,mBAAmB,gBAAgB;AAAA,IACvD,cAAc;AAAA,IACd,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS,CAAC,WAAW,oBAAoB;AAAA,IACzC,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,eAAe,QAAQ;AAAA,IAC/C,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS,OAAO,OAAO,wBAAwB;AAAA,IAC/C,cAAc,yBAAyB;AAAA,EACzC;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,eAAe,aAAa;AAAA,IACpD,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS,OAAO,OAAO,wBAAwB;AAAA,IAC/C,cAAc,yBAAyB;AAAA,EACzC;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,SAAS,mBAAmB,aAAa;AAAA,IACpD,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,SAAS,mBAAmB,cAAc;AAAA,IACrD,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,SAAS,mBAAmB,YAAY;AAAA,IACnD,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,SAAS,mBAAmB,mBAAmB;AAAA,IAC1D,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,cAAc,WAAW,UAAU;AAAA,IAC9C,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS,CAAC,QAAQ,OAAO;AAAA,IACzB,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,cAAc,WAAW,MAAM;AAAA,IAC1C,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS,CAAC,UAAU,OAAO;AAAA;AAAA,IAE3B,cAAc,6BAAO,OAAO,aAAa,OAAO,UAAU,UAA5C;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,cAAc,eAAe,kBAAkB,UAAU;AAAA,IACpE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,OAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,SAAS,eAAe,kBAAkB,YAAY;AAAA,IACjE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,UAAU,YAAY;AAAA,IAC9C,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,SACE;AAAA,IACF,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,SACE;AAAA,IACF,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,cAAc,QAAQ,SAAS;AAAA,IAC1C,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,OAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,UAAU,WAAW;AAAA,IAC7C,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,OAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA,EAGA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc,CAAC;AAAA,IACf,YAAY;AAAA,EACd;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc,CAAC;AAAA,EACjB;AAAA;AAAA,EAEA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc,CAAC;AAAA,EACjB;AAAA;AAAA,EAEA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,SAAS,SAAS;AAAA,IAC1C,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,OAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,QAAQ,wBAAwB;AAAA,IACxD,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,SAAS,wBAAwB;AAAA,IACzD,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,iBAAiB;AAAA,EACnB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,cAAc,iBAAiB,aAAa;AAAA,IACvD,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,OAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,SACE;AAAA,IACF,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,SACE;AAAA,IACF,MAAM;AAAA,IACN,SAAS,CAAC,YAAY,OAAO;AAAA,IAC7B,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,MACP,EAAE,OAAO,MAAM,MAAM,UAAU;AAAA,MAC/B,EAAE,OAAO,MAAM,MAAM,KAAK;AAAA,MAC1B,EAAE,OAAO,MAAM,MAAM,UAAU;AAAA,MAC/B,EAAE,OAAO,MAAM,MAAM,MAAM;AAAA,MAC3B,EAAE,OAAO,MAAM,MAAM,MAAM;AAAA,MAC3B,EAAE,OAAO,MAAM,MAAM,WAAW;AAAA,IAClC;AAAA,IACA,cAAc,6BAAM,UAAU,SAAS,MAAM,GAAG,EAAE,CAAC,KAAK,MAA1C;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,QAAQ,qBAAqB;AAAA,IACrD,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS,OAAO,OAAO,aAAa;AAAA,IACpC,cAAc,cAAc;AAAA,EAC9B;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,QAAQ,iBAAiB;AAAA,IACjD,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS,CAAC,cAAc,MAAM,cAAc,OAAO;AAAA,IACnD,cAAc,cAAc;AAAA,EAC9B;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,QAAQ,wBAAwB;AAAA,IACxD,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS,CAAC,cAAc,MAAM,cAAc,OAAO;AAAA,IACnD,cAAc,cAAc;AAAA,EAC9B;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,SAAS,YAAY,cAAc;AAAA,IAC9C,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,SAAS,YAAY,gBAAgB;AAAA,IAChD,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,eAAe,eAAe;AAAA,IACtD,MAAM;AAAA,IACN,SACE;AAAA,IACF,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,eAAe,gBAAgB;AAAA,IACvD,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,eAAe,sBAAsB;AAAA,IAC7D,MAAM;AAAA,IACN,SACE;AAAA,IACF,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,eAAe,wBAAwB;AAAA,IAC/D,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,OAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAAA,IACA,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAAA,IACA,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,QAAQ,gBAAgB;AAAA,IAChD,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,UAAU,wBAAC,UAAU;AACb,YAAA,UAAU,SAAS,eAAe,2BAA2B;AACnE,UAAI,SAAS;AACH,gBAAA,MAAM,UAAU,QAAQ,SAAS;AAAA,MAC3C;AAAA,IACF,GALU;AAAA,EAMZ;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,SAAS,QAAQ,YAAY;AAAA,IACxC,cAAc;AAAA,IACd,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS,CAAC,YAAY,OAAO,QAAQ;AAAA,IACrC,wBAAwB,wBAAC,UAAkB;AAEzC,UAAI,UAAU,YAAY;AACjB,eAAA;AAAA,MACT;AACO,aAAA;AAAA,IACT,GANwB;AAAA,EAO1B;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS,CAAC,WAAW,UAAU,kBAAkB;AAAA;AAAA,IAEjD,cAAc,6BACZ,OAAO,aAAa,OAAO,qBAAqB,UADpC;AAAA,EAEhB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,UAAU,YAAY;AAAA,IAC9C,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,SACE;AAAA,IACF,MAAM;AAAA,IACN,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc,CAAC;AAAA,IACf,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,wBAAwB,wBAAC,UAAiB;AACjC,aAAA,MAAM,IAAI,CAAC,eAAe;AAC3B,YAAA,WAAW,gBAAgB,MAAM,iBAAiB;AACpD,qBAAW,iBAAiB,IAAI;AAAA,QAClC;AACO,eAAA;AAAA,MAAA,CACR;AAAA,IACH,GAPwB;AAAA,EAQ1B;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc,CAAC;AAAA,IACf,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc,CAAC;AAAA,IACf,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SACE;AAAA,IACF,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,SAAS,gBAAgB;AAAA,IACjD,MAAM;AAAA,IACN,cAAc;AAAA,IACd,MAAM;AAAA,IACN,SAAS;AAAA,MACP,EAAE,OAAO,UAAU,eAAe,MAAM,WAAW;AAAA,MACnD,EAAE,OAAO,UAAU,aAAa,MAAM,SAAS;AAAA,MAC/C,EAAE,OAAO,UAAU,aAAa,MAAM,SAAS;AAAA,MAC/C,EAAE,OAAO,UAAU,aAAa,MAAM,SAAS;AAAA,IACjD;AAAA,EACF;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,QAAQ,oBAAoB;AAAA,IACpD,MAAM;AAAA,IACN,SACE;AAAA,IACF,MAAM;AAAA,IACN,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,QAAQ,oBAAoB;AAAA,IACpD,MAAM;AAAA,IACN,SACE;AAAA,IACF,MAAM;AAAA,IACN,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,QAAQ,wBAAwB;AAAA,IACxD,MAAM;AAAA,IACN,SACE;AAAA,IACF,MAAM;AAAA,IACN,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,QAAQ,wBAAwB;AAAA,IACxD,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,aAAa;AAAA,IACrC,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,cAAc;AAAA,IACd,cAAc;AAAA,IACd,iBAAiB;AAAA,EACnB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,QAAQ,aAAa;AAAA,IAC7C,MAAM;AAAA,IACN,cAAc,gBAAgB;AAAA,IAC9B,MAAM;AAAA,IACN,SAAS;AAAA,MACP,EAAE,OAAO,gBAAgB,MAAM,MAAM,OAAO;AAAA,MAC5C,EAAE,OAAO,gBAAgB,QAAQ,MAAM,SAAS;AAAA,MAChD,EAAE,OAAO,gBAAgB,OAAO,MAAM,QAAQ;AAAA,IAChD;AAAA,IACA,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,QAAQ,oBAAoB;AAAA,IACpD,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,UAAU,eAAe;AAAA,IACjD,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,WAAW,YAAY;AAAA,IAC/C,MAAM;AAAA,IACN,SACE;AAAA,IACF,cAAc;AAAA,IACd,MAAM;AAAA,IACN,OAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAAA,IACA,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,WAAW,iBAAiB;AAAA,IACpD,MAAM;AAAA,IACN,SACE;AAAA,IACF,cAAc;AAAA,IACd,MAAM;AAAA,IACN,OAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAAA,IACA,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,WAAW,iBAAiB;AAAA,IACpD,MAAM;AAAA,IACN,SACE;AAAA,IACF,MAAM;AAAA,IACN,OAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAAA,IACA,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,UAAU,UAAU;AAAA,IAC5C,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,IACA,SACE;AAAA,IACF,cAAc,UAAU;AAAA,EAC1B;AAAA;AAAA;AAAA,EAGA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,UAAU,kBAAkB;AAAA,IACpD,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA;AAAA,IAEN,cAAc,CAAC;AAAA,IACf,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,SACE;AAAA,IACF,MAAM;AAAA,IACN,cAAc,CAAC;AAAA,IACf,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,OAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAAA,IACA,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,SACE;AAAA,IACF,MAAM;AAAA,IACN,OAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,IACA,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,SAAS,YAAY,2BAA2B;AAAA,IAC3D,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,iBAAiB;AAAA,EACnB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,uBAAuB,OAAe;AAE7B,aAAA,MAAM,WAAW,SAAS,IAAI,MAAM,QAAQ,WAAW,EAAE,IAAI;AAAA,IACtE;AAAA,EACF;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc,CAAC;AAAA,IACf,iBAAiB;AAAA,EACnB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,SAAS,eAAe,mBAAmB;AAAA,IACtD,MAAM;AAAA,IACN,SACE;AAAA,IACF,MAAM;AAAA,IACN,cAAc;AAAA,IACd,SAAS,CAAC,UAAU,OAAO;AAAA,IAC3B,iBAAiB;AAAA,EACnB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,cAAc;AAAA,IACd,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,OAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAAA,IACA,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,UAAU,kBAAkB;AAAA,IACpD,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AACF;;;;;AC1qBA,UAAM,OAAO;AACP,UAAA,YAAY,IAA8B,IAAI;AACpD,UAAM,eAAe;AACrB,UAAM,eAAe;AACrB,UAAM,iBAAiB;AACvB,UAAM,cAAc;AACpB,UAAM,kBAAkB;AAAA,MACtB,MAAM,aAAa,IAAI,kBAAkB,MAAM;AAAA,IAAA;AAEjD,UAAM,uBAAuB;AAAA,MAAS,MACpC,aAAa,IAAI,qCAAqC;AAAA,IAAA;AAExD,UAAM,oBAAoB;AAAA,MAAS,MACjC,aAAa,IAAI,wBAAwB;AAAA,IAAA;AAE3C,UAAM,iBAAiB,SAAS,MAAM,aAAa,IAAI,sBAAsB,CAAC;AAC9E,UAAM,0BAA0B;AAAA,MAAS,MACvC,aAAa,IAAI,+BAA+B;AAAA,IAAA;AAGlD,gBAAY,MAAM;AACH,mBAAA,iBAAiB,aAAa,IAAI,2BAA2B;AAAA,IAAA,CAC3E;AAED,gBAAY,MAAM;AAChB,mBAAa,mBAAmB,aAAa;AAAA,QAC3C;AAAA,MAAA;AAAA,IACF,CACD;AAED,gBAAY,MAAM;AACV,YAAA,oBAAoB,aAAa,IAAI,iCAAiC;AAC5E,YAAM,YAAY,SAAS;AAAA,QACzB;AAAA,MAAA;AAGQ,gBAAA,QAAQ,CAAC,aAAkC;AACnD,iBAAS,aAAa;AAEtB,iBAAS,MAAM;AACf,iBAAS,KAAK;AAAA,MAAA,CACf;AAAA,IAAA,CACF;AAED;AAAA,MACE,MAAM,aAAa,IAAI,yBAAyB;AAAA,MAChD,MAAM;AACA,YAAA,CAAC,YAAY,OAAQ;AAEd,mBAAA,KAAKA,IAAS,MAAM,OAAO;AAChC,cAAA,CAAC,EAAE,QAAS;AACL,qBAAA,KAAK,EAAE,SAAS;AAErB,gBAAA,EAAE,iBAAiB,GAAG;AACxB,uCAAyB,CAAC;AAC1B,kBAAI,EAAE,eAAe;AACR,2BAAA,KAAK,EAAE,eAAe;AAC/B,2CAAyB,CAAC;AAAA,gBAC5B;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACSA,YAAA,MAAM,eAAe,IAAI;AAAA,MACpC;AAAA,IAAA;AAGF,UAAM,sBAAsB;AAC5B,UAAM,oBAAoB;AAC1B;AAAA,MACE,CAAC,MAAM,YAAY,QAAQ,MAAM,aAAa,IAAI,oBAAoB,CAAC;AAAA,MACvE,CAAC,CAAC,QAAQ,gBAAgB,MAAM;AAC9B,YAAI,CAAC,OAAQ;AAEb,4BAAoB,iBAAiB,gBAAgB;AAAA,MACvD;AAAA,IAAA;AAEF;AAAA,MACE,MAAM,kBAAkB;AAAA,MACxB,CAAC,aAAa;AACC,qBAAA,IAAI,sBAAsB,QAAQ;AAAA,MACjD;AAAA,IAAA;AAGF,UAAM,sBAAsB,mCAAY;AAClC,UAAA;AACI,cAAA,WAAW,MAAM,IAAI;AACpB,eAAA,QAAQ,QAAQ,EAAE,QAAQ,CAAC,CAAC,QAAQ,OAAO,MAAM;AACjD,eAAA,OAAO,mBAAmB,QAAQ,OAAO;AAAA,QAAA,CAC/C;AAAA,eACM,OAAO;AACN,gBAAA,MAAM,oCAAoC,KAAK;AAAA,MACzD;AAAA,IAAA,GAR0B;AAWtB,UAAA,gBAAgB,IAAI,KAAK;AAC/B,UAAM,sBAAsB;AAE5B,kBAAc,SAAS;AACF;AAErB,cAAU,YAAY;AACD;AACO;AAClB;AACC;AAETA,UAAS,cAAc;AAEvB,qBAAe,UAAU;AAGzB,oBAAc,KAAKA,GAAQ;AAC3B,YAAM,oBAAoB;AAC1B,YAAM,aAAa;AACL,oBAAA,QAAQ,CAAC,YAAY;AACjC,qBAAa,WAAW,OAAO;AAAA,MAAA,CAChC;AAEK,YAAAA,IAAS,MAAM,UAAU,KAAK;AACpC,kBAAY,SAASA,IAAS;AAC9B,kBAAY,OAAO,uBAAuB;AAC1C,qBAAe,UAAU;AAGzB,aAAO,KAAK,IAAIA;AAET,aAAA,OAAO,IAAIA,IAAS;AAE3B,oBAAc,QAAQ;AAEtBA,UAAS,OAAO,oBAAoB;AAAA,QAClCA,IAAS,OAAO;AAAA,QAChB,MAAM,YAAY,oBAAoB;AAAA,MAAA;AAIxC,wBAAkB,iBAAiB,aAAa;AAAA,QAC9C;AAAA,MAAA;AAIF,YAAM,oBAAoB;AAC1B,0BAAoB,yBAAyB;AAG7C;AAAA,QACE,MAAM,aAAa,IAAI,cAAc;AAAA,QACrC,YAAY;AACJ,gBAAA,gBAAkB,EAAA,QAAQ,8BAA8B;AAC9D,6BAAA,EAAqB;QACvB;AAAA,MAAA;AAGF,WAAK,OAAO;AAAA,IAAA,CACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3ND,UAAM,QAAQ;AACd,UAAM,aAAa;AACnB,UAAM,eAAe;AAErB;AAAA,MACE,MAAM,WAAW;AAAA,MACjB,CAAC,gBAAgB;AACX,YAAA,YAAY,WAAW,GAAG;AAC5B;AAAA,QACF;AAEY,oBAAA,QAAQ,CAAC,YAAY;AAC/B,gBAAM,IAAI,OAAO;AAAA,QAAA,CAClB;AACD,mBAAW,gBAAgB;MAC7B;AAAA,MACA,EAAE,MAAM,KAAK;AAAA,IAAA;AAGf;AAAA,MACE,MAAM,WAAW;AAAA,MACjB,CAAC,qBAAqB;AAChB,YAAA,iBAAiB,WAAW,GAAG;AACjC;AAAA,QACF;AAEiB,yBAAA,QAAQ,CAAC,YAAY;AACpC,gBAAM,OAAO,OAAO;AAAA,QAAA,CACrB;AACD,mBAAW,mBAAmB;MAChC;AAAA,MACA,EAAE,MAAM,KAAK;AAAA,IAAA;AAGf;AAAA,MACE,MAAM,WAAW;AAAA,MACjB,CAAC,cAAc;AACb,YAAI,WAAW;AACb,gBAAM,gBAAgB;AACtB,qBAAW,qBAAqB;AAAA,QAClC;AAAA,MACF;AAAA,IAAA;AAGF,aAAS,sBAAsB;AAC7B,YAAM,eACJ,SAAS,eAAe,qBAAqB,KAAK,mBAAmB;AACvE,YAAM,OAAO,SACV,cAAc,yBAAyB,GACtC,sBAAsB;AAC1B,UAAI,CAAC,KAAM;AAEX,mBAAa,cAAc;AAAA;AAAA,aAEhB,KAAK,MAAM,EAAE;AAAA,eACX,OAAO,cAAc,KAAK,OAAO,KAAK,SAAS,EAAE;AAAA;AAAA;AAAA,IAGhE;AAdS;AAgBT,aAAS,qBAAqB;AACtB,YAAA,QAAQ,SAAS,cAAc,OAAO;AAC5C,YAAM,KAAK;AACF,eAAA,KAAK,YAAY,KAAK;AACxB,aAAA;AAAA,IACT;AALS;AAOT;AAAA,MACE,MAAM,aAAa,IAAI,kBAAkB;AAAA,MACzC,MAAM,SAAS,mBAAmB;AAAA,MAClC,EAAE,WAAW,KAAK;AAAA,IAAA;AAEpB;AAAA,MACE,MAAM,aAAa,IAAI,wBAAwB;AAAA,MAC/C,MAAM,SAAS,mBAAmB;AAAA,MAClC,EAAE,WAAW,KAAK;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3CpB,MAAM,gBAAgB;;;;AAFtB,UAAM,qBAAqB;AAC3B,UAAM,EAAE,WAAA,IAAe,YAAY,kBAAkB;AAGrD,UAAM,eAAe;AACrB,UAAM,gBAAgB;AAAA,MAAS,MAC7B,aAAa,IAAI,mCAAmC;AAAA,IAAA;AAGhD,UAAA,cAAc,wBAAC,cAAuB;AACtC,UAAA;AACJ,UAAI,WAAW;AACP,cAAA,gBAAgB,WAAW,QAAQ;AACzC,mBAAW,KAAK,IAAI,gBAAgB,GAAG,cAAc,KAAK;AAAA,MAAA,OACrD;AACC,cAAA,gBAAgB,WAAW,QAAQ;AAC9B,mBAAA,KAAK,MAAM,gBAAgB,CAAC;AAAA,MACzC;AAEA,iBAAW,QAAQ;AAAA,IAAA,GAVD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACyCpB,UAAM,iBAAiB;AACjB,UAAA,kBAAkB,YAAY,8BAAA,CAA+B;AACnE,UAAM,EAAE,MAAM,UAAA,IAAc,YAAY,sBAAuB,CAAA;AAEzD,UAAA,EAAE,GAAAD,OAAM;AACR,UAAA,0BAA0B,SAAS,OAAO;AAAA,MAC9C,UAAU;AAAA,QACR,KAAK;AAAA,QACL,OAAOA,GAAE,UAAU;AAAA,QACnB,SAASA,GAAE,sBAAsB;AAAA,QACjC,SAAS,6BAAM;AACb,oBAAU,QAAQ;AAAA,QACpB,GAFS;AAAA,MAGX;AAAA,MACA,SAAS;AAAA,QACP,KAAK;AAAA,QACL,OAAO,GAAGA,GAAE,UAAU,CAAC,KAAKA,GAAE,cAAc,CAAC;AAAA,QAC7C,SAASA,GAAE,qBAAqB;AAAA,QAChC,SAAS,6BAAM;AACb,oBAAU,QAAQ;AAAA,QACpB,GAFS;AAAA,MAGX;AAAA,MACA,QAAQ;AAAA,QACN,KAAK;AAAA,QACL,OAAO,GAAGA,GAAE,UAAU,CAAC,KAAKA,GAAE,eAAe,CAAC;AAAA,QAC9C,SAASA,GAAE,sBAAsB;AAAA,QACjC,SAAS,6BAAM;AACb,oBAAU,QAAQ;AAAA,QACpB,GAFS;AAAA,MAGX;AAAA,IACA,EAAA;AAEF,UAAM,0BAA0B;AAAA,MAC9B,MAAM,wBAAwB,MAAM,UAAU,KAAK;AAAA,IAAA;AAErD,UAAM,qBAAqB;AAAA,MAAS,MAClC,OAAO,OAAO,wBAAwB,KAAK;AAAA,IAAA;AAG7C,UAAM,kBAAkB,SAAS,MAAM,CAAC,CAAC,gBAAgB,MAAM,KAAK;AACpE,UAAM,kBAAkB;AAAA,MACtB,MAAM,gBAAgB,MAAM,QAAQ,KAAK,UAAU,UAAU;AAAA,IAAA;AAG/D,UAAM,eAAe;AACf,UAAA,cAAc,wBAAC,MAAa;AAChC,YAAM,YACJ,cAAc,KAAK,EAAE,WACjB,2BACA;AACN,mBAAa,QAAQ,SAAS;AAAA,IAAA,GALZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC2CpB,MAAM,mBAAmB;;;;AArJzB,UAAM,gBAAgB;AAEtB,UAAM,UAAU;AAAA,MACd,MAAM,cAAc,IAAI,kBAAkB,MAAM;AAAA,IAAA;AAG5C,UAAA,WAAW,IAAwB,IAAI;AACvC,UAAA,gBAAgB,IAAwB,IAAI;AAC5C,UAAA,WAAW,gBAAgB,6BAA6B,KAAK;AAC7D,UAAA,iBAAiB,gBAAgB,+BAA+B;AAAA,MACpE,GAAG;AAAA,MACH,GAAG;AAAA,IAAA,CACJ;AACK,UAAA;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IAAA,IACE,aAAa,UAAU;AAAA,MACzB,cAAc,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,MAC3B,QAAQ;AAAA,MACR,kBAAkB,SAAS;AAAA,IAAA,CAC5B;AAGD;AAAA,MACE,CAAC,GAAG,CAAC;AAAA,MACL,CAAC,CAAC,MAAM,IAAI,MAAM;AAChB,uBAAe,QAAQ,EAAE,GAAG,MAAM,GAAG;MACvC;AAAA,MACA,EAAE,UAAU,IAAI;AAAA,IAAA;AAIlB,UAAM,qBAAqB,6BAAM;AAC/B,UAAI,EAAE,UAAU,KAAK,EAAE,UAAU,GAAG;AAClC;AAAA,MACF;AACA,UAAI,eAAe,MAAM,MAAM,KAAK,eAAe,MAAM,MAAM,GAAG;AAC9D,UAAA,QAAQ,eAAe,MAAM;AAC7B,UAAA,QAAQ,eAAe,MAAM;AACV;AACrB;AAAA,MACF;AACA,UAAI,SAAS,OAAO;AAClB,cAAM,cAAc,OAAO;AAC3B,cAAM,eAAe,OAAO;AACtB,cAAA,YAAY,SAAS,MAAM;AAC3B,cAAA,aAAa,SAAS,MAAM;AAE9B,YAAA,cAAc,KAAK,eAAe,GAAG;AACvC;AAAA,QACF;AAEE,UAAA,SAAS,cAAc,aAAa;AACpC,UAAA,QAAQ,eAAe,aAAa;AACjB;MACvB;AAAA,IAAA,GAvByB;AAyB3B,cAAU,kBAAkB;AACtB,UAAA,SAAS,CAAC,eAAe;AAC7B,UAAI,YAAY;AACd,iBAAS,kBAAkB;AAAA,MAC7B;AAAA,IAAA,CACD;AAED,UAAM,gBAAgB,IAAI;AAAA,MACxB,GAAG,EAAE;AAAA,MACL,GAAG,EAAE;AAAA,MACL,aAAa,OAAO;AAAA,MACpB,cAAc,OAAO;AAAA,IAAA,CACtB;AACD,UAAM,uBAAuB,6BAAM;AACjC,oBAAc,QAAQ;AAAA,QACpB,GAAG,EAAE;AAAA,QACL,GAAG,EAAE;AAAA,QACL,aAAa,OAAO;AAAA,QACpB,cAAc,OAAO;AAAA,MAAA;AAAA,IACvB,GAN2B;AAQ7B;AAAA,MACE;AAAA,MACA,CAAC,kBAAkB;AACjB,YAAI,CAAC,eAAe;AAEG;QACvB;AAAA,MACF;AAAA,MACA,EAAE,WAAW,KAAK;AAAA,IAAA;AAGpB,UAAM,qBAAqB,6BAAM;AAC/B,UAAI,SAAS,OAAO;AAClB,cAAM,cAAc,OAAO;AAC3B,cAAM,eAAe,OAAO;AACtB,cAAA,YAAY,SAAS,MAAM;AAC3B,cAAA,aAAa,SAAS,MAAM;AAG5B,cAAA,eAAe,cAAc,MAAM;AACzC,cAAM,gBACJ,cAAc,MAAM,eAAe,cAAc,MAAM,IAAI;AACvD,cAAA,cAAc,cAAc,MAAM;AACxC,cAAM,iBACJ,cAAc,MAAM,gBAAgB,cAAc,MAAM,IAAI;AAG9D,cAAM,YAAY;AAAA,UAChB,EAAE,MAAM,QAAQ,UAAU,aAAa;AAAA,UACvC,EAAE,MAAM,SAAS,UAAU,cAAc;AAAA,UACzC,EAAE,MAAM,OAAO,UAAU,YAAY;AAAA,UACrC,EAAE,MAAM,UAAU,UAAU,eAAe;AAAA,QAAA;AAE7C,cAAM,cAAc,UAAU;AAAA,UAAO,CAAC,KAAK,SACzC,KAAK,WAAW,IAAI,WAAW,OAAO;AAAA,QAAA;AAIxC,cAAM,gBACJ,cAAc,MAAM,IAAI,cAAc,MAAM;AAC9C,cAAM,kBACJ,cAAc,MAAM,IAAI,cAAc,MAAM;AAG1C,YAAA,YAAY,SAAS,QAAQ;AAC/B,YAAE,QAAQ,YAAY;AACtB,YAAE,QAAQ,gBAAgB;AAAA,QAAA,WACjB,YAAY,SAAS,SAAS;AACrC,YAAA,QAAQ,cAAc,YAAY,YAAY;AAChD,YAAE,QAAQ,gBAAgB;AAAA,QAAA,WACjB,YAAY,SAAS,OAAO;AACrC,YAAE,QAAQ,kBAAkB;AAC5B,YAAE,QAAQ,YAAY;AAAA,QAAA,OACjB;AAEL,YAAE,QAAQ,kBAAkB;AAC1B,YAAA,QAAQ,eAAe,aAAa,YAAY;AAAA,QACpD;AAGA,UAAE,QAAQU,cAAAA,MAAM,EAAE,OAAO,GAAG,cAAc,SAAS;AACnD,UAAE,QAAQA,cAAAA,MAAM,EAAE,OAAO,GAAG,eAAe,UAAU;AAAA,MACvD;AAAA,IAAA,GAnDyB;AAsDV,qBAAA,QAAQ,UAAU,kBAAkB;AAE/C,UAAA,aAAa,OAAmC,YAAY;AAC5D,UAAA,gBAAgB,mBAAmB,UAAU;AAE7C,UAAA,2BAA2B,SAAS,MAAM;AAC1C,UAAA,CAAC,SAAS,OAAO;AACZ,eAAA;AAAA,MACT;AACA,YAAM,EAAE,OAAW,IAAA,SAAS,MAAM,sBAAsB;AAClD,YAAA,kBAAkB,EAAE,QAAQ;AAC5B,YAAA,gBAAgB,cAAc,OAAO;AAE3C,YAAM,gBACJ,KAAK,IAAI,iBAAiB,aAAa,IACvC,KAAK,IAAI,EAAE,OAAO,cAAc,IAAI,KAAK;AAC3C,aAAO,gBAAgB;AAAA,IAAA,CACxB;AAEK,UAAA,YAAY,CAAC,kBAAkB;AACnC,UAAI,CAAC,eAAe;AAElB,iBAAS,QAAQ,yBAAyB;AAAA,MAAA,OACrC;AAEL,iBAAS,QAAQ;AAAA,MACnB;AAAA,IAAA,CACD;AAEK,UAAA,WAAW,YAAoB,SAAS;AACxC,UAAA,CAAC,YAAY,wBAAwB,GAAG,CAAC,CAAC,UAAU,WAAW,MAAM;AACzE,eAAS,KAAK,mBAAmB;AAAA,QAC/B,YAAY;AAAA,QACZ,eAAe;AAAA,MAAA,CAChB;AAAA,IAAA,CACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5LD,UAAM,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACkBzB,UAAM,eAAe;AACrB,UAAM,oBAAoB;AAAA,MAAS,MACjC,aAAa,IAAI,kBAAkB,MAAM,QAAQ,SAAS;AAAA,IAAA;AAG5D,UAAM,iBAAiB;AACjB,UAAA,EAAE,GAAAV,OAAM;AACR,UAAA,oBAAoB,wBAAC,SAA6B;AAChD,YAAA,QAAQ,OAAO,KAAK,UAAU,aAAa,KAAK,MAAA,IAAU,KAAK;AAC/D,YAAA,kBAAkB,QACpBA,GAAE,cAAc,iBAAiB,KAAK,CAAC,IAAI,KAAK,IAChD;AAEG,aAAA;AAAA,QACL,GAAG;AAAA,QACH,OAAO;AAAA,QACP,OAAO,KAAK,OAAO,IAAI,iBAAiB;AAAA,MAAA;AAAA,IAC1C,GAVwB;AAa1B,UAAM,kBAAkB;AAAA,MAAS,MAC/B,eAAe,UAAU,IAAI,iBAAiB;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACLhD,UAAM,iBAAiB;AACvB,UAAM,eAAe;AACrB,UAAM,uBAAuB;AAAA,MAAS,MACpC,aAAa,IAAI,qCAAqC;AAAA,IAAA;AAExD,UAAM,cAAc,SAAS,MAAM,aAAa,IAAI,kBAAkB,CAAC;AACvE,UAAM,kBAAkB,SAAS,MAAM,YAAY,UAAU,UAAU;AACvE,UAAM,cAAc;AAAA,MAClB,MAAM,gBAAgB,SAAS,CAAC,eAAe;AAAA,IAAA;AAG3C,UAAA,YAAY,IAA2B,IAAI;AAEjD,cAAU,MAAM;AACd,UAAI,UAAU,OAAO;AACnB,kBAAU,MAAM,YAAY,IAAI,KAAK,OAAO;AAAA,MAC9C;AAAA,IAAA,CACD;AAEK,UAAA,aAAa,IAA2B,IAAI;AAClD,YAAQ,cAAc,UAAU;AAC1B,UAAA,WAAW,YAAoB,SAAS;AACxC,UAAA,aAAa,IAAI,KAAK;AACtB,UAAA,cAAc,IAAI,KAAK;AACpB,aAAA,GAAG,CAAC,OAAe,YAAiB;AAC3C,UAAI,UAAU,mBAAmB;AAC/B,mBAAW,QAAQ,QAAQ;AACf,oBAAA,QAAQ,QAAQ,iBAAiB,QAAQ;AAAA,MACvD;AAAA,IAAA,CACD;AAED,cAAU,MAAM;AACd,UAAI,cAAc;AAChB,oBAAA,EAAc,YAAY;AAAA,UACxB,QAAQ,WAAW,OAAO,wBAAwB,UAAU;AAAA,QAAA,CAC7D;AAAA,MACH;AAAA,IAAA,CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClEM,SAAS,kBAAkC;AAChD,QAAM,kBAAkB;AACxB,QAAM,gBAAgB;AACtB,QAAM,gBAAgB;AACtB,QAAM,oBAAoB;AACpB,QAAA,aAAa,6BAAM,cAAc,gBAAgB,eAApC;AAEnB,QAAM,mBAAmB,6BAAoB;AACrC,UAAA,gBAAgB,IAAI,OAAO;AACjC,UAAM,SAAuB,CAAA;AAC7B,QAAI,eAAe;AACjB,iBAAW,KAAK,eAAe;AACvB,cAAA,OAAO,cAAc,CAAC;AAC5B,eAAO,KAAK,IAAI;AAAA,MAClB;AAAA,IACF;AACO,WAAA;AAAA,EAAA,GATgB;AAYnB,QAAA,0BAA0B,wBAAC,SAA0B;AACxC,qBAAA,EAAE,QAAQ,CAAC,SAAS;AAC/B,UAAA,KAAK,SAAS,MAAM;AACtB,aAAK,OAAO,gBAAgB;AAAA,MAAA,OACvB;AACL,aAAK,OAAO;AAAA,MACd;AAAA,IAAA,CACD;AAAA,EAAA,GAP6B;AAUzB,SAAA;AAAA,IACL;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM,gBAAgB,kBAAkB,GAAxC;AAAA,IACZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACd,YAAI,GAAG;MACT,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU,6BAAM,gBAAgB,oBAAoB,GAA1C;AAAA,IACZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,mCAAY;AACd,cAAA,WAAW,iBAAmB,EAAA;AACpC,YAAI,CAAC,SAAU;AAET,cAAA,gBAAgB,aAAa,QAAQ;AAAA,MAC7C,GALU;AAAA,IAMZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,mCAAY;AACd,cAAA,WAAW,iBAAmB,EAAA;AACpC,YAAI,CAAC,SAAU;AAET,cAAA,gBAAgB,eAAe,QAAQ;AAAA,MAC/C,GALU;AAAA,IAMZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACE,wBAAA,eAAe,YAAY,UAAU;AAAA,MACvD,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACE,wBAAA,eAAe,gBAAgB,QAAQ;AAAA,MACzD,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU,mCAAY;AACd,cAAA,WAAA,GAAc;MACtB,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU,mCAAY;AACd,cAAA,WAAA,GAAc;MACtB,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU,6BAAM;AACd,cAAM,eAAe;AACrB,YACE,CAAC,aAAa,IAAI,oBAAoB,KACtC,QAAQ,iBAAiB,GACzB;AACA,cAAI,MAAM;AACV,cAAI,MAAM;AACV,cAAI,oBAAoB,cAAc;AAAA,QACxC;AAAA,MACF,GAVU;AAAA,IAWZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU,6BAAM;AACd,4BAAA,EAAsB;MACxB,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU,6BAAM;AACd,YAAI,cAAc;AAAA,MACpB,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU,mCAAY;AACpB,cAAM,IAAI;MACZ,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU,mCAAY;AACpB,cAAM,IAAI;AACV,sBAAA,EAAgB,IAAI;AAAA,UAClB,UAAU;AAAA,UACV,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,MAAM;AAAA,QAAA,CACP;AAAA,MACH,GARU;AAAA,IASZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU,mCAAY;AACpB,cAAM,cAAc,EAAE,MAAM,CAAC,OAAO,CAAC;AACrC,sBAAA,EAAgB,IAAI;AAAA,UAClB,UAAU;AAAA,UACV,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,MAAM;AAAA,QAAA,CACP;AAAA,MACH,GARU;AAAA,IASZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU,6BAAM;AACd,sBAAc,4BAA4B;AAAA,MAC5C,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU,6BAAM;AACR,cAAA,KAAK,IAAI,OAAO;AACnB,WAAA;AAAA,UACD,GAAG,QAAQ;AAAA,UACX,GAAG,UAAU,CAAC,GAAG,QAAQ,QAAQ,GAAG,GAAG,QAAQ,SAAS,CAAC,IAAI;AAAA,QAAA;AAE3D,YAAA,OAAO,SAAS,MAAM,IAAI;AAAA,MAChC,GAPU;AAAA,IAQZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU,6BAAM;AACR,cAAA,KAAK,IAAI,OAAO;AACnB,WAAA;AAAA,UACD,GAAG,QAAQ;AAAA,UACX,GAAG,UAAU,CAAC,GAAG,QAAQ,QAAQ,GAAG,GAAG,QAAQ,SAAS,CAAC,IAAI;AAAA,QAAA;AAE3D,YAAA,OAAO,SAAS,MAAM,IAAI;AAAA,MAChC,GAPU;AAAA,IAQZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU,6BAAM;AACV,YAAA,IAAI,OAAO,OAAO;AACpB,wBAAA,EAAgB,IAAI;AAAA,YAClB,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,UAAA,CACP;AACD;AAAA,QACF;AACA,YAAI,OAAO;MACb,GAVU;AAAA,IAWZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU,6BAAM;AACd,YAAI,OAAO,WAAW,IAAI,CAAC,IAAI,OAAO,WAAW;AAAA,MACnD,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MAEd,WAAW,MAAM;AACf,cAAM,eAAe;AACrB,YAAI,sBAAsB,UAAU;AAEpC,eAAO,MAAM;AACL,gBAAA,cAAc,aAAa,IAAI,sBAAsB;AAEvD,cAAA,gBAAgB,UAAU,aAAa;AAE5B,yBAAA,IAAI,wBAAwB,mBAAmB;AAAA,UAAA,OACvD;AAEiB,kCAAA;AACT,yBAAA,IAAI,wBAAwB,UAAU,WAAW;AAAA,UAChE;AAAA,QAAA;AAAA,MACF,GACC;AAAA,IACL;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACR,cAAA,aAAa,sBAAwB,EAAA;AACvC,YAAA,YAAY,GAAG,UAAU;AAAA,MAC/B,GAHU;AAAA,IAIZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACR,cAAA,aAAa,sBAAwB,EAAA;AACvC,YAAA,YAAY,IAAI,UAAU;AAAA,MAChC,GAHU;AAAA,IAIZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACd,sBAAc,mBAAmB;AAAA,MACnC,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACR,cAAA,EAAE,OAAW,IAAA;AACf,YAAA,CAAC,OAAO,eAAe,MAAM;AAC/B,wBAAA,EAAgB,IAAI;AAAA,YAClB,UAAU;AAAA,YACV,SAAS;AAAA,YACT,QACE;AAAA,YACF,MAAM;AAAA,UAAA,CACP;AACD;AAAA,QACF;AACM,cAAA,QAAQ,IAAI;AACZ,cAAA,UAAU,kBAAkB;AAAA,UAChC;AAAA,QAAA;AAEI,cAAA,SAAS,OAAO,eAAe,OAAO;AACrC,eAAA,OAAO,IAAI,KAAK;AACvB,8BAAsB,oBAAoB;AAAA,MAC5C,GAnBU;AAAA,IAoBZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACd,wBAAgB,uBAAuB;AAAA,MACzC,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACd,wBAAgB,2BAA2B;AAAA,MAC7C,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACd,gCAAwB,gBAAgB,KAAK;AACzC,YAAA,OAAO,SAAS,MAAM,IAAI;AAAA,MAChC,GAHU;AAAA,IAIZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACd,gCAAwB,gBAAgB,MAAM;AAC1C,YAAA,OAAO,SAAS,MAAM,IAAI;AAAA,MAChC,GAHU;AAAA,IAIZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACG,yBAAA,EAAE,QAAQ,CAAC,SAAS;AAC9B,eAAA,IAAI,CAAC,KAAK,MAAM;AAAA,QAAA,CACtB;AACG,YAAA,OAAO,SAAS,MAAM,IAAI;AAAA,MAChC,GALU;AAAA,IAMZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACH,mBAAA,QAAQ,IAAI,OAAO,eAAe;AACvC,cAAA,gBAAgB,cAAc,gBAAgB,aAAa;AACxD,iBAAA,IAAI,CAAC,KAAK,MAAM;AAAA,UACvB;AAAA,QACF;AACI,YAAA,OAAO,SAAS,MAAM,IAAI;AAAA,MAChC,GAPU;AAAA,IAQZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACG,yBAAA,EAAE,QAAQ,CAAC,SAAS;AACnC,eAAK,SAAS;AAAA,QAAA,CACf;AACG,YAAA,OAAO,SAAS,MAAM,IAAI;AAAA,MAChC,GALU;AAAA,IAMZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,WAAW,MAAM;AACf,YAAI,oBAA4B,2BAA2B;AAC3D,YAAI,qBAA6B,4BAA4B;AAE7D,eAAO,MAAM;AACX,gBAAM,eAAe;AACrB,gBAAM,QAAQ,kBAAkB;AAChC,cAAI,MAAM,aAAa;AACrB,iCAAqB,MAAM;AACd,yBAAA,IAAI,sBAAsB,iBAAiB;AAAA,UAAA,OACnD;AACL,gCAAoB,MAAM;AACb,yBAAA,IAAI,sBAAsB,kBAAkB;AAAA,UAC3D;AAAA,QAAA;AAAA,MACF,GACC;AAAA,IACL;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACd,4BAAA,EAAsB;MACxB,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACd,0BAAA,EAAoB;MACtB,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACH,mBAAA,SAAS,IAAI,OAAO,eAAe;AAC5C,cAAI,iBAAiB,aAAa;AAChC,kBAAM,qBAAqB;AACrB,kBAAA,UAAU,kBAAkB;AAAA,cAChC;AAAA,YAAA;AAEI,kBAAA,SAAS,MAAM,UAAU,OAAO;AACtC,gBAAI,MAAM;UACZ;AAAA,QACF;AAAA,MACF,GAXU;AAAA,IAYZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,cAAc;AAAA,MACd,UAAU,6BAAM;AACP,eAAA;AAAA,UACL;AAAA,UACA;AAAA,QAAA;AAAA,MAEJ,GALU;AAAA,IAMZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,cAAc;AAAA,MACd,UAAU,6BAAM;AACP,eAAA,KAAK,2BAA2B,QAAQ;AAAA,MACjD,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,cAAc;AAAA,MACd,UAAU,6BAAM;AACP,eAAA,KAAK,iCAAiC,QAAQ;AAAA,MACvD,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACd,0BAAA,EAAoB;MACtB,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,cAAc;AAAA,MACd,UAAU,6BAAM;AACd,sBAAc,mBAAmB,OAAO;AAAA,MAC1C,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACE,wBAAA,kBAAkB,cAAc,cAAe;AAAA,MACjE,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACd,YAAI,cAAc;AACA,0BAAA,cAAc,cAAc,cAAc;AAAA,MAC9D,GAHU;AAAA,IAIZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACd,sBAAc,sBAAsB;AAAA,UAClC,OAAO,EAAE,YAAY;AAAA,UACrB,UAAU,EAAE,2BAA2B;AAAA,UACvC,YAAY;AAAA,YACV,WAAW;AAAA,YACX,eAAe,CAAC,eAAe,UAAU;AAAA,UAC3C;AAAA,QAAA,CACD;AAAA,MACH,GATU;AAAA,IAUZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,cAAc;AAAA,MACd,UAAU,6BAAM;AACP,eAAA,KAAK,4BAA4B,QAAQ;AAAA,MAClD,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACd,YAAI,OAAO;AACP,YAAA,OAAO,SAAS,MAAM,IAAI;AAAA,MAChC,GAHU;AAAA,IAIZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACd,sBAAc,kBAAkB;AAAA,MAClC,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACd,sBAAc,0BAA0B;AAAA,MAC1C,GAFU;AAAA,IAGZ;AAAA,EAAA;AAEJ;AA/jBgB;AC5BJ,IAAA,wCAAAW,yBAAL;AACLA,uBAAA,YAAa,IAAA;AACbA,uBAAA,MAAO,IAAA;AACPA,uBAAA,YAAa,IAAA;AACbA,uBAAA,OAAQ,IAAA;AAJEA,SAAAA;AAAA,GAAA,uBAAA,CAAA,CAAA;AAOA,IAAA,6BAAAC,cAAL;AACLA,YAAA,OAAQ,IAAA;AACRA,YAAA,MAAO,IAAA;AACPA,YAAA,SAAU,IAAA;AACVA,YAAA,OAAQ,IAAA;AACRA,YAAA,UAAW,IAAA;AALDA,SAAAA;AAAA,GAAA,YAAA,CAAA,CAAA;AAQA,IAAA,iCAAAC,kBAAL;AACLA,gBAAA,KAAM,IAAA;AACNA,gBAAA,MAAO,IAAA;AACPA,gBAAA,QAAS,IAAA;AACTA,gBAAA,QAAS,IAAA;AAJCA,SAAAA;AAAA,GAAA,gBAAA,CAAA,CAAA;AAOA,IAAA,+BAAAC,gBAAL;AAELA,cAAA,MAAO,IAAA;AAEPA,cAAA,SAAU,IAAA;AAEVA,cAAA,QAAS,IAAA;AANCA,SAAAA;AAAA,GAAA,cAAA,CAAA,CAAA;AASA,IAAA,+BAAAC,gBAAL;AAELA,cAAA,MAAO,IAAA;AAEPA,cAAA,SAAU,IAAA;AAEVA,cAAA,QAAS,IAAA;AANCA,SAAAA;AAAA,GAAA,cAAA,CAAA,CAAA;AASA,IAAA,2CAAAC,4BAAL;AACLA,0BAAA,MAAO,IAAA;AACPA,0BAAA,MAAO,IAAA;AACPA,0BAAA,MAAO,IAAA;AACPA,0BAAA,MAAO,IAAA;AACPA,0BAAA,MAAO,IAAA;AACPA,0BAAA,WAAY,IAAA;AACZA,0BAAA,SAAU,IAAA;AAPAA,SAAAA;AAAA,GAAA,0BAAA,CAAA,CAAA;AAUA,IAAA,yCAAAC,0BAAL;AACLA,wBAAA,MAAO,IAAA;AACPA,wBAAA,OAAQ,IAAA;AACRA,wBAAA,MAAO,IAAA;AACPA,wBAAA,SAAU,IAAA;AAJAA,SAAAA;AAAA,GAAA,wBAAA,CAAA,CAAA;AAOA,IAAA,mCAAAC,oBAAL;AACLA,kBAAA,MAAO,IAAA;AACPA,kBAAA,SAAU,IAAA;AACVA,kBAAA,UAAW,IAAA;AACXA,kBAAA,YAAa,IAAA;AACbA,kBAAA,SAAU,IAAA;AACVA,kBAAA,QAAS,IAAA;AACTA,kBAAA,KAAM,IAAA;AAPIA,SAAAA;AAAA,GAAA,kBAAA,CAAA,CAAA;ACnCL,MAAM,wBAA6C;AAAA;AAAA,EAExD;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,QAAQ;AAAA,IACnB,MAAM;AAAA,IACN,SAAS,OAAO,OAAO,UAAU;AAAA,IACjC,cAAc,WAAW;AAAA,IACzB,UAAU,wBAAC,UAAsB;AAC/B,cAAQ,OAAO;AAAA,QACb,KAAK,WAAW;AACd,iBAAO;QACT,KAAK,WAAW;AACP,iBAAA;AAAA,YACL,CAAC,aAAa,GAAG;AAAA,UAAA;AAAA,QAErB,KAAK,WAAW;AACP,iBAAA;AAAA,YACL,CAAC,qBAAqB,GAAG;AAAA,UAAA;AAAA,MAE/B;AAAA,IACF,GAbU;AAAA,EAcZ;AACF;AAEO,MAAM,sBAA2C;AAAA;AAAA,EAEtD;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,SAAS;AAAA,IACpB,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,SAAS;AAAA,IACpB,MAAM;AAAA;AAAA,IAEN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,SAAS;AAAA,IACpB,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,SAAS;AAAA,IACpB,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,SAAS;AAAA,IACpB,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,SAAS;AAAA,IACpB,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA;AAAA,EAGA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,MAAM;AAAA,IACjB,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,MAAM;AAAA,IACjB,MAAM;AAAA,IACN,SAAS,OAAO,OAAO,UAAU;AAAA,IACjC,cAAc,WAAW;AAAA,IACzB,UAAU,wBAAC,UAAsB;AAC/B,cAAQ,OAAO;AAAA,QACb,KAAK,WAAW;AACd,iBAAO;QACT,KAAK,WAAW;AACP,iBAAA;AAAA,YACL,CAAC,aAAa,GAAG;AAAA,UAAA;AAAA,QAErB,KAAK,WAAW;AACP,iBAAA;AAAA,YACL,CAAC,qBAAqB,GAAG;AAAA,UAAA;AAAA,MAE/B;AAAA,IACF,GAbU;AAAA,EAcZ;AAAA;AAAA,EAGA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,WAAW;AAAA,IACtB,MAAM;AAAA,IACN,SAAS;AAAA,MACP,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,IACzB;AAAA,IACA,cAAc,uBAAuB;AAAA,IACrC,SAAS;AAAA,IACT,UAAU,wBAAC,UAAkC;AAC3C,cAAQ,OAAO;AAAA,QACb,KAAK,uBAAuB;AAC1B,iBAAO;QACT,KAAK,uBAAuB;AACnB,iBAAA;AAAA,YACL,CAAC,YAAY,GAAG;AAAA,UAAA;AAAA,QAEpB,KAAK,uBAAuB;AACnB,iBAAA;AAAA,YACL,CAAC,YAAY,GAAG;AAAA,UAAA;AAAA,QAEpB;AACE,iBAAO;MACX;AAAA,IACF,GAfU;AAAA,EAgBZ;AAAA;AAAA,EAGA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,WAAW;AAAA,IACtB,MAAM;AAAA,IACN,SAAS;AAAA,MACP,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,IACzB;AAAA,IACA,cAAc,uBAAuB;AAAA,IACrC,SAAS;AAAA,IACT,UAAU,wBAAC,UAAkC;AAC3C,cAAQ,OAAO;AAAA,QACb,KAAK,uBAAuB;AAC1B,iBAAO;QACT;AACS,iBAAA;AAAA,YACL,CAAC,GAAG,MAAM,YAAY,CAAC,OAAO,GAAG;AAAA,UAAA;AAAA,MAEvC;AAAA,IACF,GATU;AAAA,EAUZ;AAAA;AAAA,EAGA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,WAAW;AAAA,IACtB,MAAM;AAAA,IACN,SAAS;AAAA,MACP,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,IACzB;AAAA,IACA,cAAc,uBAAuB;AAAA,IACrC,SAAS;AAAA,IACT,UAAU,wBAAC,UAAkC;AAC3C,cAAQ,OAAO;AAAA,QACb,KAAK,uBAAuB;AAC1B,iBAAO;QACT;AACS,iBAAA;AAAA,YACL,CAAC,GAAG,MAAM,YAAY,CAAC,MAAM,GAAG;AAAA,UAAA;AAAA,MAEtC;AAAA,IACF,GATU;AAAA,EAUZ;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,WAAW;AAAA,IACtB,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA;AAAA,EAGA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,WAAW;AAAA,IACtB,MAAM;AAAA,IACN,SAAS;AAAA,MACP,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,IACzB;AAAA,IACA,cAAc,uBAAuB;AAAA,IACrC,SAAS;AAAA,IACT,UAAU,wBAAC,UAAkC;AAC3C,cAAQ,OAAO;AAAA,QACb,KAAK,uBAAuB;AAC1B,iBAAO;QACT;AACS,iBAAA;AAAA,YACL,CAAC,GAAG,MAAM,YAAY,CAAC,WAAW,GAAG;AAAA,UAAA;AAAA,MAE3C;AAAA,IACF,GATU;AAAA,EAUZ;AAAA;AAAA,EAGA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,QAAQ;AAAA,IACnB,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,QAAQ;AAAA,IACnB,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,QAAQ;AAAA,IACnB,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA;AAAA,EAGA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,SAAS;AAAA,IACpB,MAAM;AAAA,IACN,SAAS,OAAO,OAAO,mBAAmB;AAAA,IAC1C,cAAc,oBAAoB;AAAA,EACpC;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,SAAS;AAAA,IACpB,MAAM;AAAA,IACN,cAAc;AAAA,IACd,OAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAAA,EACF;AAAA;AAAA,EAGA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,OAAO;AAAA,IAClB,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,OAAO;AAAA,IAClB,MAAM;AAAA,IACN,cAAc;AAAA,IACd,SAAS;AAAA,EACX;AAAA;AAAA,EAGA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,WAAW;AAAA,IACtB,MAAM;AAAA,IACN,SAAS,OAAO,OAAO,oBAAoB;AAAA,IAC3C,cAAc,qBAAqB;AAAA,IACnC,UAAU,wBAAC,UAAgC;AACzC,cAAQ,OAAO;AAAA,QACb,KAAK,qBAAqB;AACxB,iBAAO;QACT;AACS,iBAAA;AAAA,YACL,CAAC,OAAO,MAAM,YAAY,CAAC,kBAAkB,GAAG;AAAA,UAAA;AAAA,MAEtD;AAAA,IACF,GATU;AAAA,EAUZ;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,WAAW;AAAA,IACtB,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,WAAW;AAAA,IACtB,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA;AAAA,EAGA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,QAAQ;AAAA,IACnB,MAAM;AAAA,IACN,SAAS,OAAO,OAAO,cAAc;AAAA,IACrC,cAAc,eAAe;AAAA,IAC7B,UAAU,wBAAC,UAA0B;AACnC,cAAQ,OAAO;AAAA,QACb,KAAK,eAAe;AAClB,iBAAO;QACT;AACS,iBAAA;AAAA,YACL,CAAC,KAAK,GAAG;AAAA,UAAA;AAAA,MAEf;AAAA,IACF,GATU;AAAA,EAUZ;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,QAAQ;AAAA,IACnB,MAAM;AAAA,IACN,cAAc;AAAA,IACd,SACE;AAAA,EACJ;AAAA;AAAA,EAGA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS,OAAO,OAAO,YAAY;AAAA,IACnC,cAAc,aAAa;AAAA,EAC7B;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,SACE;AAAA,IACF,UAAU,CAAC,QAAQ;AAAA,IACnB,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS,OAAO,OAAO,QAAQ;AAAA,IAC/B,cAAc,SAAS;AAAA,IACvB,UAAU,wBAAC,UAAoB;AACtB,aAAA;AAAA,QACL,SAAS;AAAA,MAAA;AAAA,IAEb,GAJU;AAAA,EAKZ;AAAA;AAAA,EAEA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,aAAa;AAAA,IACxB,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,aAAa;AAAA,IACxB,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AACF;AC5bO,SAAS,wBAAwB;AACtC,QAAM,kBAAkB;AACxB,QAAM,qBAAqB;AAE3B,MAAI,kBAAkB;AACtB,MAAI,gBAAgB;AAChB,MAAA,iBAAiB,gBAAgB,MAAM;AACrC,QAAA,mBAAmB,SAAS,UAAU;AACxC,UAAI,eAAe;AACC,0BAAA;AAAA,MAAA,OACb;AACa,0BAAA;AACd,YAAA,YAAY,GAAG,mBAAmB,UAAU;AAChD;AAAA,MACF;AAAA,IACF;AAAA,EAAA,CACD;AAEe,kBAAA;AAAA,IACd,MAAM;AACJ,sBAAgB,gBAAgB;AAChC,UAAI,CAAC,iBAAiB,CAAC,IAAI,oBAAoB;AAC7C,YACE,mBAAmB,SAAS,aAC3B,mBAAmB,SAAS,YAAY,iBACzC;AACkB,4BAAA;AACd,cAAA,YAAY,GAAG,mBAAmB,UAAU;AAAA,QAClD;AAAA,MACF;AAAA,IACF;AAAA,IACA,EAAE,UAAU,KAAK;AAAA,EAAA;AAErB;AAjCgB;;;;;;;;;;;;;;;;;ACqDM;AAEhB,UAAA,EAAE,GAAAlB,OAAM;AACd,UAAM,QAAQ;AACd,UAAM,eAAe;AACrB,UAAM,iBAAiB;AACvB,UAAM,oBAAoB;AAC1B,UAAM,aAAa;AAEnB;AAAA,MACE,MAAM,kBAAkB;AAAA,MACxB,CAAC,aAAa;AACZ,cAAM,mBAAmB;AACzB,YAAI,SAAS,aAAa;AACf,mBAAA,KAAK,UAAU,OAAO,gBAAgB;AAAA,QAAA,OAC1C;AACI,mBAAA,KAAK,UAAU,IAAI,gBAAgB;AAAA,QAC9C;AAEA,YAAI,cAAc;AAChB,sBAAA,EAAc,YAAY;AAAA,YACxB,OAAO;AAAA,YACP,aAAa,SAAS,OAAO,WAAW,YAAY;AAAA,UAAA,CACrD;AAAA,QACH;AAAA,MACF;AAAA,MACA,EAAE,WAAW,KAAK;AAAA,IAAA;AAGpB,QAAI,cAAc;AAChB;AAAA,QACE,MAAM,WAAW;AAAA,QACjB,CAAC,UAAU,aAAa;AAEtB,gBAAM,oBAAoB,IAAI;AAAA,YAC5B,SAAS,OAAO,CAAC,SAAS,KAAK,SAAS,EAAE,IAAI,CAAC,SAAS,KAAK,QAAQ;AAAA,UAAA;AAGpE,mBAAA;AAAA,YACC,CAAC,SAAS,kBAAkB,IAAI,KAAK,QAAQ,KAAK,KAAK;AAAA,UAAA,EAExD,QAAQ,CAAC,SAAS;AACjB,wBAAA,EAAc,OAAO;AAAA,cACnB,aAAa,KAAK,cAAc,YAAa,CAAA;AAAA,cAC7C;AAAA,YAAA;AAEU,0BAAE,OAAO,WAAW,aAAa;AAAA,cAC3C,QAAQ,KAAK,cAAc,YAAY;AAAA,YAAA,CACxC;AAAA,UAAA,CACF;AAAA,QACL;AAAA,QACA,EAAE,MAAM,KAAK;AAAA,MAAA;AAAA,IAEjB;AAEA,gBAAY,MAAM;AACV,YAAA,WAAW,aAAa,IAAI,+BAA+B;AACjE,eAAS,gBAAgB,MAAM;AAAA,QAC7B;AAAA,QACA,GAAG,QAAQ;AAAA,MAAA;AAAA,IACb,CACD;AAED,gBAAY,MAAM;AACV,YAAA,UAAU,aAAa,IAAI,gCAAgC;AACjE,eAAS,gBAAgB,MAAM;AAAA,QAC7B;AAAA,QACA,GAAG,OAAO;AAAA,MAAA;AAAA,IACZ,CACD;AAED,gBAAY,MAAM;AACV,YAAA,SAAS,aAAa,IAAI,cAAc;AAC9C,UAAI,QAAQ;AACL,aAAA,OAAO,OAAO,QAAQ;AAAA,MAC7B;AAAA,IAAA,CACD;AAEK,UAAA,aAAa,SAAS,MAAM;AACzB,aAAA,aAAa,IAAI,kBAAkB;AAAA,IAAA,CAC3C;AACD,gBAAY,MAAM;AACZ,UAAA,WAAW,UAAU,YAAY;AACnC,YAAI,GAAG,cAAc,MAAM,YAAY,WAAW,OAAO;AACzD,YAAI,GAAG;MAAoB,OACtB;AACL,YAAI,GAAG,cAAc,MAAM,YAAY,WAAW,MAAM;AAAA,MAC1D;AAAA,IAAA,CACD;AAED,gBAAY,MAAM;AACL,iBAAA,kBAAkB,aAAa,IAAI,6BAA6B;AAAA,IAAA,CAC5E;AAED,UAAM,OAAO,6BAAM;AACjB,YAAM,eAAe;AACL,sBAAA,EAAE,iBAAiB,YAAY;AAC/C,uBAAA,EAAmB;AACnB,2BAAA,EAAuB;AACvB,yBAAA,EAAqB;AACrB,0BAAA,EAAsB;AACtB,UAAI,mBAAmB;IAAkB,GAP9B;AAUb,UAAM,6BAA6B;AAC7B,UAAA,WAAW,8BAAO,MAA0C;AAChE,iCAA2B,OAAO,CAAC;AACnC,YAAM,WAAW;IAAO,GAFT;AAKjB,UAAM,sBAA2C;AAAA,MAC/C,UAAU;AAAA,MACV,SAASA,GAAE,gBAAgB;AAAA,IAAA;AAG7B,UAAM,iBAAiB,6BAAM;AAC3B,YAAM,OAAO,mBAAmB;AAChC,YAAM,IAAI,mBAAmB;AAAA,IAAA,GAFR;AAKvB,UAAM,gBAAgB,6BAAM;AAC1B,YAAM,OAAO,mBAAmB;AAChC,YAAM,IAAI;AAAA,QACR,UAAU;AAAA,QACV,SAASA,GAAE,eAAe;AAAA,QAC1B,MAAM;AAAA,MAAA,CACP;AAAA,IAAA,GANmB;AAStB,cAAU,MAAM;AACV,UAAA,iBAAiB,UAAU,QAAQ;AACnC,UAAA,iBAAiB,gBAAgB,cAAc;AAC/C,UAAA,iBAAiB,eAAe,aAAa;AACjD,qBAAe,oBAAoB;AAE/B,UAAA;AACG;eACE,GAAG;AACF,gBAAA,MAAM,mCAAmC,CAAC;AAAA,MACpD;AAAA,IAAA,CACD;AAED,oBAAgB,MAAM;AAChB,UAAA,oBAAoB,UAAU,QAAQ;AACtC,UAAA,oBAAoB,gBAAgB,cAAc;AAClD,UAAA,oBAAoB,eAAe,aAAa;AACpD,qBAAe,sBAAsB;AAAA,IAAA,CACtC;AAED,qBAAiB,QAAQ,WAAW,qBAAqB,EAAE,cAAc;AAEzE,UAAM,EAAE,uBAAuB,2BAA2B,IAAI,iBAAiB;AAC/E,UAAM,eAAe,6BAAM;AACzB;AAAA,QACE,MAAM;AAGkB,gCAAA,qBAAA,EAAuB,uBAAuB;AAG9C,gCAAA,uBAAuB,gBAAgB;AAAA,YAC3D;AAAA,YACA,aAAa,IAAI,iCAAiC;AAAA,UAAA;AAIzB,qCAAA,cAAA,EAAgB,gBAAgB;AAGhC,qCAAA,sBAAA,EAAwB,mBAAmB;AAKtD,4BAAE,kBAAkB,4BAA4B,EAAE;AAAA,QACpE;AAAA,QACA,EAAE,SAAS,IAAK;AAAA,MAAA;AAAA,IAClB,GAzBmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}