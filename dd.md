输入图像形状: torch.Size([1, 1024, 1024, 3])
转换后图像形状: torch.Size([1, 3, 1024, 1024])
正在加载LBM法线生成模型...
正在使用配置文件: /root/ComfyUI/models/lbm/normals/config.yaml
未找到指定VAE路径，使用模型根目录: /root/ComfyUI/models/lbm/normals
已加载VAE配置: /root/ComfyUI/models/lbm/normals/config.json
使用VAE权重文件: /root/ComfyUI/models/lbm/normals/diffusion_pytorch_model.safetensors
使用diffusers.AutoencoderKL创建VAE模型
从本地路径加载VAE模型: /root/ComfyUI/models/lbm/normals
加载权重文件: /root/ComfyUI/models/lbm/normals/diffusion_pytorch_model.safetensors
VAE权重加载成功
VAE模型创建成功
使用UNet配置: 通道数=4
找到UNet权重文件: /root/ComfyUI/models/lbm/normals/model.safetensors
使用UNet权重文件: /root/ComfyUI/models/lbm/normals/model.safetensors
尝试创建完整的UNet模型，通道数=4
尝试直接从/root/ComfyUI/models/lbm/normals加载预训练UNet模型
初始化DiffusersUNetWrapper，输入通道数：4
尝试从/root/ComfyUI/models/lbm/normals加载UNet模型
Some weights of the model checkpoint at /root/ComfyUI/models/lbm/normals were not used when initializing UNet2DModel: 
 ['encoder.down_blocks.3.resnets.1.conv2.weight, encoder.down_blocks.3.resnets.1.norm1.bias, encoder.down_blocks.2.resnets.1.conv1.weight, encoder.down_blocks.2.resnets.0.conv_shortcut.weight, decoder.up_blocks.0.resnets.0.norm2.bias, decoder.conv_in.weight, decoder.up_blocks.0.resnets.1.norm2.bias, decoder.up_blocks.3.resnets.1.norm2.bias, decoder.up_blocks.0.resnets.1.norm1.bias, encoder.mid_block.attentions.0.to_k.bias, encoder.down_blocks.0.resnets.0.norm2.weight, decoder.up_blocks.3.resnets.1.conv1.bias, encoder.down_blocks.1.resnets.0.conv1.bias, decoder.up_blocks.2.resnets.2.conv1.weight, decoder.up_blocks.0.resnets.1.conv1.bias, decoder.up_blocks.0.resnets.1.conv2.bias, decoder.up_blocks.2.resnets.1.norm2.weight, decoder.mid_block.attentions.0.to_k.weight, encoder.down_blocks.0.resnets.1.norm1.weight, encoder.mid_block.attentions.0.to_out.0.weight, decoder.up_blocks.2.resnets.1.conv1.weight, encoder.mid_block.resnets.0.conv1.weight, decoder.up_blocks.1.resnets.2.norm1.weight, decoder.up_blocks.2.resnets.1.conv2.bias, encoder.down_blocks.1.resnets.1.norm1.bias, decoder.up_blocks.3.resnets.1.conv2.bias, decoder.mid_block.attentions.0.to_out.0.bias, decoder.up_blocks.0.resnets.0.conv2.weight, decoder.mid_block.attentions.0.to_v.weight, encoder.down_blocks.3.resnets.1.conv2.bias, encoder.down_blocks.1.resnets.1.norm1.weight, decoder.up_blocks.1.resnets.0.conv1.weight, decoder.up_blocks.2.resnets.2.conv1.bias, encoder.down_blocks.2.resnets.0.conv1.bias, decoder.up_blocks.2.resnets.1.conv1.bias, decoder.up_blocks.3.resnets.0.conv1.weight, encoder.down_blocks.2.resnets.1.norm2.weight, encoder.down_blocks.3.resnets.0.norm2.bias, decoder.mid_block.resnets.0.norm2.bias, encoder.mid_block.resnets.0.norm1.bias, decoder.mid_block.resnets.1.norm1.weight, quant_conv.bias, encoder.down_blocks.2.resnets.0.norm2.bias, decoder.up_blocks.3.resnets.2.norm2.weight, encoder.down_blocks.3.resnets.1.norm2.weight, encoder.down_blocks.2.downsamplers.0.conv.bias, decoder.up_blocks.1.resnets.1.conv1.weight, decoder.up_blocks.2.resnets.0.conv_shortcut.bias, decoder.up_blocks.0.resnets.1.conv1.weight, decoder.up_blocks.2.upsamplers.0.conv.bias, encoder.conv_norm_out.weight, encoder.down_blocks.1.downsamplers.0.conv.weight, encoder.down_blocks.1.resnets.0.norm2.weight, decoder.up_blocks.3.resnets.1.conv1.weight, decoder.up_blocks.2.resnets.0.norm1.weight, decoder.up_blocks.3.resnets.0.norm2.weight, encoder.down_blocks.0.resnets.0.norm1.bias, encoder.down_blocks.2.resnets.1.conv1.bias, decoder.mid_block.resnets.1.norm2.bias, decoder.up_blocks.2.resnets.1.conv2.weight, decoder.up_blocks.2.resnets.2.norm2.bias, decoder.up_blocks.0.upsamplers.0.conv.bias, decoder.up_blocks.1.resnets.1.conv2.bias, encoder.mid_block.resnets.1.norm2.weight, encoder.down_blocks.0.resnets.1.conv1.weight, encoder.mid_block.resnets.0.conv2.weight, decoder.up_blocks.1.resnets.1.norm1.weight, decoder.up_blocks.1.resnets.1.norm2.weight, decoder.up_blocks.2.resnets.0.conv1.bias, encoder.down_blocks.2.resnets.0.conv1.weight, quant_conv.weight, encoder.mid_block.resnets.1.norm1.weight, decoder.up_blocks.3.resnets.2.norm1.weight, decoder.up_blocks.2.resnets.0.norm2.weight, decoder.up_blocks.3.resnets.0.norm1.weight, decoder.mid_block.resnets.1.conv2.weight, decoder.up_blocks.1.resnets.2.conv2.bias, decoder.mid_block.attentions.0.to_v.bias, encoder.down_blocks.1.resnets.0.conv2.bias, decoder.up_blocks.1.upsamplers.0.conv.bias, decoder.up_blocks.1.resnets.0.conv1.bias, decoder.mid_block.resnets.0.norm1.weight, decoder.up_blocks.3.resnets.0.conv_shortcut.weight, encoder.down_blocks.0.downsamplers.0.conv.bias, encoder.down_blocks.0.resnets.1.conv2.weight, post_quant_conv.bias, encoder.mid_block.attentions.0.group_norm.bias, decoder.mid_block.resnets.1.norm1.bias, decoder.up_blocks.3.resnets.2.conv2.weight, decoder.up_blocks.2.resnets.0.norm1.bias, decoder.up_blocks.2.resnets.1.norm1.weight, encoder.down_blocks.1.resnets.1.conv1.bias, encoder.down_blocks.0.resnets.0.conv2.weight, decoder.up_blocks.0.resnets.2.norm2.weight, encoder.mid_block.attentions.0.to_k.weight, encoder.down_blocks.0.resnets.0.norm1.weight, encoder.mid_block.resnets.0.conv1.bias, decoder.conv_in.bias, decoder.up_blocks.3.resnets.0.norm2.bias, encoder.conv_out.bias, encoder.down_blocks.0.resnets.1.norm2.bias, decoder.up_blocks.0.resnets.1.conv2.weight, decoder.up_blocks.1.resnets.0.conv2.bias, decoder.up_blocks.1.resnets.1.norm2.bias, decoder.up_blocks.0.resnets.0.conv1.bias, decoder.up_blocks.0.resnets.0.norm1.bias, decoder.up_blocks.2.resnets.2.conv2.weight, encoder.mid_block.resnets.1.conv1.bias, decoder.conv_out.bias, decoder.up_blocks.1.resnets.2.conv1.bias, encoder.down_blocks.3.resnets.0.norm1.bias, decoder.up_blocks.0.resnets.0.norm1.weight, decoder.up_blocks.3.resnets.0.conv1.bias, decoder.up_blocks.2.upsamplers.0.conv.weight, decoder.up_blocks.0.resnets.2.norm1.weight, encoder.down_blocks.3.resnets.0.norm1.weight, decoder.mid_block.attentions.0.group_norm.bias, encoder.down_blocks.1.resnets.0.conv2.weight, decoder.up_blocks.1.resnets.0.norm1.weight, encoder.down_blocks.2.resnets.0.norm2.weight, decoder.mid_block.attentions.0.to_k.bias, decoder.mid_block.resnets.0.norm2.weight, encoder.down_blocks.0.resnets.0.conv1.weight, decoder.up_blocks.0.resnets.0.norm2.weight, decoder.up_blocks.0.resnets.2.norm1.bias, decoder.mid_block.resnets.1.conv1.weight, decoder.mid_block.resnets.0.conv2.bias, encoder.down_blocks.3.resnets.1.norm1.weight, encoder.mid_block.attentions.0.group_norm.weight, encoder.down_blocks.2.resnets.0.conv_shortcut.bias, decoder.mid_block.attentions.0.to_q.bias, decoder.up_blocks.1.resnets.0.norm2.weight, decoder.up_blocks.3.resnets.1.norm1.weight, encoder.down_blocks.1.resnets.0.conv_shortcut.weight, decoder.mid_block.resnets.1.norm2.weight, encoder.down_blocks.0.resnets.0.conv2.bias, encoder.down_blocks.1.resnets.1.conv2.bias, encoder.conv_in.bias, decoder.mid_block.attentions.0.to_out.0.weight, decoder.up_blocks.0.resnets.2.norm2.bias, decoder.up_blocks.0.upsamplers.0.conv.weight, decoder.up_blocks.1.resnets.1.conv2.weight, decoder.up_blocks.2.resnets.0.norm2.bias, encoder.down_blocks.1.resnets.0.conv_shortcut.bias, decoder.up_blocks.1.resnets.1.norm1.bias, encoder.down_blocks.2.resnets.0.conv2.weight, encoder.down_blocks.0.downsamplers.0.conv.weight, decoder.mid_block.resnets.0.norm1.bias, encoder.down_blocks.2.resnets.0.norm1.bias, decoder.up_blocks.3.resnets.1.norm2.weight, decoder.mid_block.attentions.0.group_norm.weight, encoder.conv_out.weight, encoder.mid_block.resnets.0.conv2.bias, encoder.mid_block.resnets.0.norm2.weight, encoder.down_blocks.0.resnets.1.conv2.bias, encoder.down_blocks.3.resnets.0.conv2.bias, decoder.conv_out.weight, decoder.mid_block.resnets.0.conv1.weight, encoder.down_blocks.3.resnets.0.conv1.weight, encoder.down_blocks.2.resnets.1.norm1.weight, encoder.mid_block.resnets.1.norm1.bias, decoder.mid_block.resnets.1.conv1.bias, decoder.up_blocks.3.resnets.0.conv_shortcut.bias, decoder.up_blocks.3.resnets.1.norm1.bias, encoder.down_blocks.1.resnets.1.conv2.weight, encoder.down_blocks.3.resnets.0.norm2.weight, decoder.conv_norm_out.bias, decoder.up_blocks.1.upsamplers.0.conv.weight, encoder.mid_block.resnets.1.conv2.weight, decoder.up_blocks.0.resnets.0.conv2.bias, encoder.down_blocks.1.resnets.1.norm2.weight, decoder.mid_block.resnets.1.conv2.bias, decoder.up_blocks.0.resnets.0.conv1.weight, encoder.down_blocks.0.resnets.0.norm2.bias, encoder.down_blocks.2.resnets.1.norm2.bias, decoder.up_blocks.0.resnets.2.conv2.weight, decoder.up_blocks.0.resnets.1.norm1.weight, encoder.down_blocks.2.downsamplers.0.conv.weight, decoder.up_blocks.2.resnets.0.conv_shortcut.weight, decoder.up_blocks.0.resnets.2.conv1.bias, decoder.up_blocks.1.resnets.0.norm2.bias, decoder.up_blocks.1.resnets.0.conv2.weight, encoder.down_blocks.2.resnets.0.norm1.weight, decoder.up_blocks.1.resnets.2.norm2.bias, decoder.up_blocks.3.resnets.1.conv2.weight, encoder.down_blocks.0.resnets.1.norm2.weight, encoder.down_blocks.0.resnets.1.conv1.bias, decoder.up_blocks.2.resnets.0.conv1.weight, encoder.down_blocks.3.resnets.1.conv1.bias, decoder.up_blocks.2.resnets.2.norm2.weight, decoder.up_blocks.1.resnets.2.norm2.weight, encoder.down_blocks.2.resnets.0.conv2.bias, decoder.up_blocks.1.resnets.1.conv1.bias, decoder.conv_norm_out.weight, decoder.up_blocks.2.resnets.2.conv2.bias, decoder.up_blocks.3.resnets.0.conv2.weight, encoder.down_blocks.2.resnets.1.conv2.weight, encoder.mid_block.attentions.0.to_out.0.bias, encoder.conv_norm_out.bias, decoder.up_blocks.3.resnets.0.conv2.bias, decoder.up_blocks.3.resnets.2.conv1.bias, encoder.down_blocks.1.resnets.0.norm1.bias, decoder.up_blocks.3.resnets.2.norm2.bias, encoder.mid_block.attentions.0.to_q.weight, decoder.up_blocks.1.resnets.0.norm1.bias, encoder.down_blocks.1.resnets.1.conv1.weight, decoder.up_blocks.2.resnets.2.norm1.weight, decoder.mid_block.resnets.0.conv2.weight, decoder.up_blocks.3.resnets.2.norm1.bias, encoder.down_blocks.3.resnets.0.conv2.weight, decoder.up_blocks.1.resnets.2.norm1.bias, decoder.up_blocks.2.resnets.0.conv2.bias, encoder.down_blocks.1.downsamplers.0.conv.bias, encoder.down_blocks.1.resnets.0.conv1.weight, decoder.up_blocks.0.resnets.2.conv1.weight, decoder.up_blocks.3.resnets.2.conv2.bias, encoder.mid_block.resnets.1.norm2.bias, encoder.down_blocks.1.resnets.1.norm2.bias, decoder.up_blocks.0.resnets.2.conv2.bias, encoder.mid_block.resnets.1.conv1.weight, decoder.mid_block.attentions.0.to_q.weight, decoder.mid_block.resnets.0.conv1.bias, decoder.up_blocks.2.resnets.1.norm2.bias, encoder.mid_block.attentions.0.to_q.bias, encoder.mid_block.attentions.0.to_v.bias, post_quant_conv.weight, decoder.up_blocks.3.resnets.2.conv1.weight, decoder.up_blocks.2.resnets.1.norm1.bias, encoder.down_blocks.3.resnets.1.conv1.weight, encoder.mid_block.resnets.1.conv2.bias, encoder.conv_in.weight, decoder.up_blocks.3.resnets.0.norm1.bias, encoder.down_blocks.3.resnets.1.norm2.bias, decoder.up_blocks.0.resnets.1.norm2.weight, encoder.down_blocks.2.resnets.1.conv2.bias, encoder.mid_block.attentions.0.to_v.weight, encoder.mid_block.resnets.0.norm1.weight, encoder.down_blocks.3.resnets.0.conv1.bias, decoder.up_blocks.2.resnets.2.norm1.bias, encoder.down_blocks.1.resnets.0.norm2.bias, encoder.mid_block.resnets.0.norm2.bias, encoder.down_blocks.2.resnets.1.norm1.bias, decoder.up_blocks.1.resnets.2.conv2.weight, decoder.up_blocks.1.resnets.2.conv1.weight, decoder.up_blocks.2.resnets.0.conv2.weight, encoder.down_blocks.0.resnets.1.norm1.bias, encoder.down_blocks.0.resnets.0.conv1.bias, encoder.down_blocks.1.resnets.0.norm1.weight']
Some weights of UNet2DModel were not initialized from the model checkpoint at /root/ComfyUI/models/lbm/normals and are newly initialized: ['up_blocks.3.resnets.2.norm2.weight', 'up_blocks.1.resnets.1.norm2.weight', 'down_blocks.1.resnets.0.norm2.bias', 'up_blocks.1.resnets.0.conv_shortcut.weight', 'up_blocks.0.resnets.0.conv1.weight', 'mid_block.resnets.0.norm1.weight', 'up_blocks.0.resnets.1.conv2.bias', 'mid_block.resnets.0.norm1.bias', 'mid_block.attentions.0.to_q.bias', 'down_blocks.2.resnets.0.conv1.bias', 'up_blocks.3.resnets.2.time_emb_proj.weight', 'conv_out.weight', 'up_blocks.3.resnets.1.norm1.weight', 'mid_block.resnets.1.conv2.bias', 'up_blocks.2.resnets.0.norm2.bias', 'down_blocks.1.resnets.1.norm2.weight', 'down_blocks.2.resnets.1.norm1.bias', 'down_blocks.0.resnets.1.norm2.bias', 'up_blocks.0.resnets.2.conv2.bias', 'down_blocks.0.resnets.0.norm2.bias', 'down_blocks.3.resnets.0.norm2.bias', 'conv_norm_out.weight', 'up_blocks.2.resnets.2.norm1.bias', 'down_blocks.0.resnets.1.conv1.weight', 'down_blocks.0.resnets.1.conv2.weight', 'up_blocks.1.upsamplers.0.conv.weight', 'down_blocks.1.resnets.0.conv2.bias', 'up_blocks.2.resnets.0.conv2.bias', 'time_embedding.linear_1.bias', 'up_blocks.2.resnets.0.conv1.weight', 'up_blocks.2.resnets.2.conv2.bias', 'down_blocks.0.resnets.1.norm1.bias', 'up_blocks.1.resnets.1.conv1.bias', 'up_blocks.0.resnets.0.norm1.bias', 'mid_block.resnets.0.time_emb_proj.weight', 'up_blocks.2.resnets.1.conv1.weight', 'down_blocks.3.resnets.1.norm2.weight', 'mid_block.resnets.0.conv1.weight', 'down_blocks.1.resnets.0.conv1.bias', 'up_blocks.0.resnets.2.norm2.weight', 'time_embedding.linear_2.bias', 'up_blocks.1.upsamplers.0.conv.bias', 'down_blocks.0.resnets.1.conv2.bias', 'down_blocks.3.resnets.1.conv2.bias', 'up_blocks.1.resnets.2.time_emb_proj.weight', 'mid_block.resnets.1.norm1.bias', 'up_blocks.0.resnets.1.norm2.bias', 'down_blocks.2.resnets.1.conv2.weight', 'up_blocks.2.resnets.0.conv_shortcut.weight', 'up_blocks.3.resnets.1.norm2.weight', 'up_blocks.0.resnets.1.norm1.weight', 'up_blocks.3.resnets.0.conv2.bias', 'mid_block.resnets.1.conv1.weight', 'up_blocks.0.resnets.0.norm1.weight', 'up_blocks.2.resnets.0.conv_shortcut.bias', 'up_blocks.2.resnets.1.conv2.bias', 'up_blocks.3.resnets.2.time_emb_proj.bias', 'up_blocks.0.resnets.0.time_emb_proj.bias', 'up_blocks.0.resnets.1.conv1.weight', 'up_blocks.3.resnets.1.conv2.bias', 'up_blocks.1.resnets.0.time_emb_proj.bias', 'up_blocks.1.resnets.0.conv_shortcut.bias', 'mid_block.attentions.0.to_q.weight', 'up_blocks.2.resnets.2.conv2.weight', 'down_blocks.2.resnets.1.norm1.weight', 'up_blocks.1.resnets.0.conv1.weight', 'up_blocks.3.resnets.0.norm2.weight', 'mid_block.resnets.0.conv1.bias', 'down_blocks.3.resnets.1.norm1.weight', 'down_blocks.0.downsamplers.0.conv.weight', 'up_blocks.1.resnets.1.conv1.weight', 'mid_block.attentions.0.to_out.0.bias', 'up_blocks.1.resnets.1.norm2.bias', 'up_blocks.2.resnets.2.norm1.weight', 'up_blocks.2.resnets.2.conv1.weight', 'up_blocks.1.resnets.2.conv2.bias', 'down_blocks.0.resnets.1.norm2.weight', 'down_blocks.2.resnets.0.norm2.bias', 'up_blocks.3.resnets.0.conv2.weight', 'up_blocks.3.resnets.1.time_emb_proj.bias', 'time_embedding.linear_2.weight', 'down_blocks.1.resnets.0.conv1.weight', 'down_blocks.1.resnets.1.conv2.weight', 'down_blocks.1.resnets.1.conv2.bias', 'up_blocks.1.resnets.0.conv1.bias', 'up_blocks.2.resnets.1.time_emb_proj.weight', 'mid_block.resnets.1.conv2.weight', 'down_blocks.0.resnets.1.conv1.bias', 'down_blocks.2.resnets.1.conv1.bias', 'up_blocks.1.resnets.1.time_emb_proj.bias', 'mid_block.resnets.0.time_emb_proj.bias', 'up_blocks.1.resnets.1.conv2.weight', 'down_blocks.2.resnets.0.norm1.weight', 'down_blocks.2.resnets.0.conv1.weight', 'down_blocks.1.resnets.1.conv1.bias', 'down_blocks.2.resnets.0.conv2.bias', 'up_blocks.1.resnets.2.conv1.weight', 'down_blocks.3.resnets.0.conv2.bias', 'mid_block.resnets.1.time_emb_proj.weight', 'down_blocks.1.resnets.0.conv2.weight', 'down_blocks.1.downsamplers.0.conv.weight', 'up_blocks.1.resnets.2.time_emb_proj.bias', 'mid_block.attentions.0.group_norm.weight', 'mid_block.attentions.0.to_v.bias', 'up_blocks.3.resnets.1.conv1.bias', 'down_blocks.3.resnets.0.norm1.bias', 'up_blocks.0.resnets.2.time_emb_proj.weight', 'down_blocks.2.resnets.1.norm2.weight', 'down_blocks.1.resnets.1.conv1.weight', 'up_blocks.2.resnets.1.conv1.bias', 'down_blocks.3.resnets.1.norm1.bias', 'up_blocks.3.resnets.0.conv1.bias', 'down_blocks.1.resnets.0.norm1.bias', 'down_blocks.2.resnets.0.conv2.weight', 'up_blocks.0.resnets.2.norm2.bias', 'up_blocks.3.resnets.2.conv1.weight', 'up_blocks.2.resnets.2.time_emb_proj.weight', 'down_blocks.1.resnets.1.norm1.bias', 'up_blocks.0.resnets.0.conv1.bias', 'up_blocks.1.resnets.1.norm1.bias', 'down_blocks.3.resnets.0.norm2.weight', 'up_blocks.2.resnets.0.time_emb_proj.weight', 'up_blocks.3.resnets.0.norm1.bias', 'mid_block.resnets.1.norm2.weight', 'up_blocks.3.resnets.0.time_emb_proj.bias', 'mid_block.resnets.1.norm2.bias', 'up_blocks.2.resnets.1.norm1.weight', 'up_blocks.1.resnets.2.norm2.weight', 'up_blocks.0.resnets.2.conv1.weight', 'up_blocks.3.resnets.1.norm1.bias', 'down_blocks.0.resnets.0.norm2.weight', 'up_blocks.3.resnets.1.conv2.weight', 'mid_block.resnets.0.norm2.bias', 'up_blocks.2.resnets.0.conv1.bias', 'time_embedding.linear_1.weight', 'down_blocks.2.resnets.1.norm2.bias', 'conv_in.bias', 'up_blocks.3.resnets.2.norm1.bias', 'down_blocks.1.resnets.0.norm2.weight', 'up_blocks.1.resnets.2.norm1.bias', 'up_blocks.2.resnets.0.norm1.bias', 'up_blocks.1.resnets.0.conv2.weight', 'mid_block.attentions.0.to_k.bias', 'down_blocks.0.resnets.0.conv2.bias', 'up_blocks.0.resnets.2.conv2.weight', 'up_blocks.1.resnets.0.norm1.bias', 'mid_block.attentions.0.to_out.0.weight', 'mid_block.resnets.0.conv2.weight', 'down_blocks.3.resnets.0.conv1.bias', 'down_blocks.3.resnets.1.conv1.bias', 'up_blocks.1.resnets.2.conv2.weight', 'up_blocks.2.resnets.2.norm2.bias', 'up_blocks.3.resnets.1.conv1.weight', 'conv_in.weight', 'up_blocks.3.resnets.1.time_emb_proj.weight', 'up_blocks.0.resnets.1.time_emb_proj.weight', 'conv_norm_out.bias', 'up_blocks.1.resnets.1.time_emb_proj.weight', 'up_blocks.3.resnets.2.norm2.bias', 'mid_block.attentions.0.to_k.weight', 'up_blocks.2.resnets.1.norm2.weight', 'up_blocks.0.resnets.1.conv2.weight', 'up_blocks.2.resnets.1.norm1.bias', 'up_blocks.1.resnets.0.norm1.weight', 'down_blocks.2.resnets.1.conv1.weight', 'up_blocks.0.resnets.0.time_emb_proj.weight', 'up_blocks.0.resnets.1.conv1.bias', 'up_blocks.2.resnets.1.norm2.bias', 'up_blocks.3.resnets.0.norm1.weight', 'mid_block.resnets.1.time_emb_proj.bias', 'up_blocks.3.resnets.2.conv2.weight', 'down_blocks.3.resnets.0.norm1.weight', 'up_blocks.0.upsamplers.0.conv.bias', 'up_blocks.0.resnets.0.conv2.weight', 'down_blocks.2.resnets.0.norm2.weight', 'up_blocks.0.resnets.0.conv2.bias', 'down_blocks.2.resnets.1.conv2.bias', 'up_blocks.1.resnets.0.conv2.bias', 'down_blocks.2.resnets.0.conv_shortcut.bias', 'up_blocks.1.resnets.2.norm1.weight', 'up_blocks.1.resnets.2.norm2.bias', 'up_blocks.3.resnets.0.conv1.weight', 'up_blocks.3.resnets.2.conv2.bias', 'down_blocks.3.resnets.1.conv1.weight', 'down_blocks.3.resnets.1.norm2.bias', 'up_blocks.3.resnets.2.conv1.bias', 'mid_block.attentions.0.to_v.weight', 'down_blocks.1.resnets.0.conv_shortcut.weight', 'up_blocks.2.resnets.0.norm2.weight', 'down_blocks.1.resnets.1.norm1.weight', 'conv_out.bias', 'down_blocks.0.resnets.0.conv1.weight', 'up_blocks.1.resnets.0.norm2.weight', 'up_blocks.0.upsamplers.0.conv.weight', 'down_blocks.0.resnets.0.conv1.bias', 'down_blocks.3.resnets.1.conv2.weight', 'up_blocks.1.resnets.0.norm2.bias', 'up_blocks.2.resnets.0.time_emb_proj.bias', 'mid_block.attentions.0.group_norm.bias', 'down_blocks.1.resnets.0.norm1.weight', 'up_blocks.3.resnets.0.time_emb_proj.weight', 'up_blocks.0.resnets.0.norm2.bias', 'up_blocks.1.resnets.2.conv1.bias', 'up_blocks.2.resnets.1.conv2.weight', 'down_blocks.0.resnets.1.norm1.weight', 'mid_block.resnets.1.norm1.weight', 'mid_block.resnets.0.conv2.bias', 'down_blocks.1.resnets.1.norm2.bias', 'up_blocks.0.resnets.0.norm2.weight', 'up_blocks.3.resnets.0.norm2.bias', 'down_blocks.0.downsamplers.0.conv.bias', 'up_blocks.3.resnets.1.norm2.bias', 'up_blocks.0.resnets.1.norm2.weight', 'up_blocks.2.resnets.2.norm2.weight', 'up_blocks.1.resnets.1.norm1.weight', 'up_blocks.3.resnets.2.norm1.weight', 'down_blocks.1.downsamplers.0.conv.bias', 'down_blocks.2.downsamplers.0.conv.weight', 'down_blocks.0.resnets.0.conv2.weight', 'up_blocks.0.resnets.1.time_emb_proj.bias', 'down_blocks.2.resnets.0.norm1.bias', 'down_blocks.0.resnets.0.norm1.weight', 'down_blocks.0.resnets.0.norm1.bias', 'up_blocks.0.resnets.2.conv1.bias', 'up_blocks.0.resnets.2.time_emb_proj.bias', 'up_blocks.2.resnets.1.time_emb_proj.bias', 'up_blocks.0.resnets.2.norm1.bias', 'down_blocks.3.resnets.0.conv1.weight', 'down_blocks.3.resnets.0.conv2.weight', 'up_blocks.2.upsamplers.0.conv.bias', 'down_blocks.2.resnets.0.conv_shortcut.weight', 'up_blocks.2.resnets.2.conv1.bias', 'up_blocks.0.resnets.2.norm1.weight', 'mid_block.resnets.0.norm2.weight', 'down_blocks.1.resnets.0.conv_shortcut.bias', 'up_blocks.1.resnets.0.time_emb_proj.weight', 'up_blocks.2.resnets.0.conv2.weight', 'up_blocks.2.resnets.0.norm1.weight', 'up_blocks.1.resnets.1.conv2.bias', 'up_blocks.2.upsamplers.0.conv.weight', 'mid_block.resnets.1.conv1.bias', 'down_blocks.2.downsamplers.0.conv.bias', 'up_blocks.0.resnets.1.norm1.bias', 'up_blocks.2.resnets.2.time_emb_proj.bias']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
成功加载UNet模型
成功直接加载预训练UNet模型
预训练UNet模型加载完成: 244/244 键 (100.00%)
创建PNDM调度器
LBM模型创建成功
LBM法线模型加载成功
LBM法线生成模型加载成功
开始生成法线图，采样步数: 16
将输入图像从float32转换为float16以匹配模型
使用VAE编码输入图像
FETCH ComfyRegistry Data: 35/86
FETCH ComfyRegistry Data: 40/86
开始采样过程，步数: 16
DiffusersUNetWrapper前向传播错误: Tensor on device meta is not on the expected device cpu!
采样步骤 1/25
DiffusersUNetWrapper前向传播错误: Tensor on device meta is not on the expected device cpu!
采样步骤 2/25
DiffusersUNetWrapper前向传播错误: Tensor on device meta is not on the expected device cpu!
采样步骤 3/25
DiffusersUNetWrapper前向传播错误: Tensor on device meta is not on the expected device cpu!
采样步骤 4/25
DiffusersUNetWrapper前向传播错误: Tensor on device meta is not on the expected device cpu!
采样步骤 5/25
DiffusersUNetWrapper前向传播错误: Tensor on device meta is not on the expected device cpu!
采样步骤 6/25
DiffusersUNetWrapper前向传播错误: Tensor on device meta is not on the expected device cpu!
采样步骤 7/25
DiffusersUNetWrapper前向传播错误: Tensor on device meta is not on the expected device cpu!
采样步骤 8/25
DiffusersUNetWrapper前向传播错误: Tensor on device meta is not on the expected device cpu!
采样步骤 9/25
DiffusersUNetWrapper前向传播错误: Tensor on device meta is not on the expected device cpu!
采样步骤 10/25
DiffusersUNetWrapper前向传播错误: Tensor on device meta is not on the expected device cpu!
采样步骤 11/25
DiffusersUNetWrapper前向传播错误: Tensor on device meta is not on the expected device cpu!
采样步骤 12/25
DiffusersUNetWrapper前向传播错误: Tensor on device meta is not on the expected device cpu!
采样步骤 13/25
DiffusersUNetWrapper前向传播错误: Tensor on device meta is not on the expected device cpu!
采样步骤 14/25
DiffusersUNetWrapper前向传播错误: Tensor on device meta is not on the expected device cpu!
采样步骤 15/25
DiffusersUNetWrapper前向传播错误: Tensor on device meta is not on the expected device cpu!
采样步骤 16/25
DiffusersUNetWrapper前向传播错误: Tensor on device meta is not on the expected device cpu!
采样步骤 17/25
DiffusersUNetWrapper前向传播错误: Tensor on device meta is not on the expected device cpu!
采样步骤 18/25
DiffusersUNetWrapper前向传播错误: Tensor on device meta is not on the expected device cpu!
采样步骤 19/25
DiffusersUNetWrapper前向传播错误: Tensor on device meta is not on the expected device cpu!
采样步骤 20/25
DiffusersUNetWrapper前向传播错误: Tensor on device meta is not on the expected device cpu!
采样步骤 21/25
DiffusersUNetWrapper前向传播错误: Tensor on device meta is not on the expected device cpu!
采样步骤 22/25
DiffusersUNetWrapper前向传播错误: Tensor on device meta is not on the expected device cpu!
采样步骤 23/25
DiffusersUNetWrapper前向传播错误: Tensor on device meta is not on the expected device cpu!
采样步骤 24/25
DiffusersUNetWrapper前向传播错误: Tensor on device meta is not on the expected device cpu!
采样步骤 25/25