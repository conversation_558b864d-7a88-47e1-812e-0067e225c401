name: Bug Report
description: "Something is broken inside of ComfyUI. (Do not use this if you're just having issues and need help, or if the issue relates to a custom node)"
labels: ["Potential Bug"]
body:
  - type: markdown
    attributes:
      value: |
        Before submitting a **Bug Report**, please ensure the following:

        - **1:** You are running the latest version of ComfyUI.
        - **2:** You have looked at the existing bug reports and made sure this isn't already reported.
        - **3:** You confirmed that the bug is not caused by a custom node. You can disable all custom nodes by passing
        `--disable-all-custom-nodes` command line argument.
        - **4:** This is an actual bug in ComfyUI, not just a support question. A bug is when you can specify exact
        steps to replicate what went wrong and others will be able to repeat your steps and see the same issue happen.

        If unsure, ask on the [ComfyUI Matrix Space](https://app.element.io/#/room/%23comfyui_space%3Amatrix.org) or the [Comfy Org Discord](https://discord.gg/comfyorg) first.
  - type: textarea
    attributes:
      label: Expected Behavior
      description: "What you expected to happen."
    validations:
      required: true
  - type: textarea
    attributes:
      label: Actual Behavior
      description: "What actually happened. Please include a screenshot of the issue if possible."
    validations:
      required: true
  - type: textarea
    attributes:
      label: Steps to Reproduce
      description: "Describe how to reproduce the issue. Please be sure to attach a workflow JSON or PNG, ideally one that doesn't require custom nodes to test. If the bug open happens when certain custom nodes are used, most likely that custom node is what has the bug rather than ComfyUI, in which case it should be reported to the node's author."
    validations:
      required: true
  - type: textarea
    attributes:
      label: Debug Logs
      description: "Please copy the output from your terminal logs here."
      render: powershell
    validations:
      required: true
  - type: textarea
    attributes:
      label: Other
      description: "Any other additional information you think might be helpful."
    validations:
      required: false
