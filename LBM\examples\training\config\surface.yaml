# wandb
wandb_project: lbm-surface-flows
timestep_sampling: custom_timesteps
unet_input_channels: 4
vae_num_channels: 4
selected_timesteps: [250, 500, 750, 1000]
prob: [0.25, 0.25, 0.25, 0.25]
pixel_loss_type: lpips # l1 l2
pixel_loss_weight: 10.0
latent_loss_type: l2 # l1 l2
latent_loss_weight: 1.0
bridge_noise_sigma: 0.005
conditioning_images_keys: []
conditioning_masks_keys: []

# SHARDS_PATH_OR_URLS
train_shards:
  - pipe:cat PATH_TO_TRAIN_TARS

validation_shards:
  - pipe:cat PATH_TO_VAL_TARS

batch_size: 4
learning_rate: 4e-5
optimizer: AdamW
num_steps: [1, 4]
log_interval: 500
resume_from_checkpoint: true
max_epochs: 50
save_interval: 5000
save_ckpt_path: ./checkpoints
