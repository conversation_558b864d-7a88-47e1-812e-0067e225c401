class FluxStyleAdjust:
    @classmethod
    def INPUT_TYPES(s):
        return {"required": {
            "clip_vision": ("CLIP_VISION",),
            "image": ("IMAGE",),
            "style_grid_size": ("INT", {"default": 27, "min": 1, "max": 27, "step": 1}),
            "style_strength": ("FLOAT", {"default": 1.0, "min": 0.0, "max": 10.0, "step": 0.1}),
            "mask": ("MASK", {"default": None}),
            }}
    RETURN_TYPES = ("STYLE_MODEL",)
    FUNCTION = "apply_style"
    CATEGORY = "conditioning/style_model"

    def apply_style(self, clip_vision, image, style_grid_size=27, style_strength=1.0, mask=None):
        # 处理图像尺寸
        output = clip_vision.encode_image(image)
        
        # 获取特征图尺寸
        feature_map = output.image_embeds
        B, H, W, C = feature_map.shape
        
        # 根据style_grid_size调整特征图尺寸
        if style_grid_size < H:
            # 使用自适应平均池化调整特征图大小
            pool = torch.nn.AdaptiveAvgPool2d((style_grid_size, style_grid_size))
            feature_map = feature_map.permute(0, 3, 1, 2)  # B,C,H,W
            feature_map = pool(feature_map)
            feature_map = feature_map.permute(0, 2, 3, 1)  # B,H,W,C
        
        # 应用mask
        if mask is not None:
            # 调整mask尺寸以匹配特征图
            mask = torch.nn.functional.interpolate(mask.unsqueeze(1), size=(style_grid_size, style_grid_size), mode='bilinear')
            mask = mask.squeeze(1)
            
            # 扩展mask维度以匹配特征图
            mask = mask.unsqueeze(-1).expand(-1, -1, -1, C)
            
            # 应用mask到特征图
            feature_map = feature_map * mask
        
        # 应用style_strength
        feature_map = feature_map * style_strength
        
        # 更新output中的特征图
        output.image_embeds = feature_map
        
        return (output,) 