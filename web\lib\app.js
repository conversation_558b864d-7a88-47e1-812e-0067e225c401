// ComfyUI 前端核心注册节点的方式
app.registerExtension({
    name: "Canvas",
    async setup(app) {
        // 注册自定义节点
        LiteGraph.registerNodeType(
            "CanvasNode",
            CanvasNodeClass
        );

        // 确保样式文件加载
        document.head.appendChild(
            Object.assign(document.createElement("link"), {
                rel: "stylesheet",
                href: "./custom_nodes/comfyui-canvas/web/css/canvas.css"
            })
        );
    }
}); 