{"data": {"automata": {"type": "automata", "properties": {"cache": false}, "context": {}, "initial": "intro", "blocks": {"intro": {"type": "state", "name": "Intro", "properties": {}, "render": {"text": "## 👏👏👏Welcome to experience Return To Infancy👏👏\n👶🏼 Button one“Return”\n     Here you can convert your photos to Return to infancy. I'm sure that's very cute! ", "buttons": [{"content": "👶🏼 Return", "on_click": {"event": "d7d9b3d7-6266-43f5-a8cd-62d5541dc54b", "payload": {}}, "id": "d7d9b3d7-6266-43f5-a8cd-62d5541dc54b", "description": ""}], "image": "https://www.myshellstatic.com/image/chat/embed_obj/202505291724/e2c31cd268330b01f3d977453fd68b55_raw.webp"}, "blocks": [], "inputs": {}, "outputs": {}, "transitions": {"d7d9b3d7-6266-43f5-a8cd-62d5541dc54b": {"name": "Untitled", "target": "form1", "target_inputs": {}}}}, "form1": {"type": "form", "name": "Form#1", "properties": {}, "render": {}, "inputs": {"upload": {"name": "Upload", "type": "image", "user_input": true, "object_properties": {}, "array_items": {"type": "text"}, "default_value": "", "description": "", "validations": [], "value": ""}}, "transitions": {"ALWAYS": {"name": "Untitled", "target": "state1", "target_inputs": {}}}, "blocks": []}, "state1": {"type": "state", "name": "State#1", "properties": {}, "render": {"text": "<h2>💥Boom! Like it? </h2>\n✅Save & share in the gallery, or try a New Template !", "image": "{{comfy_ui_widget1.output_image}}", "buttons": [{"content": "Pick A New Pic", "on_click": {"event": "21e99a5b-7b57-480d-8a46-4dc5b59ccf21", "payload": {}}, "id": "21e99a5b-7b57-480d-8a46-4dc5b59ccf21", "description": ""}, {"content": "Regenerate", "on_click": {"event": "3cecc426-ff29-44d1-9dbf-3fae8881c645", "payload": {}}, "id": "3cecc426-ff29-44d1-9dbf-3fae8881c645", "description": ""}]}, "blocks": [{"inputs": {"prompt": "【上传图片】人物可爱的婴儿形象, 保持面部准确", "image_url": "{{upload}}"}, "outputs": {"display": {"file_urls": "Array<string>"}}, "type": "task", "display_name": "GPT Image 1#1", "name": "gpt_image_11", "mode": "widget", "widget_name": "@myshell/1912154065390264321", "widget_class_name": "MyShellAnyWidgetCallerWidget"}, {"api": "https://bfbza4t8wp9hkoz6-8188.container.x-gpu.com/ ", "location": null, "inputs": {"45": "{{gpt_image_11.file_urls[0]}}", "46": "{{upload}}"}, "outputs": {"display": {"output_image": "string"}}, "type": "task", "display_name": "ComfyUI Widget#1", "name": "comfy_ui_widget1", "mode": "comfy_workflow", "widget_name": "ComfyUIWidget", "widget_class_name": "ComfyUIWidget", "comfy_workflow_id": "720c465570584dc489704b4767fefd8a"}], "inputs": {"upload": {"name": "Upload", "type": "image", "object_properties": {}, "array_items": {"type": "text"}, "value": "", "description": "", "validations": []}}, "outputs": {}, "transitions": {"21e99a5b-7b57-480d-8a46-4dc5b59ccf21": {"name": "Untitled", "target": "form1", "target_inputs": {}}, "3cecc426-ff29-44d1-9dbf-3fae8881c645": {"name": "Untitled", "target": "form1_copy1", "target_inputs": {}}}}, "state1_copy1": {"type": "state", "name": "State", "properties": {}, "render": {"text": "<h2>💥Boom! Like it? </h2>\n✅Save & share in the gallery, or try a New Template !", "image": "{{comfy_ui_widget1.output_image}}", "buttons": [{"content": "Pick A New Pic", "on_click": {"event": "c71ad3c8-5b55-4fee-89db-55c3c04bfec9", "payload": {}}, "id": "c71ad3c8-5b55-4fee-89db-55c3c04bfec9", "description": ""}, {"content": "Regenerate", "on_click": {"event": "be3c5d87-d002-4a0f-b553-8232a7fac8b0", "payload": {}}, "id": "be3c5d87-d002-4a0f-b553-8232a7fac8b0", "description": ""}, {"content": "Back To Home", "on_click": {"event": "70bb2814-1f3f-4139-b132-bd5615142af2", "payload": {}}, "id": "70bb2814-1f3f-4139-b132-bd5615142af2", "description": ""}]}, "blocks": [{"inputs": {"prompt": "【上传图片】人物可爱的婴儿形象, 保持面部准确", "image_url": ""}, "outputs": {"display": {"file_urls": "Array<string>"}}, "type": "task", "display_name": "GPT Image 1#1", "name": "gpt_image_11", "mode": "widget", "widget_name": "@myshell/1912154065390264321", "widget_class_name": "MyShellAnyWidgetCallerWidget"}, {"api": "https://bfbza4t8wp9hkoz6-8188.container.x-gpu.com/", "location": null, "inputs": {"45": "{{gpt_image_11.file_urls[0]}}", "46": "{{upload}}"}, "outputs": {"display": {"output_image": "string"}}, "type": "task", "display_name": "ComfyUI Widget#1", "name": "comfy_ui_widget1", "mode": "comfy_workflow", "widget_name": "ComfyUIWidget", "widget_class_name": "ComfyUIWidget", "comfy_workflow_id": "a3f2a594b3a749339e9749880a0e0743"}], "inputs": {"upload": {"name": "Upload", "type": "image", "object_properties": {}, "array_items": {"type": "text"}, "value": "", "description": "", "validations": []}}, "outputs": {}, "transitions": {"c71ad3c8-5b55-4fee-89db-55c3c04bfec9": {"name": "Untitled", "target": "form1", "target_inputs": {}}, "be3c5d87-d002-4a0f-b553-8232a7fac8b0": {"name": "Untitled", "target": "form1_copy1", "target_inputs": {}}, "70bb2814-1f3f-4139-b132-bd5615142af2": {"name": "Untitled", "target": "intro", "target_inputs": {}}}}, "form1_copy1": {"type": "form", "name": "Form", "properties": {}, "render": {}, "inputs": {"upload": {"name": "Upload", "type": "image", "user_input": false, "object_properties": {}, "array_items": {"type": "text"}, "default_value": "", "description": "", "validations": [], "value": ""}}, "transitions": {"ALWAYS": {"name": "Untitled", "target": "state1_copy1", "target_inputs": {}}}, "blocks": []}}, "transitions": {}}, "workflows": {}, "comfyui_workflows": {"720c465570584dc489704b4767fefd8a": {"workflow": {"id": "b3c6f1fb-6d5e-494d-a6eb-eff4a1020dac", "revision": 0, "last_node_id": 49, "last_link_id": 84, "nodes": [{"id": 42, "type": "YCMaskComposite", "pos": [1290, 1200], "size": [315, 174], "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "destination", "type": "MASK", "link": 70}, {"name": "source", "type": "MASK", "link": 71}], "outputs": [{"name": "mask", "type": "MASK", "links": [75]}], "properties": {"aux_id": "yichengup/ComfyUI-YCNodes", "ver": "67c378c43fb91916a0b48caf7cc2923b4e1b5b88", "Node name for S&R": "YCMaskComposite", "cnr_id": "ComfyUI-YCNodes"}, "widgets_values": [0, 0, "natural_blend", 1, 1]}, {"id": 40, "type": "IrregularToEllipseMask", "pos": [910, 1140], "size": [315, 202], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 68}], "outputs": [{"name": "ellipse_mask", "type": "MASK", "links": [71]}], "properties": {"aux_id": "yichengup/ComfyUI-YCNodes", "ver": "115fc0ec3e28c5cd6c805edc2f7e09b875780b88", "Node name for S&R": "IrregularToEllipseMask", "cnr_id": "ComfyUI-YCNodes"}, "widgets_values": ["largest", "enable", "enable", 3, "convex_hull", 30, 50]}, {"id": 41, "type": "IrregularToEllipseMask", "pos": [910, 1410], "size": [315, 202], "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 69}], "outputs": [{"name": "ellipse_mask", "type": "MASK", "links": [67]}], "properties": {"aux_id": "yichengup/ComfyUI-YCNodes", "ver": "115fc0ec3e28c5cd6c805edc2f7e09b875780b88", "Node name for S&R": "IrregularToEllipseMask", "cnr_id": "ComfyUI-YCNodes"}, "widgets_values": ["largest", "enable", "enable", 3, "convex_hull", 0, 20]}, {"id": 39, "type": "YCRemapMaskRange", "pos": [920, 1700], "size": [315, 82], "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 67}], "outputs": [{"name": "mask", "type": "MASK", "links": [70]}], "properties": {"aux_id": "yichengup/ComfyUI-YCNodes", "ver": "3ba5ce3390fbc52024c5d14f2d5fce42056a6cca", "Node name for S&R": "YCRemapMaskRange"}, "widgets_values": [0, 0.06000000000000001]}, {"id": 3, "type": "HyperLoRALoader", "pos": [1270, 140], "size": [358.7608947753906, 84.72722625732422], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "config", "type": "HYPER_LORA_CONFIG", "link": 1}], "outputs": [{"name": "HYPER_LORA", "type": "HYPER_LORA", "links": [2, 7, 9]}], "properties": {"aux_id": "bytedance/ComfyUI-HyperLoRA", "ver": "feb11bedcb5f965085dd8e52862dadd9fe456df9", "Node name for S&R": "HyperLoRALoader"}, "widgets_values": ["sdxl_hyper_id_lora_v1_edit", "fp16"]}, {"id": 8, "type": "HyperLoRAGenerateIDLoRA", "pos": [1680, 140], "size": [249.1568145751953, 46], "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "hyper_lora", "type": "HYPER_LORA", "link": 9}, {"name": "id_cond", "type": "ID_COND", "link": 8}], "outputs": [{"name": "LORA", "type": "LORA", "links": [10]}], "properties": {"aux_id": "bytedance/ComfyUI-HyperLoRA", "ver": "feb11bedcb5f965085dd8e52862dadd9fe456df9", "Node name for S&R": "HyperLoRAGenerateIDLoRA"}, "widgets_values": []}, {"id": 7, "type": "HyperLoRAIDCond", "pos": [1690, 260], "size": [249.3211669921875, 122.28309631347656], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "hyper_lora", "type": "HYPER_LORA", "link": 7}, {"name": "images", "type": "IMAGE", "link": 83}, {"name": "face_attr", "type": "FACE_ATTR", "link": 5}], "outputs": [{"name": "ID_COND", "type": "ID_COND", "links": [8]}, {"name": "IMAGE", "type": "IMAGE", "links": null}], "properties": {"aux_id": "bytedance/ComfyUI-HyperLoRA", "ver": "feb11bedcb5f965085dd8e52862dadd9fe456df9", "Node name for S&R": "HyperLoRAIDCond"}, "widgets_values": [false, true]}, {"id": 4, "type": "HyperLoRAFaceAttr", "pos": [1340, 300], "size": [202.2093048095703, 46], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "hyper_lora", "type": "HYPER_LORA", "link": 2}, {"name": "images", "type": "IMAGE", "link": 82}], "outputs": [{"name": "FACE_ATTR", "type": "FACE_ATTR", "links": [5]}], "properties": {"aux_id": "bytedance/ComfyUI-HyperLoRA", "ver": "feb11bedcb5f965085dd8e52862dadd9fe456df9", "Node name for S&R": "HyperLoRAFaceAttr"}, "widgets_values": []}, {"id": 9, "type": "HyperLoRAApplyLoRA", "pos": [1194.29052734375, 605.4680786132812], "size": [242.75352478027344, 78], "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 11}, {"name": "lora", "type": "LORA", "link": 10}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [41, 66]}], "properties": {"aux_id": "bytedance/ComfyUI-HyperLoRA", "ver": "feb11bedcb5f965085dd8e52862dadd9fe456df9", "Node name for S&R": "HyperLoRAApplyLoRA"}, "widgets_values": [0.52]}, {"id": 23, "type": "DifferentialDiffusion", "pos": [1473.38623046875, 605.9352416992188], "size": [211.40646362304688, 62.403324127197266], "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 41}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [42]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "DifferentialDiffusion"}, "widgets_values": []}, {"id": 15, "type": "CLIPSetLastLayer", "pos": [910.9378662109375, 625.6146240234375], "size": [243.56906127929688, 61.31538772583008], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 21}], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [27, 28]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "CLIPSetLastLayer"}, "widgets_values": [-2]}, {"id": 36, "type": "CLIPTextEncode", "pos": [464.042724609375, 805.3428955078125], "size": [395.23272705078125, 88], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 62}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [64]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["fcsks fxhks fhyks,girl baby,bust portrait,wear dress,white background"]}, {"id": 37, "type": "CLIPTextEncode", "pos": [459.2410888671875, 964.5931396484375], "size": [378.5472717285156, 88], "flags": {"collapsed": false}, "order": 7, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 63}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [65]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["Blurred, messy, turbid, lowest resolution, worst quality, least details, extremely poor image quality, distorted structure, messy structure, broken image,Fat, excess fat"]}, {"id": 19, "type": "CLIPTextEncode", "pos": [896.0573120117188, 966.6331787109375], "size": [398.8081970214844, 89.16095733642578], "flags": {"collapsed": false}, "order": 11, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 28}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [32]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["Blurred, messy, turbid, lowest resolution, worst quality, least details, extremely poor image quality, distorted structure, messy structure, broken image,Fat, excess fat"]}, {"id": 20, "type": "InpaintModelConditioning", "pos": [1375.4140625, 806.4811401367188], "size": [315, 138], "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 31}, {"name": "negative", "type": "CONDITIONING", "link": 32}, {"name": "vae", "type": "VAE", "link": 37}, {"name": "pixels", "type": "IMAGE", "link": 80}, {"name": "mask", "type": "MASK", "link": 75}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [33]}, {"name": "negative", "type": "CONDITIONING", "links": [34]}, {"name": "latent", "type": "LATENT", "links": [40]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [true]}, {"id": 11, "type": "K<PERSON><PERSON><PERSON>", "pos": [1741.142578125, 706.003662109375], "size": [315, 262], "flags": {}, "order": 21, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 42}, {"name": "positive", "type": "CONDITIONING", "link": 33}, {"name": "negative", "type": "CONDITIONING", "link": 34}, {"name": "latent_image", "type": "LATENT", "link": 40}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [22]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [693709576827051, "fixed", 25, 5, "dpmpp_2m", "beta", 0.7500000000000001]}, {"id": 16, "type": "VAEDecode", "pos": [2118.36376953125, 603.369873046875], "size": [206.59979248046875, 69.72660064697266], "flags": {}, "order": 22, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 22}, {"name": "vae", "type": "VAE", "link": 23}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [52]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 33, "type": "VAEEncode", "pos": [2114.95751953125, 767.8564453125], "size": [210, 46], "flags": {}, "order": 23, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 52}, {"name": "vae", "type": "VAE", "link": 60}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [53]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "VAEEncode"}, "widgets_values": []}, {"id": 32, "type": "K<PERSON><PERSON><PERSON>", "pos": [2377.598388671875, 688.5667114257812], "size": [315, 262], "flags": {}, "order": 24, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 66}, {"name": "positive", "type": "CONDITIONING", "link": 64}, {"name": "negative", "type": "CONDITIONING", "link": 65}, {"name": "latent_image", "type": "LATENT", "link": 53}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [57]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [693709576827052, "fixed", 20, 7, "euler", "sgm_uniform", 0.25000000000000006]}, {"id": 38, "type": "easy humanSegmentation", "pos": [510, 1150], "size": [300, 500], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 81}], "outputs": [{"name": "image", "type": "IMAGE", "links": []}, {"name": "mask", "type": "MASK", "links": [68, 69]}, {"name": "bbox", "type": "BBOX", "links": null}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "0daf114fe8870aeacfea484aa59e7f9973b91cd5", "Node name for S&R": "easy humanSegmentation", "values": [1]}, "widgets_values": ["human_parts (deeplabv3p)", 0.4, 0, "1"]}, {"id": 34, "type": "VAEDecode", "pos": [2752.6640625, 621.0095825195312], "size": [249.32998657226562, 79.37089538574219], "flags": {}, "order": 25, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 57}, {"name": "vae", "type": "VAE", "link": 58}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [84]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 2, "type": "HyperLoRAConfig", "pos": [860, 130], "size": [370.171875, 370], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "HYPER_LORA_CONFIG", "type": "HYPER_LORA_CONFIG", "links": [1]}], "properties": {"aux_id": "bytedance/ComfyUI-HyperLoRA", "ver": "feb11bedcb5f965085dd8e52862dadd9fe456df9", "Node name for S&R": "HyperLoRAConfig"}, "widgets_values": ["clip_vit_large_14_processor", "clip_vit_large_14", 1024, 64, 12, 4, 4, "clip + arcface", "antelopev2", 512, 16, 128, 8, false]}, {"id": 1, "type": "CheckpointLoaderSimple", "pos": [457.5687255859375, 627.37451171875], "size": [366.26171875, 98], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [11]}, {"name": "CLIP", "type": "CLIP", "links": [21, 62, 63]}, {"name": "VAE", "type": "VAE", "links": [23, 37, 58, 60]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["realvisxlV50_v50Bakedvae.safetensors"]}, {"id": 47, "type": "ShellAgentPluginSaveImage", "pos": [1710.6878662109375, 2209.6298828125], "size": [510.41094970703125, 379.59686279296875], "flags": {}, "order": 26, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 84}], "outputs": [], "properties": {"cnr_id": "ComfyUI-ShellAgent-Plugin", "ver": "d27fafbb003badd19246b05049dd0caca483a2a1", "Node name for S&R": "ShellAgentPluginSaveImage"}, "widgets_values": ["output_image", "ComfyUI", ""]}, {"id": 18, "type": "CLIPTextEncode", "pos": [892.8939208984375, 802.1796875], "size": [408.34271240234375, 90.35277557373047], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 27}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [31]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["fcsks fxhks fhyks, baby,bust portrait,"]}, {"id": 46, "type": "ShellAgentPluginInputImage", "pos": [549.7325439453125, 2204.956298828125], "size": [405.8222961425781, 374.5360107421875], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [82, 83]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "ComfyUI-ShellAgent-Plugin", "ver": "d27fafbb003badd19246b05049dd0caca483a2a1", "Node name for S&R": "ShellAgentPluginInputImage"}, "widgets_values": ["user_image", "2<PERSON><PERSON><PERSON>.jpeg", "image", "", ""]}, {"id": 45, "type": "ShellAgentPluginInputImage", "pos": [1120.1146240234375, 2215.21240234375], "size": [390.5999755859375, 362], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [80, 81]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "ComfyUI-ShellAgent-Plugin", "ver": "d27fafbb003badd19246b05049dd0caca483a2a1", "Node name for S&R": "ShellAgentPluginInputImage"}, "widgets_values": ["face_image", "Myshell_ShellAgent App Builder Chat_250529_214203.jpeg", "image", "", ""]}], "links": [[1, 2, 0, 3, 0, "HYPER_LORA_CONFIG"], [2, 3, 0, 4, 0, "HYPER_LORA"], [5, 4, 0, 7, 2, "FACE_ATTR"], [7, 3, 0, 7, 0, "HYPER_LORA"], [8, 7, 0, 8, 1, "ID_COND"], [9, 3, 0, 8, 0, "HYPER_LORA"], [10, 8, 0, 9, 1, "LORA"], [11, 1, 0, 9, 0, "MODEL"], [21, 1, 1, 15, 0, "CLIP"], [22, 11, 0, 16, 0, "LATENT"], [23, 1, 2, 16, 1, "VAE"], [27, 15, 0, 18, 0, "CLIP"], [28, 15, 0, 19, 0, "CLIP"], [31, 18, 0, 20, 0, "CONDITIONING"], [32, 19, 0, 20, 1, "CONDITIONING"], [33, 20, 0, 11, 1, "CONDITIONING"], [34, 20, 1, 11, 2, "CONDITIONING"], [37, 1, 2, 20, 2, "VAE"], [40, 20, 2, 11, 3, "LATENT"], [41, 9, 0, 23, 0, "MODEL"], [42, 23, 0, 11, 0, "MODEL"], [52, 16, 0, 33, 0, "IMAGE"], [53, 33, 0, 32, 3, "LATENT"], [57, 32, 0, 34, 0, "LATENT"], [58, 1, 2, 34, 1, "VAE"], [60, 1, 2, 33, 1, "VAE"], [62, 1, 1, 36, 0, "CLIP"], [63, 1, 1, 37, 0, "CLIP"], [64, 36, 0, 32, 1, "CONDITIONING"], [65, 37, 0, 32, 2, "CONDITIONING"], [66, 9, 0, 32, 0, "MODEL"], [67, 41, 0, 39, 0, "MASK"], [68, 38, 1, 40, 0, "MASK"], [69, 38, 1, 41, 0, "MASK"], [70, 39, 0, 42, 0, "MASK"], [71, 40, 0, 42, 1, "MASK"], [75, 42, 0, 20, 4, "MASK"], [80, 45, 0, 20, 3, "IMAGE"], [81, 45, 0, 38, 0, "IMAGE"], [82, 46, 0, 4, 1, "IMAGE"], [83, 46, 0, 7, 1, "IMAGE"], [84, 34, 0, 47, 0, "IMAGE"]], "groups": [{"id": 1, "title": "用户脸", "bounding": [591.9435424804688, 2042.62158203125, 280.6344909667969, 80], "color": "#3f789e", "font_size": 50, "flags": {}}, {"id": 2, "title": "婴儿图", "bounding": [1183.0845947265625, 2035.469970703125, 280.6344909667969, 80], "color": "#3f789e", "font_size": 50, "flags": {}}, {"id": 3, "title": "输出", "bounding": [1799.254638671875, 2048.580078125, 280.6344909667969, 80], "color": "#3f789e", "font_size": 50, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.6830134553650705, "offset": [-1323.4940339154223, -1982.4247242071374]}, "frontendVersion": "1.16.9", "VHS_latentpreview": false, "VHS_latentpreviewrate": 0}, "version": 0.4}, "workflow_api": {"1": {"inputs": {"ckpt_name": "realvisxlV50_v50Bakedvae.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Checkpoint加载器（简易）"}}, "2": {"inputs": {"image_processor": "clip_vit_large_14_processor", "image_encoder": "clip_vit_large_14", "resampler.dim": 1024, "resampler.dim_head": 64, "resampler.heads": 12, "resampler.depth": 4, "resampler.ff_mult": 4, "encoder_types": "clip + arcface", "face_analyzer": "antelopev2", "id_embed_dim": 512, "num_id_tokens": 16, "hyper_dim": 128, "lora_rank": 8, "has_base_lora": false}, "class_type": "HyperLoRAConfig", "_meta": {"title": "HyperLoRA Config"}}, "3": {"inputs": {"model": "sdxl_hyper_id_lora_v1_edit", "dtype": "fp16", "config": ["2", 0]}, "class_type": "HyperLoRALoader", "_meta": {"title": "HyperLoRA Loader"}}, "4": {"inputs": {"hyper_lora": ["3", 0], "images": ["46", 0]}, "class_type": "HyperLoRAFaceAttr", "_meta": {"title": "HyperLoRA Face Attr"}}, "7": {"inputs": {"grayscale": false, "remove_background": true, "hyper_lora": ["3", 0], "images": ["46", 0], "face_attr": ["4", 0]}, "class_type": "HyperLoRAIDCond", "_meta": {"title": "HyperLoRA ID Cond"}}, "8": {"inputs": {"hyper_lora": ["3", 0], "id_cond": ["7", 0]}, "class_type": "HyperLoRAGenerateIDLoRA", "_meta": {"title": "HyperLoRA Generate ID LoRA"}}, "9": {"inputs": {"weight": 0.52, "model": ["1", 0], "lora": ["8", 0]}, "class_type": "HyperLoRAApplyLoRA", "_meta": {"title": "HyperLoRA Apply LoRA"}}, "11": {"inputs": {"seed": 693709576827051, "steps": 25, "cfg": 5, "sampler_name": "dpmpp_2m", "scheduler": "beta", "denoise": 0.7500000000000001, "model": ["23", 0], "positive": ["20", 0], "negative": ["20", 1], "latent_image": ["20", 2]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K采样器"}}, "15": {"inputs": {"stop_at_clip_layer": -2, "clip": ["1", 1]}, "class_type": "CLIPSetLastLayer", "_meta": {"title": "设置CLIP最后一层"}}, "16": {"inputs": {"samples": ["11", 0], "vae": ["1", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE解码"}}, "18": {"inputs": {"text": "fcsks fxhks fhyks, baby,bust portrait,", "clip": ["15", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码"}}, "19": {"inputs": {"text": "Blurred, messy, turbid, lowest resolution, worst quality, least details, extremely poor image quality, distorted structure, messy structure, broken image,Fat, excess fat", "clip": ["15", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码"}}, "20": {"inputs": {"noise_mask": true, "positive": ["18", 0], "negative": ["19", 0], "vae": ["1", 2], "pixels": ["45", 0], "mask": ["42", 0]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "内补模型条件"}}, "23": {"inputs": {"model": ["9", 0]}, "class_type": "DifferentialDiffusion", "_meta": {"title": "差异扩散DifferentialDiffusion"}}, "32": {"inputs": {"seed": 693709576827052, "steps": 20, "cfg": 7, "sampler_name": "euler", "scheduler": "sgm_uniform", "denoise": 0.25000000000000006, "model": ["9", 0], "positive": ["36", 0], "negative": ["37", 0], "latent_image": ["33", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K采样器"}}, "33": {"inputs": {"pixels": ["16", 0], "vae": ["1", 2]}, "class_type": "VAEEncode", "_meta": {"title": "VAE编码"}}, "34": {"inputs": {"samples": ["32", 0], "vae": ["1", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE解码"}}, "36": {"inputs": {"text": "fcsks fxhks fhyks,girl baby,bust portrait,wear dress,white background", "clip": ["1", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码"}}, "37": {"inputs": {"text": "Blurred, messy, turbid, lowest resolution, worst quality, least details, extremely poor image quality, distorted structure, messy structure, broken image,Fat, excess fat", "clip": ["1", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码"}}, "38": {"inputs": {"method": "human_parts (deeplabv3p)", "confidence": 0.4, "crop_multi": 0, "mask_components": "1", "image": ["45", 0]}, "class_type": "easy humanSegmentation", "_meta": {"title": "人体Segmentation"}}, "39": {"inputs": {"min": 0, "max": 0.06000000000000001, "mask": ["41", 0]}, "class_type": "YCRemapMaskRange", "_meta": {"title": "Remap Mask Range (YC)"}}, "40": {"inputs": {"keep_region": "largest", "fill_holes": "enable", "smooth_edges": "enable", "smoothing_kernel_size": 3, "output_mode": "convex_hull", "expand_mask": 30, "blur_amount": 50, "mask": ["38", 1]}, "class_type": "IrregularToEllipseMask", "_meta": {"title": "Irregular To EllipseMask"}}, "41": {"inputs": {"keep_region": "largest", "fill_holes": "enable", "smooth_edges": "enable", "smoothing_kernel_size": 3, "output_mode": "convex_hull", "expand_mask": 0, "blur_amount": 20, "mask": ["38", 1]}, "class_type": "IrregularToEllipseMask", "_meta": {"title": "Irregular To EllipseMask"}}, "42": {"inputs": {"x": 0, "y": 0, "operation": "natural_blend", "opacity": 1, "blend_power": 1, "destination": ["39", 0], "source": ["40", 0]}, "class_type": "YCMaskComposite", "_meta": {"title": "Mask Composite (YC)"}}, "45": {"inputs": {"input_name": "face_image", "default_value": "Myshell_ShellAgent App Builder Chat_250529_214203.jpeg", "description": ""}, "class_type": "ShellAgentPluginInputImage", "_meta": {"title": "Input Image (ShellAgent Plugin)"}}, "46": {"inputs": {"input_name": "user_image", "default_value": "2<PERSON><PERSON><PERSON>.jpeg", "description": ""}, "class_type": "ShellAgentPluginInputImage", "_meta": {"title": "Input Image (ShellAgent Plugin)"}}, "47": {"inputs": {"output_name": "output_image", "filename_prefix": "ComfyUI", "images": ["34", 0]}, "class_type": "ShellAgentPluginSaveImage", "_meta": {"title": "Save Image (ShellAgent Plugin)"}}}, "schemas": {"inputs": {"45": {"title": "face_image", "type": "string", "default": "Myshell_ShellAgent App Builder Chat_250529_214203.jpeg", "description": "", "url_type": "image"}, "46": {"title": "user_image", "type": "string", "default": "2<PERSON><PERSON><PERSON>.jpeg", "description": "", "url_type": "image"}}, "outputs": {"47": {"title": "output_image", "type": "string", "url_type": "image"}}}}, "a3f2a594b3a749339e9749880a0e0743": {"workflow": {"id": "b3c6f1fb-6d5e-494d-a6eb-eff4a1020dac", "revision": 0, "last_node_id": 49, "last_link_id": 84, "nodes": [{"id": 42, "type": "YCMaskComposite", "pos": [1290, 1200], "size": [315, 174], "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "destination", "type": "MASK", "link": 70}, {"name": "source", "type": "MASK", "link": 71}], "outputs": [{"name": "mask", "type": "MASK", "links": [75]}], "properties": {"aux_id": "yichengup/ComfyUI-YCNodes", "ver": "67c378c43fb91916a0b48caf7cc2923b4e1b5b88", "Node name for S&R": "YCMaskComposite", "cnr_id": "ComfyUI-YCNodes"}, "widgets_values": [0, 0, "natural_blend", 1, 1]}, {"id": 40, "type": "IrregularToEllipseMask", "pos": [910, 1140], "size": [315, 202], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 68}], "outputs": [{"name": "ellipse_mask", "type": "MASK", "links": [71]}], "properties": {"aux_id": "yichengup/ComfyUI-YCNodes", "ver": "115fc0ec3e28c5cd6c805edc2f7e09b875780b88", "Node name for S&R": "IrregularToEllipseMask", "cnr_id": "ComfyUI-YCNodes"}, "widgets_values": ["largest", "enable", "enable", 3, "convex_hull", 30, 50]}, {"id": 41, "type": "IrregularToEllipseMask", "pos": [910, 1410], "size": [315, 202], "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 69}], "outputs": [{"name": "ellipse_mask", "type": "MASK", "links": [67]}], "properties": {"aux_id": "yichengup/ComfyUI-YCNodes", "ver": "115fc0ec3e28c5cd6c805edc2f7e09b875780b88", "Node name for S&R": "IrregularToEllipseMask", "cnr_id": "ComfyUI-YCNodes"}, "widgets_values": ["largest", "enable", "enable", 3, "convex_hull", 0, 20]}, {"id": 39, "type": "YCRemapMaskRange", "pos": [920, 1700], "size": [315, 82], "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 67}], "outputs": [{"name": "mask", "type": "MASK", "links": [70]}], "properties": {"aux_id": "yichengup/ComfyUI-YCNodes", "ver": "3ba5ce3390fbc52024c5d14f2d5fce42056a6cca", "Node name for S&R": "YCRemapMaskRange"}, "widgets_values": [0, 0.06000000000000001]}, {"id": 3, "type": "HyperLoRALoader", "pos": [1270, 140], "size": [358.7608947753906, 84.72722625732422], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "config", "type": "HYPER_LORA_CONFIG", "link": 1}], "outputs": [{"name": "HYPER_LORA", "type": "HYPER_LORA", "links": [2, 7, 9]}], "properties": {"aux_id": "bytedance/ComfyUI-HyperLoRA", "ver": "feb11bedcb5f965085dd8e52862dadd9fe456df9", "Node name for S&R": "HyperLoRALoader"}, "widgets_values": ["sdxl_hyper_id_lora_v1_edit", "fp16"]}, {"id": 8, "type": "HyperLoRAGenerateIDLoRA", "pos": [1680, 140], "size": [249.1568145751953, 46], "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "hyper_lora", "type": "HYPER_LORA", "link": 9}, {"name": "id_cond", "type": "ID_COND", "link": 8}], "outputs": [{"name": "LORA", "type": "LORA", "links": [10]}], "properties": {"aux_id": "bytedance/ComfyUI-HyperLoRA", "ver": "feb11bedcb5f965085dd8e52862dadd9fe456df9", "Node name for S&R": "HyperLoRAGenerateIDLoRA"}, "widgets_values": []}, {"id": 7, "type": "HyperLoRAIDCond", "pos": [1690, 260], "size": [249.3211669921875, 122.28309631347656], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "hyper_lora", "type": "HYPER_LORA", "link": 7}, {"name": "images", "type": "IMAGE", "link": 83}, {"name": "face_attr", "type": "FACE_ATTR", "link": 5}], "outputs": [{"name": "ID_COND", "type": "ID_COND", "links": [8]}, {"name": "IMAGE", "type": "IMAGE", "links": null}], "properties": {"aux_id": "bytedance/ComfyUI-HyperLoRA", "ver": "feb11bedcb5f965085dd8e52862dadd9fe456df9", "Node name for S&R": "HyperLoRAIDCond"}, "widgets_values": [false, true]}, {"id": 4, "type": "HyperLoRAFaceAttr", "pos": [1340, 300], "size": [202.2093048095703, 46], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "hyper_lora", "type": "HYPER_LORA", "link": 2}, {"name": "images", "type": "IMAGE", "link": 82}], "outputs": [{"name": "FACE_ATTR", "type": "FACE_ATTR", "links": [5]}], "properties": {"aux_id": "bytedance/ComfyUI-HyperLoRA", "ver": "feb11bedcb5f965085dd8e52862dadd9fe456df9", "Node name for S&R": "HyperLoRAFaceAttr"}, "widgets_values": []}, {"id": 9, "type": "HyperLoRAApplyLoRA", "pos": [1194.29052734375, 605.4680786132812], "size": [242.75352478027344, 78], "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 11}, {"name": "lora", "type": "LORA", "link": 10}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [41, 66]}], "properties": {"aux_id": "bytedance/ComfyUI-HyperLoRA", "ver": "feb11bedcb5f965085dd8e52862dadd9fe456df9", "Node name for S&R": "HyperLoRAApplyLoRA"}, "widgets_values": [0.52]}, {"id": 23, "type": "DifferentialDiffusion", "pos": [1473.38623046875, 605.9352416992188], "size": [211.40646362304688, 62.403324127197266], "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 41}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [42]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "DifferentialDiffusion"}, "widgets_values": []}, {"id": 15, "type": "CLIPSetLastLayer", "pos": [910.9378662109375, 625.6146240234375], "size": [243.56906127929688, 61.31538772583008], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 21}], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [27, 28]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "CLIPSetLastLayer"}, "widgets_values": [-2]}, {"id": 36, "type": "CLIPTextEncode", "pos": [464.042724609375, 805.3428955078125], "size": [395.23272705078125, 88], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 62}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [64]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["fcsks fxhks fhyks,girl baby,bust portrait,wear dress,white background"]}, {"id": 37, "type": "CLIPTextEncode", "pos": [459.2410888671875, 964.5931396484375], "size": [378.5472717285156, 88], "flags": {"collapsed": false}, "order": 7, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 63}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [65]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["Blurred, messy, turbid, lowest resolution, worst quality, least details, extremely poor image quality, distorted structure, messy structure, broken image,Fat, excess fat"]}, {"id": 19, "type": "CLIPTextEncode", "pos": [896.0573120117188, 966.6331787109375], "size": [398.8081970214844, 89.16095733642578], "flags": {"collapsed": false}, "order": 11, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 28}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [32]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["Blurred, messy, turbid, lowest resolution, worst quality, least details, extremely poor image quality, distorted structure, messy structure, broken image,Fat, excess fat"]}, {"id": 20, "type": "InpaintModelConditioning", "pos": [1375.4140625, 806.4811401367188], "size": [315, 138], "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 31}, {"name": "negative", "type": "CONDITIONING", "link": 32}, {"name": "vae", "type": "VAE", "link": 37}, {"name": "pixels", "type": "IMAGE", "link": 80}, {"name": "mask", "type": "MASK", "link": 75}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [33]}, {"name": "negative", "type": "CONDITIONING", "links": [34]}, {"name": "latent", "type": "LATENT", "links": [40]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [true]}, {"id": 11, "type": "K<PERSON><PERSON><PERSON>", "pos": [1741.142578125, 706.003662109375], "size": [315, 262], "flags": {}, "order": 21, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 42}, {"name": "positive", "type": "CONDITIONING", "link": 33}, {"name": "negative", "type": "CONDITIONING", "link": 34}, {"name": "latent_image", "type": "LATENT", "link": 40}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [22]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [693709576827051, "fixed", 25, 5, "dpmpp_2m", "beta", 0.7500000000000001]}, {"id": 16, "type": "VAEDecode", "pos": [2118.36376953125, 603.369873046875], "size": [206.59979248046875, 69.72660064697266], "flags": {}, "order": 22, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 22}, {"name": "vae", "type": "VAE", "link": 23}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [52]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 33, "type": "VAEEncode", "pos": [2114.95751953125, 767.8564453125], "size": [210, 46], "flags": {}, "order": 23, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 52}, {"name": "vae", "type": "VAE", "link": 60}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [53]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "VAEEncode"}, "widgets_values": []}, {"id": 32, "type": "K<PERSON><PERSON><PERSON>", "pos": [2377.598388671875, 688.5667114257812], "size": [315, 262], "flags": {}, "order": 24, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 66}, {"name": "positive", "type": "CONDITIONING", "link": 64}, {"name": "negative", "type": "CONDITIONING", "link": 65}, {"name": "latent_image", "type": "LATENT", "link": 53}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [57]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [693709576827052, "fixed", 20, 7, "euler", "sgm_uniform", 0.25000000000000006]}, {"id": 38, "type": "easy humanSegmentation", "pos": [510, 1150], "size": [300, 500], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 81}], "outputs": [{"name": "image", "type": "IMAGE", "links": []}, {"name": "mask", "type": "MASK", "links": [68, 69]}, {"name": "bbox", "type": "BBOX", "links": null}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "0daf114fe8870aeacfea484aa59e7f9973b91cd5", "Node name for S&R": "easy humanSegmentation", "values": [1]}, "widgets_values": ["human_parts (deeplabv3p)", 0.4, 0, "1"]}, {"id": 34, "type": "VAEDecode", "pos": [2752.6640625, 621.0095825195312], "size": [249.32998657226562, 79.37089538574219], "flags": {}, "order": 25, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 57}, {"name": "vae", "type": "VAE", "link": 58}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [84]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 2, "type": "HyperLoRAConfig", "pos": [860, 130], "size": [370.171875, 370], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "HYPER_LORA_CONFIG", "type": "HYPER_LORA_CONFIG", "links": [1]}], "properties": {"aux_id": "bytedance/ComfyUI-HyperLoRA", "ver": "feb11bedcb5f965085dd8e52862dadd9fe456df9", "Node name for S&R": "HyperLoRAConfig"}, "widgets_values": ["clip_vit_large_14_processor", "clip_vit_large_14", 1024, 64, 12, 4, 4, "clip + arcface", "antelopev2", 512, 16, 128, 8, false]}, {"id": 1, "type": "CheckpointLoaderSimple", "pos": [457.5687255859375, 627.37451171875], "size": [366.26171875, 98], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [11]}, {"name": "CLIP", "type": "CLIP", "links": [21, 62, 63]}, {"name": "VAE", "type": "VAE", "links": [23, 37, 58, 60]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["realvisxlV50_v50Bakedvae.safetensors"]}, {"id": 47, "type": "ShellAgentPluginSaveImage", "pos": [1710.6878662109375, 2209.6298828125], "size": [510.41094970703125, 379.59686279296875], "flags": {}, "order": 26, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 84}], "outputs": [], "properties": {"cnr_id": "ComfyUI-ShellAgent-Plugin", "ver": "d27fafbb003badd19246b05049dd0caca483a2a1", "Node name for S&R": "ShellAgentPluginSaveImage"}, "widgets_values": ["output_image", "ComfyUI", ""]}, {"id": 18, "type": "CLIPTextEncode", "pos": [892.8939208984375, 802.1796875], "size": [408.34271240234375, 90.35277557373047], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 27}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [31]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["fcsks fxhks fhyks, baby,bust portrait,"]}, {"id": 46, "type": "ShellAgentPluginInputImage", "pos": [549.7325439453125, 2204.956298828125], "size": [405.8222961425781, 374.5360107421875], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [82, 83]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "ComfyUI-ShellAgent-Plugin", "ver": "d27fafbb003badd19246b05049dd0caca483a2a1", "Node name for S&R": "ShellAgentPluginInputImage"}, "widgets_values": ["user_image", "2<PERSON><PERSON><PERSON>.jpeg", "image", "", ""]}, {"id": 45, "type": "ShellAgentPluginInputImage", "pos": [1120.1146240234375, 2215.21240234375], "size": [390.5999755859375, 362], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [80, 81]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "ComfyUI-ShellAgent-Plugin", "ver": "d27fafbb003badd19246b05049dd0caca483a2a1", "Node name for S&R": "ShellAgentPluginInputImage"}, "widgets_values": ["face_image", "Myshell_ShellAgent App Builder Chat_250529_214203.jpeg", "image", "", ""]}], "links": [[1, 2, 0, 3, 0, "HYPER_LORA_CONFIG"], [2, 3, 0, 4, 0, "HYPER_LORA"], [5, 4, 0, 7, 2, "FACE_ATTR"], [7, 3, 0, 7, 0, "HYPER_LORA"], [8, 7, 0, 8, 1, "ID_COND"], [9, 3, 0, 8, 0, "HYPER_LORA"], [10, 8, 0, 9, 1, "LORA"], [11, 1, 0, 9, 0, "MODEL"], [21, 1, 1, 15, 0, "CLIP"], [22, 11, 0, 16, 0, "LATENT"], [23, 1, 2, 16, 1, "VAE"], [27, 15, 0, 18, 0, "CLIP"], [28, 15, 0, 19, 0, "CLIP"], [31, 18, 0, 20, 0, "CONDITIONING"], [32, 19, 0, 20, 1, "CONDITIONING"], [33, 20, 0, 11, 1, "CONDITIONING"], [34, 20, 1, 11, 2, "CONDITIONING"], [37, 1, 2, 20, 2, "VAE"], [40, 20, 2, 11, 3, "LATENT"], [41, 9, 0, 23, 0, "MODEL"], [42, 23, 0, 11, 0, "MODEL"], [52, 16, 0, 33, 0, "IMAGE"], [53, 33, 0, 32, 3, "LATENT"], [57, 32, 0, 34, 0, "LATENT"], [58, 1, 2, 34, 1, "VAE"], [60, 1, 2, 33, 1, "VAE"], [62, 1, 1, 36, 0, "CLIP"], [63, 1, 1, 37, 0, "CLIP"], [64, 36, 0, 32, 1, "CONDITIONING"], [65, 37, 0, 32, 2, "CONDITIONING"], [66, 9, 0, 32, 0, "MODEL"], [67, 41, 0, 39, 0, "MASK"], [68, 38, 1, 40, 0, "MASK"], [69, 38, 1, 41, 0, "MASK"], [70, 39, 0, 42, 0, "MASK"], [71, 40, 0, 42, 1, "MASK"], [75, 42, 0, 20, 4, "MASK"], [80, 45, 0, 20, 3, "IMAGE"], [81, 45, 0, 38, 0, "IMAGE"], [82, 46, 0, 4, 1, "IMAGE"], [83, 46, 0, 7, 1, "IMAGE"], [84, 34, 0, 47, 0, "IMAGE"]], "groups": [{"id": 1, "title": "用户脸", "bounding": [591.9435424804688, 2042.62158203125, 280.6344909667969, 80], "color": "#3f789e", "font_size": 50, "flags": {}}, {"id": 2, "title": "婴儿图", "bounding": [1183.0845947265625, 2035.469970703125, 280.6344909667969, 80], "color": "#3f789e", "font_size": 50, "flags": {}}, {"id": 3, "title": "输出", "bounding": [1799.254638671875, 2048.580078125, 280.6344909667969, 80], "color": "#3f789e", "font_size": 50, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.6830134553650705, "offset": [-1323.4940339154223, -1982.4247242071374]}, "frontendVersion": "1.16.9", "VHS_latentpreview": false, "VHS_latentpreviewrate": 0}, "version": 0.4}, "workflow_api": {"1": {"inputs": {"ckpt_name": "realvisxlV50_v50Bakedvae.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Checkpoint加载器（简易）"}}, "2": {"inputs": {"image_processor": "clip_vit_large_14_processor", "image_encoder": "clip_vit_large_14", "resampler.dim": 1024, "resampler.dim_head": 64, "resampler.heads": 12, "resampler.depth": 4, "resampler.ff_mult": 4, "encoder_types": "clip + arcface", "face_analyzer": "antelopev2", "id_embed_dim": 512, "num_id_tokens": 16, "hyper_dim": 128, "lora_rank": 8, "has_base_lora": false}, "class_type": "HyperLoRAConfig", "_meta": {"title": "HyperLoRA Config"}}, "3": {"inputs": {"model": "sdxl_hyper_id_lora_v1_edit", "dtype": "fp16", "config": ["2", 0]}, "class_type": "HyperLoRALoader", "_meta": {"title": "HyperLoRA Loader"}}, "4": {"inputs": {"hyper_lora": ["3", 0], "images": ["46", 0]}, "class_type": "HyperLoRAFaceAttr", "_meta": {"title": "HyperLoRA Face Attr"}}, "7": {"inputs": {"grayscale": false, "remove_background": true, "hyper_lora": ["3", 0], "images": ["46", 0], "face_attr": ["4", 0]}, "class_type": "HyperLoRAIDCond", "_meta": {"title": "HyperLoRA ID Cond"}}, "8": {"inputs": {"hyper_lora": ["3", 0], "id_cond": ["7", 0]}, "class_type": "HyperLoRAGenerateIDLoRA", "_meta": {"title": "HyperLoRA Generate ID LoRA"}}, "9": {"inputs": {"weight": 0.52, "model": ["1", 0], "lora": ["8", 0]}, "class_type": "HyperLoRAApplyLoRA", "_meta": {"title": "HyperLoRA Apply LoRA"}}, "11": {"inputs": {"seed": 693709576827051, "steps": 25, "cfg": 5, "sampler_name": "dpmpp_2m", "scheduler": "beta", "denoise": 0.7500000000000001, "model": ["23", 0], "positive": ["20", 0], "negative": ["20", 1], "latent_image": ["20", 2]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K采样器"}}, "15": {"inputs": {"stop_at_clip_layer": -2, "clip": ["1", 1]}, "class_type": "CLIPSetLastLayer", "_meta": {"title": "设置CLIP最后一层"}}, "16": {"inputs": {"samples": ["11", 0], "vae": ["1", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE解码"}}, "18": {"inputs": {"text": "fcsks fxhks fhyks, baby,bust portrait,", "clip": ["15", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码"}}, "19": {"inputs": {"text": "Blurred, messy, turbid, lowest resolution, worst quality, least details, extremely poor image quality, distorted structure, messy structure, broken image,Fat, excess fat", "clip": ["15", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码"}}, "20": {"inputs": {"noise_mask": true, "positive": ["18", 0], "negative": ["19", 0], "vae": ["1", 2], "pixels": ["45", 0], "mask": ["42", 0]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "内补模型条件"}}, "23": {"inputs": {"model": ["9", 0]}, "class_type": "DifferentialDiffusion", "_meta": {"title": "差异扩散DifferentialDiffusion"}}, "32": {"inputs": {"seed": 693709576827052, "steps": 20, "cfg": 7, "sampler_name": "euler", "scheduler": "sgm_uniform", "denoise": 0.25000000000000006, "model": ["9", 0], "positive": ["36", 0], "negative": ["37", 0], "latent_image": ["33", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K采样器"}}, "33": {"inputs": {"pixels": ["16", 0], "vae": ["1", 2]}, "class_type": "VAEEncode", "_meta": {"title": "VAE编码"}}, "34": {"inputs": {"samples": ["32", 0], "vae": ["1", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE解码"}}, "36": {"inputs": {"text": "fcsks fxhks fhyks,girl baby,bust portrait,wear dress,white background", "clip": ["1", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码"}}, "37": {"inputs": {"text": "Blurred, messy, turbid, lowest resolution, worst quality, least details, extremely poor image quality, distorted structure, messy structure, broken image,Fat, excess fat", "clip": ["1", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码"}}, "38": {"inputs": {"method": "human_parts (deeplabv3p)", "confidence": 0.4, "crop_multi": 0, "mask_components": "1", "image": ["45", 0]}, "class_type": "easy humanSegmentation", "_meta": {"title": "人体Segmentation"}}, "39": {"inputs": {"min": 0, "max": 0.06000000000000001, "mask": ["41", 0]}, "class_type": "YCRemapMaskRange", "_meta": {"title": "Remap Mask Range (YC)"}}, "40": {"inputs": {"keep_region": "largest", "fill_holes": "enable", "smooth_edges": "enable", "smoothing_kernel_size": 3, "output_mode": "convex_hull", "expand_mask": 30, "blur_amount": 50, "mask": ["38", 1]}, "class_type": "IrregularToEllipseMask", "_meta": {"title": "Irregular To EllipseMask"}}, "41": {"inputs": {"keep_region": "largest", "fill_holes": "enable", "smooth_edges": "enable", "smoothing_kernel_size": 3, "output_mode": "convex_hull", "expand_mask": 0, "blur_amount": 20, "mask": ["38", 1]}, "class_type": "IrregularToEllipseMask", "_meta": {"title": "Irregular To EllipseMask"}}, "42": {"inputs": {"x": 0, "y": 0, "operation": "natural_blend", "opacity": 1, "blend_power": 1, "destination": ["39", 0], "source": ["40", 0]}, "class_type": "YCMaskComposite", "_meta": {"title": "Mask Composite (YC)"}}, "45": {"inputs": {"input_name": "face_image", "default_value": "Myshell_ShellAgent App Builder Chat_250529_214203.jpeg", "description": ""}, "class_type": "ShellAgentPluginInputImage", "_meta": {"title": "Input Image (ShellAgent Plugin)"}}, "46": {"inputs": {"input_name": "user_image", "default_value": "2<PERSON><PERSON><PERSON>.jpeg", "description": ""}, "class_type": "ShellAgentPluginInputImage", "_meta": {"title": "Input Image (ShellAgent Plugin)"}}, "47": {"inputs": {"output_name": "output_image", "filename_prefix": "ComfyUI", "images": ["34", 0]}, "class_type": "ShellAgentPluginSaveImage", "_meta": {"title": "Save Image (ShellAgent Plugin)"}}}, "schemas": {"inputs": {"45": {"title": "face_image", "type": "string", "default": "Myshell_ShellAgent App Builder Chat_250529_214203.jpeg", "description": "", "url_type": "image"}, "46": {"title": "user_image", "type": "string", "default": "2<PERSON><PERSON><PERSON>.jpeg", "description": "", "url_type": "image"}}, "outputs": {"47": {"title": "output_image", "type": "string", "url_type": "image"}}}}}, "comfyui_dependencies": {"comfyui_version": {"name": "ComfyUI", "repo": "https://github.com/comfyanonymous/ComfyUI.git", "commit": "49b732afd54e1871d59fd0bca9e7a3a97e3532ea"}, "custom_nodes": [{"name": "ComfyUI-YCNodes", "repo": "https://github.com/yichengup/ComfyUI-YCNodes.git", "commit": "137fe033bf67f21d80ef8cf1e5ff1106b04d308f"}, {"name": "ComfyUI-HyperLoRA", "repo": "https://github.com/bytedance/ComfyUI-HyperLoRA.git", "commit": "feb11bedcb5f965085dd8e52862dadd9fe456df9"}, {"name": "ComfyUI-Easy-Use", "repo": "https://github.com/yolain/ComfyUI-Easy-Use.git", "commit": "0daf114fe8870aeacfea484aa59e7f9973b91cd5"}, {"name": "ComfyUI-ShellAgent-Plugin", "repo": "https://github.com/myshell-ai/ComfyUI-ShellAgent-Plugin.git", "commit": "d27fafbb003badd19246b05049dd0caca483a2a1"}, {"name": "ComfyUI-Inspire-Pack", "repo": "https://github.com/ltdrdata/ComfyUI-Inspire-Pack.git", "commit": ""}, {"name": "ComfyUI-Advanced-ControlNet", "repo": "https://github.com/Kosinkadink/ComfyUI-Advanced-ControlNet.git", "commit": ""}, {"name": "ComfyUI_smZNodes", "repo": "https://github.com/shiimizu/ComfyUI_smZNodes.git", "commit": ""}, {"name": "ComfyUI_IPAdapter_plus", "repo": "https://github.com/cubiq/ComfyUI_IPAdapter_plus.git", "commit": "9d076a3df0d2763cef5510ec5ab807f6632c39f5"}], "models": {"6a35a7855770ae9820a3c931d4964c3817b6d9e3c6f9c4dabb5b3a94e5643b80": {"filename": "realvisxlV50_v50Bakedvae.safetensors", "save_path": "checkpoints", "urls": ["https://huggingface.co/misri/RealVisXL_V5.0_fp16/resolve/main/RealVisXL_V5.0_fp16.safetensors", "https://huggingface.co/alaa304/V5_realvision/resolve/main/RealVisXL_V5.0_fp16.safetensors", "https://huggingface.co/SG161222/RealVisXL_V5.0/resolve/main/RealVisXL_V5.0_fp16.safetensors", "https://huggingface.co/fal-collab-models/RealVisXL_V5.0_single/resolve/main/RealVisXL_V5.0.safetensors", "https://civitai.com/api/download/models/789646"], "require_recheck": false}}, "files": {"a36805c47000bd0d019cd6ca676c372ff87c104247454b61f00cb19d43d0fc5f": {"filename": "input/Myshell_ShellAgent App Builder Chat_250529_214203.jpeg", "urls": ["https://www.myshellstatic.com/image/chat/embed_obj/202505291639/Myshell_ShellAgent App Builder Chat_250529_214203.jpeg"]}, "a8088153278e36a3b7bffe710d5a5538ece192358a1bfb159b6b122f303e16db": {"filename": "input/2<PERSON><PERSON><PERSON>.jpeg", "urls": ["https://www.myshellstatic.com/image/chat/embed_obj/202505291639/2S<PERSON><PERSON>.jpeg"]}}, "pypi": {"pillow": "10.4.0", "matplotlib": "3.10.0", "easydict": "1.13", "insightface": "0.7.3", "peft": "0.14.0", "lark": "1.2.2", "pydantic": "2.10.6", "imageio-ffmpeg": "0.6.0", "numpy": "1.26.4", "clip_interrogator": "0.6.0", "onnxruntime": "1.20.1", "spandrel": "0.4.1", "aiofiles": "24.1.0", "diffusers": "0.32.2", "torch": "2.4.1+cu124", "timm": "1.0.14", "accelerate": "1.3.0", "transformers": "4.51.3", "pillow_heif": "0.21.0", "sentencepiece": "0.2.0", "scipy": "1.15.1", "brotli": "1.1.0", "opencv-python": "*********"}}, "metadata": {"name": "Return To Infancy", "description": "", "template_id": "", "version": "v0.3.4", "update_time": "Fri May 30 01:23:47 2025"}, "reactflow": {"config": {"fieldsModeMap": {}, "refs": {"state1": {"blocks.gpt_image_11.inputs.prompt": {"currentMode": "raw"}, "blocks.gpt_image_11.inputs.image_url": {"currentMode": "ref", "ref": "state1.inputs.upload"}, "blocks.comfy_ui_widget1.inputs.45": {"currentMode": "raw"}, "blocks.comfy_ui_widget1.inputs.46": {"currentMode": "ref", "ref": "state1.inputs.upload"}, "render.image": {"currentMode": "ref", "ref": "state1.blocks.comfy_ui_widget1.output_image"}, "outputs.__context__upload__.value": {"currentMode": "ref", "ref": "state1.inputs.upload"}}, "state1_copy1": {"blocks.gpt_image_11.inputs.prompt": {"currentMode": "raw"}, "blocks.gpt_image_11.inputs.image_url": {"currentMode": "ref", "ref": "state1_copy1.inputs"}, "blocks.comfy_ui_widget1.inputs.45": {"currentMode": "raw"}, "blocks.comfy_ui_widget1.inputs.46": {"currentMode": "ref", "ref": "state1_copy1.inputs.upload"}, "render.image": {"currentMode": "ref", "ref": "state1_copy1.blocks"}}}, "validateIssues": {"intro": [{"message": "Keep image file size under 1MB.", "type": "warning", "path": ["render.image"], "code": "custom", "key": "intro-render.image"}]}, "currentVersion": "v0.3.4"}, "reactflow": {"nodes": [{"width": 380, "height": 149, "id": "@@@start", "position": {"x": 18.00341796875, "y": 290.0034713745117}, "type": "start", "selectable": true, "focusable": true, "draggable": true, "data": {"type": "start", "id": "@@@start", "display_name": "Start"}, "selected": false, "positionAbsolute": {"x": 18.00341796875, "y": 290.0034713745117}, "dragging": false}, {"id": "intro", "position": {"x": 446.99906089583646, "y": 279.0460739375752}, "type": "state", "selectable": true, "focusable": true, "draggable": true, "data": {"type": "state", "id": "intro", "name": "Intro", "display_name": "Intro"}, "width": 220, "height": 193, "selected": true, "positionAbsolute": {"x": 446.99906089583646, "y": 279.0460739375752}, "dragging": false}, {"id": "form1", "position": {"x": 695.7135063974887, "y": 282.51029749622825}, "type": "form", "selectable": true, "focusable": true, "draggable": true, "data": {"type": "form", "id": "form1", "name": "Form", "display_name": "Form#1"}, "width": 220, "height": 77, "selected": false, "positionAbsolute": {"x": 695.7135063974887, "y": 282.51029749622825}, "dragging": false}, {"id": "state1", "position": {"x": 948.7390608883477, "y": 140.94456340717102}, "type": "state", "selectable": true, "focusable": true, "draggable": true, "data": {"type": "state", "id": "state1", "name": "State", "display_name": "State#1"}, "width": 380, "height": 391, "selected": false, "positionAbsolute": {"x": 948.7390608883477, "y": 140.94456340717102}, "dragging": false}, {"id": "state1_copy1", "position": {"x": 1528.1673834447552, "y": 141.91071091044654}, "type": "state", "selectable": true, "focusable": true, "draggable": true, "data": {"type": "state", "id": "state1_copy1", "name": "State", "display_name": "State#1 Copy#1"}, "width": 380, "height": 427, "selected": false, "positionAbsolute": {"x": 1528.1673834447552, "y": 141.91071091044654}, "dragging": false}, {"id": "form1_copy1", "position": {"x": 1333.0153496317232, "y": 11.622781337176221}, "type": "form", "selectable": true, "focusable": true, "draggable": true, "data": {"type": "form", "id": "form1_copy1", "name": "Form", "display_name": "Form#1 Copy#1"}, "width": 220, "height": 77, "selected": false, "positionAbsolute": {"x": 1333.0153496317232, "y": 11.622781337176221}, "dragging": false}], "edges": [{"type": "default_edge", "style": {"strokeWidth": 2, "stroke": "#d1d5db"}, "markerEnd": {"color": "var(--border-bolder)", "height": 12, "strokeWidth": 2, "type": "arrow", "width": 12}, "source": "@@@start", "sourceHandle": "@@@start", "target": "intro", "targetHandle": "intro", "animated": false, "id": "reactflow__edge-@@@start@@@start-introintro"}, {"type": "custom_edge", "style": {"stroke": "var(--flow-color-03)"}, "markerEnd": {"color": "var(--border-bolder)", "height": 12, "strokeWidth": 2, "type": "arrow", "width": 12}, "source": "intro", "sourceHandle": "custom_button-source-handle-d7d9b3d7-6266-43f5-a8cd-62d5541dc54b", "target": "form1", "targetHandle": "form1", "data": {"name": "Untitled", "key": "untitled_transition_1", "event_key": "d7d9b3d7-6266-43f5-a8cd-62d5541dc54b", "type": "STATE", "source": "intro", "target": "form1", "target_inputs": {}}, "id": "reactflow__edge-introcustom_button-source-handle-d7d9b3d7-6266-43f5-a8cd-62d5541dc54b-form1form1"}, {"type": "custom_edge", "style": {"stroke": "var(--flow-color-02)", "strokeWidth": 2}, "markerEnd": {"color": "var(--border-bolder)", "height": 12, "strokeWidth": 2, "type": "arrow", "width": 12}, "source": "form1", "sourceHandle": "custom_form1", "target": "state1", "targetHandle": "state1", "data": {"name": "Untitled", "key": "untitled_transition_2", "type": "ALWAYS", "source": "form1", "target": "state1", "target_inputs": {}}, "id": "reactflow__edge-form1custom_form1-state1state1"}, {"type": "custom_edge", "style": {"stroke": "var(--flow-color-03)"}, "markerEnd": {"color": "var(--border-bolder)", "height": 12, "strokeWidth": 2, "type": "arrow", "width": 12}, "source": "state1", "sourceHandle": "custom_button-source-handle-21e99a5b-7b57-480d-8a46-4dc5b59ccf21", "target": "form1", "targetHandle": "form1", "data": {"name": "Untitled", "key": "untitled_transition_3", "event_key": "21e99a5b-7b57-480d-8a46-4dc5b59ccf21", "type": "STATE", "source": "state1", "target": "form1", "target_inputs": {}}, "id": "reactflow__edge-state1custom_button-source-handle-21e99a5b-7b57-480d-8a46-4dc5b59ccf21-form1form1"}, {"type": "custom_edge", "style": {"stroke": "var(--flow-color-04)"}, "markerEnd": {"color": "var(--border-bolder)", "height": 12, "strokeWidth": 2, "type": "arrow", "width": 12}, "source": "state1", "sourceHandle": "custom_button-source-handle-3cecc426-ff29-44d1-9dbf-3fae8881c645", "target": "form1_copy1", "targetHandle": "form1_copy1", "data": {"name": "Untitled", "key": "untitled_transition_4", "event_key": "3cecc426-ff29-44d1-9dbf-3fae8881c645", "type": "STATE", "source": "state1", "target": "form1_copy1", "target_inputs": {}}, "id": "reactflow__edge-state1custom_button-source-handle-3cecc426-ff29-44d1-9dbf-3fae8881c645-form1_copy1form1_copy1"}, {"type": "custom_edge", "style": {"stroke": "var(--flow-color-02)", "strokeWidth": 2}, "markerEnd": {"color": "var(--border-bolder)", "height": 12, "strokeWidth": 2, "type": "arrow", "width": 12}, "source": "form1_copy1", "sourceHandle": "custom_form1_copy1", "target": "state1_copy1", "targetHandle": "state1_copy1", "data": {"name": "Untitled", "key": "untitled_transition_5", "type": "ALWAYS", "source": "form1_copy1", "target": "state1_copy1", "target_inputs": {}}, "id": "reactflow__edge-form1_copy1custom_form1_copy1-state1_copy1state1_copy1"}, {"type": "custom_edge", "style": {"stroke": "var(--flow-color-03)"}, "markerEnd": {"color": "var(--border-bolder)", "height": 12, "strokeWidth": 2, "type": "arrow", "width": 12}, "source": "state1_copy1", "sourceHandle": "custom_button-source-handle-c71ad3c8-5b55-4fee-89db-55c3c04bfec9", "target": "form1", "targetHandle": "form1", "data": {"name": "Untitled", "key": "untitled_transition_6", "event_key": "c71ad3c8-5b55-4fee-89db-55c3c04bfec9", "type": "STATE", "source": "state1_copy1", "target": "form1", "target_inputs": {}}, "id": "reactflow__edge-state1_copy1custom_button-source-handle-c71ad3c8-5b55-4fee-89db-55c3c04bfec9-form1form1"}, {"type": "custom_edge", "style": {"stroke": "var(--flow-color-04)"}, "markerEnd": {"color": "var(--border-bolder)", "height": 12, "strokeWidth": 2, "type": "arrow", "width": 12}, "source": "state1_copy1", "sourceHandle": "custom_button-source-handle-be3c5d87-d002-4a0f-b553-8232a7fac8b0", "target": "form1_copy1", "targetHandle": "form1_copy1", "data": {"name": "Untitled", "key": "untitled_transition_7", "event_key": "be3c5d87-d002-4a0f-b553-8232a7fac8b0", "type": "STATE", "source": "state1_copy1", "target": "form1_copy1", "target_inputs": {}}, "id": "reactflow__edge-state1_copy1custom_button-source-handle-be3c5d87-d002-4a0f-b553-8232a7fac8b0-form1_copy1form1_copy1"}, {"type": "custom_edge", "style": {"stroke": "var(--flow-color-05)"}, "markerEnd": {"color": "var(--border-bolder)", "height": 12, "strokeWidth": 2, "type": "arrow", "width": 12}, "source": "state1_copy1", "sourceHandle": "custom_button-source-handle-70bb2814-1f3f-4139-b132-bd5615142af2", "target": "intro", "targetHandle": "intro", "data": {"name": "Untitled", "key": "untitled_transition_8", "event_key": "70bb2814-1f3f-4139-b132-bd5615142af2", "type": "STATE", "source": "state1_copy1", "target": "intro", "target_inputs": {}}, "id": "reactflow__edge-state1_copy1custom_button-source-handle-70bb2814-1f3f-4139-b132-bd5615142af2-introintro"}], "viewport": {"x": -6.131950968172191, "y": 282.12514533867454, "zoom": 0.7192963653407479}}}, "dependencies": {"shellagent_version": {"repo": "https://github.com/myshell-ai/ShellAgentInternal.git", "version": "v0.3.4"}, "models": {}, "custom_widgets": [], "used_func": []}, "envs": {}}, "success": true, "message": ""}