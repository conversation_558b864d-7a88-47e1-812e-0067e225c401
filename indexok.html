<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卡通换脸工具</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .upload-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .preview-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .preview-container {
            width: 30%;
            text-align: center;
        }
        .preview-image {
            max-width: 100%;
            height: auto;
            margin-top: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .result-section {
            margin-top: 20px;
            text-align: center;
        }
        .result-image {
            max-width: 100%;
            height: auto;
            margin-top: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        .error {
            color: red;
            margin-top: 10px;
            display: none;
        }
        #debug {
            margin-top: 20px;
            padding: 10px;
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            display: none;
            max-height: 300px;
            overflow-y: auto;
        }
        .file-input {
            margin: 10px 0;
        }
        .progress {
            margin-top: 10px;
            display: none;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
        }
        .progress-bar-fill {
            height: 100%;
            background-color: #4CAF50;
            width: 0%;
            transition: width 0.3s ease-in-out;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>卡通换脸工具</h1>
        
        <div class="upload-section">
            <h3>1. 上传输入图片</h3>
            <input type="file" id="inputImage" accept="image/*" class="file-input">
            <div class="preview-container">
                <img id="inputPreview" class="preview-image" style="display: none;">
            </div>
            <div id="inputProgress" class="progress">
                <div class="progress-bar">
                    <div class="progress-bar-fill"></div>
                </div>
            </div>
        </div>

        <div class="upload-section">
            <h3>2. 上传遮罩图片</h3>
            <input type="file" id="maskImage" accept="image/*" class="file-input">
            <div class="preview-container">
                <img id="maskPreview" class="preview-image" style="display: none;">
            </div>
            <div id="maskProgress" class="progress">
                <div class="progress-bar">
                    <div class="progress-bar-fill"></div>
                </div>
            </div>
        </div>

        <div class="upload-section">
            <h3>3. 上传人脸图片</h3>
            <input type="file" id="faceImage" accept="image/*" class="file-input">
            <div class="preview-container">
                <img id="facePreview" class="preview-image" style="display: none;">
            </div>
            <div id="faceProgress" class="progress">
                <div class="progress-bar">
                    <div class="progress-bar-fill"></div>
                </div>
            </div>
        </div>

        <button id="generateBtn" disabled>生成图片</button>
        <button id="stopBtn" style="display: none; background-color: #f44336;">停止生成</button>
        <button id="toggleDebug" onclick="toggleDebug()">显示调试信息</button>

        <div class="loading" id="loading">
            <p>正在生成中，请稍候...</p>
        </div>

        <div class="error" id="error"></div>

        <div class="result-section">
            <h3>生成结果</h3>
            <img id="resultImage" class="result-image" style="display: none;">
        </div>

        <div id="debug"></div>
    </div>

    <script>
        const API_KEY = '287c8d5f4705404dae0b3be0e095ca96';
        const WORKFLOW_ID = '1864252288238829569';
        const BASE_URL = 'https://www.runninghub.cn';
        const UPLOAD_URL = `${BASE_URL}/task/openapi/upload`;
        const CREATE_URL = `${BASE_URL}/task/openapi/create`;
        const QUERY_URL = `${BASE_URL}/task/openapi/outputs`;
        const STATUS_URL = `${BASE_URL}/uc/openapi/accountStatus`;

        const HEADERS = {
            "Accept": "*/*",
            "Host": "www.runninghub.cn",
            "Connection": "keep-alive"
        };

        let inputFileUrl, maskFileUrl, faceFileUrl;
        const debugDiv = document.getElementById('debug');

        function log(message) {
            console.log(message);
            debugDiv.textContent += new Date().toISOString() + ': ' + JSON.stringify(message) + '\n';
            debugDiv.scrollTop = debugDiv.scrollHeight;
        }

        function toggleDebug() {
            debugDiv.style.display = debugDiv.style.display === 'none' ? 'block' : 'none';
        }

        // 处理文件上传
        async function handleFileUpload(file, previewId, progressId) {
            const preview = document.getElementById(previewId);
            const progress = document.getElementById(progressId);
            const progressBar = progress.querySelector('.progress-bar-fill');

            if (!file) {
                return null;
            }

            // 预览图片
            const reader = new FileReader();
            reader.onload = (e) => {
                preview.src = e.target.result;
                preview.style.display = 'block';
            };
            reader.readAsDataURL(file);

            // 上传文件
            const formData = new FormData();
            formData.append('apiKey', API_KEY);
            formData.append('file', file);
            formData.append('fileType', 'image');

            progress.style.display = 'block';
            
            try {
                const response = await fetch(UPLOAD_URL, {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();
                log('上传响应: ' + JSON.stringify(data));

                if (data.code === 0) {
                    progressBar.style.width = '100%';
                    return data.data.fileName;
                } else {
                    throw new Error(data.msg || '上传失败');
                }
            } catch (err) {
                log('上传错误: ' + err.message);
                throw err;
            } finally {
                progress.style.display = 'none';
            }
        }

        // 为文件输入添加事件监听器
        document.getElementById('inputImage').addEventListener('change', async function(e) {
            try {
                inputFileUrl = await handleFileUpload(e.target.files[0], 'inputPreview', 'inputProgress');
                checkGenerateButton();
            } catch (err) {
                document.getElementById('error').textContent = '上传输入图片失败：' + err.message;
                document.getElementById('error').style.display = 'block';
            }
        });

        document.getElementById('maskImage').addEventListener('change', async function(e) {
            try {
                maskFileUrl = await handleFileUpload(e.target.files[0], 'maskPreview', 'maskProgress');
                checkGenerateButton();
            } catch (err) {
                document.getElementById('error').textContent = '上传遮罩图片失败：' + err.message;
                document.getElementById('error').style.display = 'block';
            }
        });

        document.getElementById('faceImage').addEventListener('change', async function(e) {
            try {
                faceFileUrl = await handleFileUpload(e.target.files[0], 'facePreview', 'faceProgress');
                checkGenerateButton();
            } catch (err) {
                document.getElementById('error').textContent = '上传人脸图片失败：' + err.message;
                document.getElementById('error').style.display = 'block';
            }
        });

        // 检查是否可以启用生成按钮
        function checkGenerateButton() {
            const generateBtn = document.getElementById('generateBtn');
            generateBtn.disabled = !(inputFileUrl && maskFileUrl && faceFileUrl);
        }

        // 检查账户状态
        async function checkAccountStatus() {
            try {
                const response = await fetch(STATUS_URL, {
                    method: 'POST',
                    headers: {
                        ...HEADERS,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        apikey: API_KEY
                    })
                });

                const data = await response.json();
                log('账户状态响应: ' + JSON.stringify(data));
                
                if (data.code === 0) {
                    return data.data.currentTaskCounts;
                }
                throw new Error(data.msg || '检查账户状态失败');
            } catch (err) {
                log('检查账户状态错误: ' + err.message);
                throw err;
            }
        }

        let isGenerating = false;
        let shouldStop = false;

        // 生成图片
        document.getElementById('generateBtn').addEventListener('click', async function() {
            const loading = document.getElementById('loading');
            const error = document.getElementById('error');
            const resultImage = document.getElementById('resultImage');
            const stopBtn = document.getElementById('stopBtn');
            const generateBtn = document.getElementById('generateBtn');

            loading.style.display = 'block';
            error.style.display = 'none';
            resultImage.style.display = 'none';
            stopBtn.style.display = 'block';
            generateBtn.style.display = 'none';
            isGenerating = true;
            shouldStop = false;

            try {
                // 检查账户状态
                log('检查账户状态...');
                const status = await checkAccountStatus();
                if (status !== "0" && !shouldStop) {
                    let retries = 0;
                    while (status !== "0" && retries < 20 && !shouldStop) {
                        log(`当前队列任务数: ${status}，等待中... (重试次数: ${retries})`);
                        await new Promise(resolve => setTimeout(resolve, 5000));
                        retries++;
                    }
                    if (status !== "0") {
                        throw new Error('任务队列繁忙，请稍后重试');
                    }
                }

                if (shouldStop) {
                    throw new Error('用户取消了操作');
                }

                log('开始处理图片...');
                
                // 构建请求数据
                const requestData = {
                    workflowId: WORKFLOW_ID,
                    apiKey: API_KEY,
                    nodeInfoList: [
                        {
                            nodeId: "238",
                            fieldName: "image",
                            fieldValue: inputFileUrl
                        },
                        {
                            nodeId: "239",
                            fieldName: "image",
                            fieldValue: maskFileUrl
                        },
                        {
                            nodeId: "240",
                            fieldName: "image",
                            fieldValue: faceFileUrl
                        }
                    ]
                };

                log('准备发送请求...');
                log('请求数据: ' + JSON.stringify(requestData));

                const response = await fetch(CREATE_URL, {
                    method: 'POST',
                    headers: {
                        ...HEADERS,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                log('收到响应状态: ' + response.status);
                const data = await response.json();
                log('响应数据: ' + JSON.stringify(data));

                if (data.code === 0 && data.data && data.data.taskId) {
                    const taskId = data.data.taskId;
                    log('获取到taskId: ' + taskId);
                    await pollTaskStatus(taskId);
                } else {
                    throw new Error(data.msg || '创建任务失败');
                }
            } catch (err) {
                log('发生错误: ' + err.message);
                error.textContent = '错误：' + err.message;
                error.style.display = 'block';
            } finally {
                loading.style.display = 'none';
                stopBtn.style.display = 'none';
                generateBtn.style.display = 'block';
                isGenerating = false;
                shouldStop = false;
            }
        });

        // 停止按钮事件监听器
        document.getElementById('stopBtn').addEventListener('click', function() {
            if (isGenerating) {
                shouldStop = true;
                log('用户请求停止操作');
                document.getElementById('loading').innerHTML = '<p>正在停止操作...</p>';
            }
        });

        // 轮询任务状态
        async function pollTaskStatus(taskId) {
            const loading = document.getElementById('loading');
            const error = document.getElementById('error');
            const resultImage = document.getElementById('resultImage');

            const POLL_INTERVAL = 5000; // 5秒
            const MAX_ATTEMPTS = 120; // 10分钟
            let attempts = 0;

            const checkStatus = async () => {
                if (shouldStop) {
                    log('操作已被用户停止');
                    return true;
                }

                try {
                    log('查询任务状态: ' + taskId + ' 第' + (attempts + 1) + '次');
                    const response = await fetch(QUERY_URL, {
                        method: 'POST',
                        headers: {
                            ...HEADERS,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            taskId: taskId,
                            apiKey: API_KEY
                        })
                    });

                    log('状态查询响应: ' + response.status);
                    const data = await response.json();
                    log('状态数据: ' + JSON.stringify(data));

                    if (data.msg === "success") {
                        const fileUrl = data.data[0].fileUrl;
                        log('获取到结果URL: ' + fileUrl);
                        resultImage.src = fileUrl;
                        resultImage.style.display = 'block';
                        loading.style.display = 'none';
                        return true;
                    }

                    if (++attempts >= MAX_ATTEMPTS) {
                        log('达到最大尝试次数');
                        error.textContent = '错误：任务超时';
                        error.style.display = 'block';
                        loading.style.display = 'none';
                        return true;
                    }

                    loading.innerHTML = '<p>正在生成中，请稍候...（' + attempts + '/' + MAX_ATTEMPTS + '）</p>';
                    return false;
                } catch (err) {
                    log('查询状态错误: ' + err.message);
                    error.textContent = '错误：' + err.message;
                    error.style.display = 'block';
                    loading.style.display = 'none';
                    return true;
                }
            };

            while (!(await checkStatus())) {
                if (shouldStop) {
                    break;
                }
                await new Promise(resolve => setTimeout(resolve, POLL_INTERVAL));
            }
        }
    </script>
</body>
</html> 