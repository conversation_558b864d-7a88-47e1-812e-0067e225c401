# Output path for training runs. Each training run makes a new directory in here.
output_dir = '/root/diffusion-pipe/output'

# Dataset config file.
dataset = 'examples/dataset.toml'
# You can have separate eval datasets. Give them a name for Tensorboard metrics.
# eval_datasets = [
#     {name = 'something', config = 'path/to/eval_dataset.toml'},
# ]

# training settings

# 设置为较高的值，可以根据实际需要调整
epochs = 50
# 单个GPU上的批量大小
micro_batch_size_per_gpu = 1
# 模型并行度，模型被分割到多少个GPU上
pipeline_stages = 2
# 每个训练步骤中发送到pipeline的微批次数量
# 如果pipeline_stages > 1，更高的GAS意味着由于更小的pipeline气泡（GPU不重叠计算的地方）而获得更好的GPU利用率
gradient_accumulation_steps = 2
# 梯度裁剪
gradient_clipping = 1.0
# 学习率预热
warmup_steps = 100

# eval settings

eval_every_n_epochs = 1
eval_before_first_step = true
# 可能希望为评估设置较低的值，以便减少丢弃的图像/视频（评估数据集大小通常比训练集小得多）
# 每个图像/视频的大小桶被向下舍入到全局批量大小的最接近倍数，因此更高的全局批量大小意味着
# 更多丢弃的图像。对于训练通常不重要，但评估集要小得多，所以可能会有影响。
eval_micro_batch_size_per_gpu = 1
eval_gradient_accumulation_steps = 1
# 增加数据重复次数的另一种方式 - 添加自定义参数
repeat_data_iterator = true

# misc settings

# 如果数据集较小，可能希望将此设置得更高一些，这样就不会最终得到大量保存的模型
save_every_n_epochs = 5
# 可以每n个epoch或每n分钟检查点训练状态。只设置其中一个。可以使用--resume_from_checkpoint标志从检查点恢复
#checkpoint_every_n_epochs = 1
checkpoint_every_n_minutes = 60
# 使用 'unsloth' 激活检查点可以大幅降低显存占用
activation_checkpointing = 'unsloth'
# 控制Deepspeed如何决定如何在GPU之间划分层。可能不需要更改此项
partition_method = 'parameters'
# 保存LoRA或模型的dtype，如果与训练dtype不同
save_dtype = 'bfloat16'
# 缓存潜在变量和文本嵌入的批量大小。增加可能导致缓存阶段的GPU利用率更高，但使用更多内存
caching_batch_size = 1
# Deepspeed向控制台日志的频率
steps_per_print = 1
# 如何从单个输入视频文件中提取用于训练的视频剪辑
# 视频文件首先被分配到一个配置的帧桶中，但随后我们必须提取一个或多个恰好具有该桶正确帧数的剪辑
# single_beginning: 从视频开始的一个剪辑
# single_middle: 从视频中间的一个剪辑（均等地切掉开始和结束）
# multiple_overlapping: 提取覆盖视频完整范围的最小剪辑数。它们可能会有一些重叠
# 默认是single_middle
video_clip_mode = 'single_beginning'
# Wan2.1的帧率设置
framerate = 16

# 添加显存优化参数，对于大模型训练至关重要
# blocks_to_swap = 38

[model]
type = 'wan'
# 指向Wan2.1模型文件的路径，需要包含模型文件、VAE和T5编码器
ckpt_path = '/root/diffusion-pipe/Wan2_1models/Wan2.1-I2V-14B-480P-FP16'
# 基本dtype用于所有模型，Wan2.1默认使用bfloat16
dtype = 'bfloat16'
# 使用float8精度可以大幅降低显存占用
transformer_dtype = 'float8'
# 如何采样训练的时间步长。可以是logit_normal或uniform
timestep_sample_method = 'logit_normal'
# 可以指定使用哪个Wan2.1变体，可选't2v-1.3B'或't2v-14B'
#model_variant = 't2v-1.3B'
# 可以指定视频分辨率，Wan2.1支持的分辨率有'480*832', '832*480'(1.3B)
# 以及'720*1280', '1280*720', '480*832', '832*480'(14B)
video_resolution = '256*448'

# 对于支持完全微调的模型，只需删除或注释掉[adapter]表以进行FFT
[adapter]
type = 'lora'
# LoRA的秩，较高的值提供更多的表达能力，但需要更多的参数
rank = 12
# 你正在训练的LoRA权重的dtype
dtype = 'bfloat16'
# 你可以从先前训练的lora初始化lora权重
#init_from_existing = '/data/diffusion_pipe_training_runs/something/epoch50'
# 指定要应用LoRA的目标模块，Wan2.1中主要是注意力模块
target_modules = ["WanAttentionBlock"]
# LoRA的dropout率，可以帮助防止过拟合
dropout = 0.25

[optimizer]
# 使用 8 位优化器可以大幅降低优化器状态的内存占用
type = 'AdamW8bitKahan'
# 降低学习率，防止小数据集过拟合
lr = 2e-5
# 增加beta2值，对小数据集有帮助，使梯度更平滑
betas = [0.9, 0.999]
# 增加权重衰减，加强正则化效果
weight_decay = 0.1
stabilize = false 