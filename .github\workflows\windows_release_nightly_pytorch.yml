name: "Windows Release Nightly pytorch"

on:
  workflow_dispatch:
    inputs:
      cu:
        description: 'cuda version'
        required: true
        type: string
        default: "128"

      python_minor:
        description: 'python minor version'
        required: true
        type: string
        default: "13"

      python_patch:
        description: 'python patch version'
        required: true
        type: string
        default: "2"
#  push:
#    branches:
#      - master

jobs:
  build:
    permissions:
        contents: "write"
        packages: "write"
        pull-requests: "read"
    runs-on: windows-latest
    steps:
        - uses: actions/checkout@v4
          with:
            fetch-depth: 30
            persist-credentials: false
        - uses: actions/setup-python@v5
          with:
            python-version: 3.${{ inputs.python_minor }}.${{ inputs.python_patch }}
        - shell: bash
          run: |
            cd ..
            cp -r ComfyUI ComfyUI_copy
            curl https://www.python.org/ftp/python/3.${{ inputs.python_minor }}.${{ inputs.python_patch }}/python-3.${{ inputs.python_minor }}.${{ inputs.python_patch }}-embed-amd64.zip -o python_embeded.zip
            unzip python_embeded.zip -d python_embeded
            cd python_embeded
            echo 'import site' >> ./python3${{ inputs.python_minor }}._pth
            curl https://bootstrap.pypa.io/get-pip.py -o get-pip.py
            ./python.exe get-pip.py
            python -m pip wheel torch torchvision torchaudio --pre --extra-index-url https://download.pytorch.org/whl/nightly/cu${{ inputs.cu }} -r ../ComfyUI/requirements.txt pygit2 -w ../temp_wheel_dir
            ls ../temp_wheel_dir
            ./python.exe -s -m pip install --pre ../temp_wheel_dir/*
            sed -i '1i../ComfyUI' ./python3${{ inputs.python_minor }}._pth
            cd ..

            git clone --depth 1 https://github.com/comfyanonymous/taesd
            cp taesd/*.safetensors ./ComfyUI_copy/models/vae_approx/

            mkdir ComfyUI_windows_portable_nightly_pytorch
            mv python_embeded ComfyUI_windows_portable_nightly_pytorch
            mv ComfyUI_copy ComfyUI_windows_portable_nightly_pytorch/ComfyUI

            cd ComfyUI_windows_portable_nightly_pytorch

            mkdir update
            cp -r ComfyUI/.ci/update_windows/* ./update/
            cp -r ComfyUI/.ci/windows_base_files/* ./
            cp -r ComfyUI/.ci/windows_nightly_base_files/* ./

            echo "call update_comfyui.bat nopause
            ..\python_embeded\python.exe -s -m pip install --upgrade --pre torch torchvision torchaudio --extra-index-url https://download.pytorch.org/whl/nightly/cu${{ inputs.cu }} -r ../ComfyUI/requirements.txt pygit2
            pause" > ./update/update_comfyui_and_python_dependencies.bat
            cd ..

            "C:\Program Files\7-Zip\7z.exe" a -t7z -m0=lzma2 -mx=9 -mfb=128 -md=512m -ms=on -mf=BCJ2 ComfyUI_windows_portable_nightly_pytorch.7z ComfyUI_windows_portable_nightly_pytorch
            mv ComfyUI_windows_portable_nightly_pytorch.7z ComfyUI/ComfyUI_windows_portable_nvidia_or_cpu_nightly_pytorch.7z

            cd ComfyUI_windows_portable_nightly_pytorch
            python_embeded/python.exe -s ComfyUI/main.py --quick-test-for-ci --cpu

            ls

        - name: Upload binaries to release
          uses: svenstaro/upload-release-action@v2
          with:
                repo_token: ${{ secrets.GITHUB_TOKEN }}
                file: ComfyUI_windows_portable_nvidia_or_cpu_nightly_pytorch.7z
                tag: "latest"
                overwrite: true
